<template>
    <el-dialog 
      v-model="dialogVisible" 
      title="执行日志" 
      width="1100px" 
      :before-close="handleClose"
      :show-close='true'
    >
    <div
      ref="terminal"
      v-loading="loading"
      element-loading-text="拼命连接中"
      class="terminal"
    ></div>

  </el-dialog>
</template>

<script lang="ts" setup>
import { computed, ref, watch ,onMounted, onBeforeUnmount } from "vue"

import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { ElMessageBox } from 'element-plus';

import 'xterm/css/xterm.css'


// 父子组件传值
const props = defineProps({
  visible: { type: Boolean, default: false, },
  executeId: { type: String, default: '', }
});

const emit = defineEmits(["update:visible",]);

const dialogVisible = computed({
  get: () => props.visible,
  set: (v) => {
    emit("update:visible", v);
  },
},);


const executeId = computed({
  get: () => props.executeId,
  set: (v) => {},
},);


// const handleClose = () => {
//   dialogVisible.value = false;
// }
const handleClose = (done: () => void) => {
  ElMessageBox.confirm('退出后无法返回查看日志，确定退出？',
    'Warning',
  {
    confirmButtonText: '确认',
    cancelButtonText: '返回',
    type: 'warning',
    center: true,
  })
    .then(() => {
      done()
      dialogVisible.value = false;
    })
    .catch(() => {
      // catch error
    })
}

// xterm websocket 部分
const terminal = ref(null)
const fitAddon = new FitAddon()

let loading = ref(true)
let terminalSocket = ref<WebSocket|any>(null)
let term = ref<Terminal | any>(null)

let remote_token = import.meta.env.VITE_REMOTE_API_TOKEN



// 初始化WS
const initWS = () => {
  if (!terminalSocket.value) {
    createWS()
  }
  if (terminalSocket.value && terminalSocket.value.readyState > 1) {
    terminalSocket.value.close()
    createWS()
  }
}
 
// 创建WS
const createWS = () => {
  // 实例化socket
  terminalSocket.value = new WebSocket(
    `ws://opa.hzxingzai.cn/websocket/ansiblelog/${executeId.value}/?token=${remote_token}`   //HostsJob_20240826154647_1567
  )
  terminalSocket.value.onopen = runRealTerminal //WebSocket 连接已建立
  terminalSocket.value.onerror = errorRealTerminal //WebSocket 连接出错
  terminalSocket.value.onmessage = getMessage //收到服务器消息
  terminalSocket.value.onclose = closeRealTerminal //WebSocket 连接已关闭
}
//WebSocket 建立连接
const runRealTerminal = () => {
  loading.value = false
  console.log('socket连接成功')
  term.value.write(`已连接到服务器，正在读取执行日志\n`)
}
//WebSocket 连接出错
const errorRealTerminal = (ex: any) => {
  let message = ex.message;
  if (!message) message = 'disconnected';
  term.value.write(`\x1b[31m${message}\x1b[m\r\n`);
};
//WebSocket 连接已关闭
const closeRealTerminal = (e: any) => {
  // term.value.write('～本次连接已断开，请重新建立连接\n');
  console.log('本次连接已断开，请重新建立连接')
  loading.value = false;
};

const getMessage = (msg: any) => {
  if (msg.data === 'EOF') {
    term.value.write(`\n\x1b[31m 作业执行完成 \x1b[m\r\n`);
    terminalSocket.value.close()
    return
  }
  term.value.write(msg.data)
  term.value.focus()
}

// 初始化Terminal
const initTerm = () => {
  if (term.value) { return }
  term.value = new Terminal({
    rows: 33, // 行数
    cols: 110,
    convertEol: true, // 启用时，光标将设置为下一行的开头
    scrollback: 1000, // 终端中的回滚量
    disableStdin: true, // 是否应禁用输入
    cursorStyle: 'underline', // 光标样式
    cursorBlink: false, // 光标闪烁
    screenReaderMode: true,
    theme: {
      foreground: '#ffffff', // 字体
      background: '#000000', // 背景色
      cursor: 'help' // 设置光标
    },
  })
  term.value?.open(terminal.value) //挂载dom窗口
  term.value?.clear()
  term.value?.loadAddon(fitAddon) //自适应尺寸
  // 不能初始化的时候fit,需要等terminal准备就绪,可以设置延时操作
  setTimeout(() => {
    fitAddon.fit()
  }, 5)
}

// 移除浏览器刷新监听事件的监听器
onBeforeUnmount(() => {
  terminalSocket.value && terminalSocket.value.close();
});

watch(dialogVisible, (New, Old) => {
  if (dialogVisible.value) {
    setTimeout(() => {
      initTerm()
      initWS()
    }, 100)
  } else { 
    // term.value?.reset()
		term.value?.clear()
  }
})

</script>

<style lang="scss" scoped>
.terminal {
  width: 100%;
  height: calc(100% - 30px);
}
</style>