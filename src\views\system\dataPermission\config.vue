<template>
  <fs-page>
    <template #header>
      <div class="new-title">
        <el-button @click="goBack" :icon="ArrowLeft" class="go-back">返回</el-button>
         <span class="sub-title">数据配置</span>
         <span class="sub-title-role-name">
         <el-tooltip
            effect="dark"
            content="当前数据组名称"
            placement="top-start"
          >
          <el-tag effect="light" type="success">{{roleInfo.roleName}}</el-tag>
         </el-tooltip>
         </span>
         
      </div>
      
    </template>
    
    <template #default>
      
     <div class="custom-scoll-main">
      <el-tooltip
        class="box-item"
        effect="light"
        content="返回顶部"
        placement="top-start"
      >
      <el-backtop target=".custom-scoll-main" :right="40" :bottom="100">
      </el-backtop>
    </el-tooltip>
      <div class="custom-card-content">
        <div>
          <div class="data-group-title">数据概览</div>
        </div>
        <el-row :gutter="20" v-for="(groupModelName, indexGroup) in groupsModelNames" :key="indexGroup">
          <el-col :span="6" v-for="itemStatic in groupModelName" :key="itemStatic.key">
            <el-card shadow="hover">
              <el-statistic :title="itemStatic.label" :value="getRoleRowStatisticValue(itemStatic.key, 'had_role_perm_count')">
                <template #title>
                  <el-link :underline="false" @click="scrollToElement(itemStatic.key)">
                  <div style="display: inline-flex; align-items: center">
                    {{itemStatic.label}}
                  </div>
                </el-link>
                </template>
                <template #suffix>/{{ getRoleRowStatisticValue(itemStatic.key, 'all_role_perm_count') }}
                  </template>
                </el-statistic>
            </el-card>
            
          </el-col>
        </el-row>
      </div>
      <div class="custom-card-content" v-for="modelName in modelNames" :key="modelName.key" :id="modelName.key">
        <div>
          <div class="data-group-title">{{modelName.label}}</div>
          <dataToGroup :role-id="roleInfo.roleId" :role-origin="modelName.value" :show-config="getModelShowConfig(modelName.value)"/>
        </div>
      </div>
    </div>
    </template>
    
    <template #footer>
      <div class="custom-card-footer">
        <el-button @click="goBack" :icon="ArrowLeft" class="go-back" style="float:right; padding-right: 10px; align-items: center;">返回</el-button>
      </div>
    </template>
    
  </fs-page>
  
</template>

<script setup lang="ts" name="dataPermissionConfig">
import { getModelList, getRoleRowStatistics } from '/@/api/models/search'
import { useRouter, useRoute } from "vue-router";
import { ref, onMounted, Ref } from "vue";
import { ArrowLeft } from "@element-plus/icons-vue";
import dataToGroup from "./component/dataToGroup.vue";

const router = useRouter();
const route = useRoute();


// model 数据源选择
interface SelectOption {
  key?: number
  path_key?: string
  label?: string
  value?: string
  disabled?: boolean
}
interface OptionShowConfig {
  [key: string]: SelectOption
}
let modelsShowConfig: OptionShowConfig = {}
let modelNames:Ref = ref<SelectOption>(<SelectOption>[])
const groupsModelNames:Ref = ref<SelectOption[]>(<SelectOption[]>[]);


interface roleInfoType {
  roleId?: number
  roleName?: string
}
const roleInfo: roleInfoType = {
  'roleId': Number(route.query.roleId) || -1,
  'roleName': String(route.query.roleName) || 'Unknown',
};


interface roleModelRowStatical {
  [key: string]: {
    all_role_perm_count: number,
    had_role_perm_count: number,
  }
}
const roleModelRowStaticals:Ref = ref<roleModelRowStatical[]>([]);

const currentStep = ref(0);

const scrollToElement = (key: string) => {
  // 更新当前激活的步骤
  currentStep.value = modelNames.value.findIndex((step: { key: string; }) => step.key === key);

  // 获取目标元素
  const targetElement = document.getElementById(key);

  // 滚动到目标元素
  if (targetElement) {
    targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }
};


const goBack = () => {
  // 返回上一页
  router.go(-1);
}

// 默认你获取的应用程序
const appName = ['resource', 'operatorcmdb']

const getModelData = async () => {
    const apiRes = await getModelList({app_name: appName, resource_interval_enabled: true})
    const response = apiRes.data
    let models = response.models
    modelsShowConfig = response.models_show
    models.forEach((val: any) => {
      modelNames.value.push({
        key: val.key,
        label: val.title,
        value: val.app + '.' + val.key,
        disabled: false,
      })
    })
    // 分组groupMdoels
    getGroupsModelNames();
}


const getRoleRowStatisticsData = async() => {
    const apiRes = await getRoleRowStatistics({role_id: roleInfo.roleId, app_name: appName, resource_interval_enabled: true})
    roleModelRowStaticals.value = apiRes.data
}

// watch(roleModelRowStaticals.value, (newValue, oldValue) => {
//       if (roleModelRowStaticals.value.length <= 0) {
//         getRoleRowStatisticsData();
//       }
//     }, { deep: true, immediate: true }
//   );
const getRoleRowStatisticValue = (key: string, option: string) => {
  const tempValue = roleModelRowStaticals.value?.[key]
  const count = 0
  if (option === 'had_role_perm_count') {
    const count = tempValue?.had_role_perm_count
    if (count !== undefined) {
      return count
    }
  } else {
    const count = tempValue?.all_role_perm_count
    if (count !== undefined) {
      return count
    }
  }
  return count
  
}


const getModelShowConfig = (key: string) => {
  const showConfig = modelsShowConfig?.[key]
  return showConfig
}

// 以下为 错误示例 ,保留当经验
// let sourceModelNames = ref(modelNames)
// console.error('sourceModelNames0001:--->', sourceModelNames)

// if(modelNames.length <=0) {
//   modelNames = [{ "key": "PrivateRoom", "label": "包间信息表", "value": "resource.PrivateRoom", "disabled": false }]
// }
const getGroupsModelNames = () => {
  for (let i =0; i < modelNames.value.length; i+=4){
    groupsModelNames.value.push(modelNames.value.slice(i, i + 4));
  }
}

onMounted(() => {
  getModelData();
  getRoleRowStatisticsData();
})
</script>


<style lang="scss">
.go-back {
  height: 100%;
  background-color: #ffffff;
  border-color: #ffffff;
}
.go-back:hover {
  background-color: #ffffff; /* hover状态下也保持背景色透明 */
  border-color: #ffffff; /* hover状态下也保持边框颜色透明 */
  color: #66b1ff; /* hover状态下可以更改文字颜色 */
}
.new-title {
  border: 8px solid #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, .04), 2px 2px 2px rgba(0, 0, 0, .08);
  background-color: #ffffff;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 20px;
}
.custom-scoll-main {
  height: 100%;
  overflow: auto; 
}
.el-step.is-simple .el-step__title {
  font-size: 10px;
  color: #409eff;
}

.el-row {
  margin-bottom: 20px;
}

.sub-title {
  align-items: center;
  font-weight: bolder;
}

.sub-title-role-name {
  align-items: center;
  padding-left: 2%;
}

.data-group-title {
  font-size: 20px;
  font-weight: bolder;
  padding-bottom: 20px;
}

.custom-card-content {
  border: 8px solid #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: 0px 2px 2px 2px rgba(0, 0, 0, .04), 0px 2px 2px rgba(0, 0, 0, .08);
  background-color: #ffffff;
  margin-top: 20px;
  padding-top: 10px;
}
.custom-card-footer {
  border: 8px solid #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, .08), 2px 2px 2px rgba(0, 0, 0, .08);
  background-color: #ffffff;
  height: 48px;
}

</style>