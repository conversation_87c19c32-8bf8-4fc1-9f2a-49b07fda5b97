<template>
  <tiny-card custom-class="stat-cards" type="text">
      <tiny-chart-bar :options="options" :extend="extChart" :toolbox="toolbox"></tiny-chart-bar>
  </tiny-card>

</template>

<script lang="ts" setup name="ChartBar">
import { ref, watch, PropType } from 'vue';
import { TinyChartBar, TinyCard, } from '@opentiny/vue';

const props = defineProps({
  data: {
    type: Array<any>,
    required: true,
    default() {
      return []
    },
  },
  config: {
    type: Object as PropType<{
      title: string;
      subTitle: string;
      xColumn: string;
      direction: string;
    }>,
    required: true,
    default() {
      return {
        title: '',
    subTitle: '',
    xColumn: '',
    direction: 'horizontal',
      }
    },
  }
});

const extChart = ref({
  title: {
          text: props.config.title, 
          subtext: props.config.subTitle, 
          // sublink: 'subLink', 
          left: 'left',
          top: 'left',
          textStyle: {
            "margin-bottom": 20,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
  // series: [
  //   {
  //     showSymbol: true,
  //     label: {
  //       show: true,

  //     }
  //   }
  // ]
});

const toolbox = ref({
  feature: {
    dataView: {},
    magicType: { type: ['line', 'bar'] },
    saveAsImage: {}
  }
})

let options: any = ref({
  data: props.data,
  label: {
    show: true,
    position: 'right',
    offset: [6, 0],
    formatter: (params: any) => {
            if (!params.data)  return '';
            return `${params.value}`; 
          },
  },
  xAxis: {
    data: props.config.xColumn
  },
  yAxis: {
    minInterval: 1, //设置成1保证坐标轴分割刻度显示成整数。
  },
  direction: props.config.direction // 横向柱状图
})


// Watch for props changes and update chart options 
watch(() => props.data, () => {
  options.value.data = props.data
}, { deep: true });


// Watch for props changes and update chart options 
watch(() => props.config, () => {
  options.value.xAxis.data = props.config.xColumn;
  options.value.direction = props.config.direction;
}, { deep: true });
</script>
<style scoped>
.dashboard-sub-title {
  font-size: 16px;
  margin-bottom: 20px;
}

/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px);
  /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important;
  /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>