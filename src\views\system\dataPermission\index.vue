<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
    </fs-crud>
	</fs-page>
</template>

<script lang="ts" setup name="rowPermission">
import { ref, onMounted } from 'vue';
import { useExpose, useCrud } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';

let dataPermDialogVisible = ref(false);

let roleId = ref();
let roleName = ref('Unknown');

const handleDataPermisson = (row: any) => {
  roleId.value = row.id;
	roleName.value = row.name;
  dataPermDialogVisible.value = true;
};

// crud组件的ref
const crudRef = ref();
// crud 配置的ref
const crudBinding = ref();


const { crudExpose } = useExpose({ crudRef, crudBinding });

// 你的crud配置
const { crudOptions } = createCrudOptions({ crudExpose, handleDataPermisson});

// 初始化crud配置
// @ts-ignore
// eslint-disable-next-line no-unused-vars
const { resetCrudOptions } = useCrud({
  crudExpose,
  crudOptions,
  context: {},
});

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>
