import { AddReq, DelReq, EditReq, CrudExpose, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import * as api from './api';
import { auth } from "/@/utils/authFunction";

//此处为crudOptions配置
// eslint-disable-next-line no-unused-vars
export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
  const pageRequest = async (query: any) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    if (row.id) {
      form.id = row.id;
    }
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('ProjectsModelViewSet:Create'),
          },
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            type: 'text',
            order: 1,
            show: auth('ProjectsModelViewSet:Retrieve')
          },
          edit: {
            type: 'text',
            order: 2,
            show: auth('ProjectsModelViewSet:Update')
          },
          remove: {
            type: 'text',
            order: 4,
            show: auth('ProjectsModelViewSet:Delete')
          },
        },
      },
      columns: {
        name: {
          title: '项目',
          type: 'input',
          search: { show: true },
          column: {
            minWidth: 120,
            sortable: 'custom',
          },
          form: {
            helper: {
              render() {
                return <div style={"color:blue"}>项目名称必填</div>;
              }
            },
            rules: [{ required: true, message: '项目名称必填' }],
            component: {
              placeholder: '请输入项目名称',
            },
          },
        },
        sign: {
          title: '项目标识',
          type: 'input',
          search: { show: true },
          column: {
            minWidth: 120,
            sortable: 'custom',
          },
          form: {
            helper: {
              render() {
                return <div style={"color:blue"}>项目标识必填</div>;
              }
            },
            rules: [{ required: true, message: '项目标识必填' }],
            component: {
              placeholder: '请输入项目标识',
            },
          },
        },
      },
    },
  };
}