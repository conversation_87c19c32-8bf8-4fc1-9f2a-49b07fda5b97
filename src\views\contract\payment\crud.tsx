import * as api from '../../../api/contract/payment';
import {
  UserPageQuery,
  DelReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict,
  FsButton
} from '@fast-crud/fast-crud';
import { h } from 'vue';
import { ElTooltip } from 'element-plus';
import { QuestionFilled } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const exportRequest = async (query: UserPageQuery) => {
		return await api.exportData(query);
	};

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        delRequest,
        exportRequest
      },
      toolbar: {
        buttons: {
          // 刷新按钮
          export:{
            show: false,
          },
          //查询按钮
          search:{
              show: false,
          },
          // 刷新按钮
          refresh:{
            show: false,
          },
          // 紧凑模式
          compact:{
            show: false,
          },
          columns:{
            show: true,
            ...FsButton,
            circle: true,
            buttonProps: {
              plain: true,
            },
            icon: 'setting',
            order:1,  // 列排序号，数字越小越靠前排列。 默认值为1, 当配置0或负数则排到最前面，配置2则排到最后面
          },
        }
      },
      actionbar: {
        buttons: {
          add: {
            show: false,
            plain: false,
          },
          export: {
            show: auth('contract:payment:Export'),
            text: "导出",//按钮文字
            title: "导出",//鼠标停留显示的信息
            click() {
              return exportRequest(crudExpose.getSearchFormData())
            }
          }
        }
      }, 
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            link: true,
            type: 'primary',
            show: false,
          },
          edit: {
            link: true,
            type: 'primary',
            show: false,
          },
          remove: {
            link: true,
            type: 'danger',
            show: auth('contract:payment:Delete'),
          },
          viewLog: {
                                type: 'primary',
                                text: '查看日志',
                                link: true,
                                show: auth('system:auditLog:GetResourceLogs'),
                                click(context) {
                                  router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
                                }
                              },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        // _index: {
        //   title: '序号',
        //   form: { show: false },
        //   column: {
        //     type: 'index',
        //     align: 'center',
        //     width: '70px',
        //     columnSetDisabled: true, //禁止在列设置中选择
        //   },
        // },
        // status: {
        //   title: '回款状态',
        //   search: {
        //     show: true,
        //   },
        //   type: 'dict-select',
        //   dict: dict({
        //     data: dictionary('oamanage:payment:status', undefined)
        //   }),
        //   column: {
        //     minWidth: 90,
        //     fixed: 'left',
        //   },
        //   form: {
        //     component: {
        //       placeholder: '请选择回款状态',
        //     }
        //   }
        // },
        contract_code: {
          title: '合同流程编号',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 100,
            fixed: 'left',
          },
          form: {
            component: {
              placeholder: '请输入合同流程编号',
            }
          }
        },
        contract_name: {
          title: '合同名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 100,
            fixed: 'left',
          },
          form: {
            component: {
              placeholder: '请输入合同名称',
            }
          }
        },
        pre_payment_status: {
          title: '往期账单状态',
          type: 'dict-select',
          dict: dict({
            data: dictionary('oamanage:contract:pre_pay_status', undefined)
          }),
          column: {
            minWidth: 90,
            fixed: 'left',
            renderHeader: () =>
            h('div', { style: 'display: flex; align-items: center;justify-content:center' }, [
              h('span', '往期账单状态'),
              h(
                ElTooltip,
                { content: '若回款中当前账期与逾期账期都为全部回款则显示“全部回款”，否则显示“未回款”', placement: 'top' },
                {
                  default: () =>
                    h(QuestionFilled, {
                      style: 'margin-left: 6px; color: #909399; cursor: pointer;width: 18px; height: 18px;'
                    })
                }
              )
            ])
          },
          search: {
            show: true,
          }, // 开启搜索
          disabled: true // 列表中不允许手动编辑（如需）
        },
        pre_payment_amount: {
          title: '已回款金额(元)',
          key: 'pre_payment_amount', 
          column: { minWidth: 90,}
        },
        pre_payment_time: {
          title: '最近回款时间',
          key: 'pre_payment_time', 
          column: { minWidth: 90,}
        },
        invoice_title: {
          title: '回款方名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 150,
          },
          form: {
            component: {
              placeholder: '请输入回款方名称',
            }
          }
        },
        amount: {
          title: '预回款金额(元)',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请输入总回款金额',
            }
          }
        },
        payment_time: {
          title: '预回款时间',
          search: {
            show: true,
            component: {
              name: 'el-date-picker',
              props: {
                type: 'date',           // 从 month 改为 date 
                placeholder: '选择预回款日期',
                valueFormat: 'YYYY-MM-DD',  // 格式精确到日 
                shortcuts: [            // 可选：添加常用日期快捷选项 
                  { text: '今天', value: new Date() },
                  { text: '一周后', value: () => new Date(Date.now() + 7 * 86400000) },
                  { text: '一月后', value: () => new Date(Date.now()  + 30 * 86400000) },
                  { text: '两月后', value: () => new Date(Date.now()  + 60 * 86400000) },
                  { text: '三月后', value: () => new Date(Date.now()  + 90 * 86400000) },
                ]
              }
            }
          },
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请选择预回款时间',
            },
          }
        },
      },
    },
  };
};
