<template>
  <div>
    <div style="margin-bottom: 12px;">
              <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image :src="projectImg" alt="项目图标" style="width: 24px; height: 16px;"></tiny-image>当前项目</span>
              <tiny-tag type="success" size="medium"> {{ currentSelectProject }} </tiny-tag>
              <span
              style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">过滤按钮点击后，需要点击两次确定方可搜索，以下具有相同的问题。</span>
            </div>
    <tiny-grid ref="selectProjectGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @radio-change="handleRadioChange" header-align="center">
      <tiny-grid-column type="radio" width="40"></tiny-grid-column>
      <tiny-grid-column field="name" title="ID/名称" :filter="nameFilter" :sortable="true">
        <template #default="{row}">
          <div class="id-cell">
              <tiny-link :underline="false" type="primary">{{ row.project_id.slice(0, 8) }}</tiny-link>
              <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.project_id)"></tiny-link>
          </div>
          <p>{{ row.name }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="description" title="描述" align="center">
      </tiny-grid-column>
      <tiny-grid-column field="enabled" title="状态" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.enabled ? '是': '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="node" title="区域" align="center">
      </tiny-grid-column>
      <tiny-grid-column field="sync_time" title="同步时间" align="center" :sortable="true">
        <template #default="{row}">
          {{ formatNow(row.sync_time) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectProjectForm">
import { ref, reactive, toRefs, watch, } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyTag,
  TinyImage,
  TinyInput,
  TinyPager,
} from '@opentiny/vue';
import { GetList } from '/@/api/tenant/project';

import { iconCopy } from '@opentiny/vue-icon';
import { formatNow } from '/@/utils/formatTime';
import { copyText } from '/@/utils/copyText';
import projectImg from '/@/assets/img/project.svg';


const props = defineProps({
  currentSelectProjectId: {
    type: String,
    required: false,
    default: '',
  },
  node: {
    type: String,
    required: false,
    default: '',
  }
});


// 当前选中值
let currentSelectProject = ref<string>('--');
const emit = defineEmits(['update:currentSelectProjectId']);

const TinyIconCopy = iconCopy();

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input, default, base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})


// 初始化请求数据
interface FilterOptions {
  project_id: string;
  enabled: string;
  description: string;
  name: string;
  node: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    description: '',
    project_id: props.currentSelectProjectId,
    enabled: '',
    name: '',
    node: props.node,
  },
});
let tableData = ref<Array<FilterOptions>>([]);

const selectProjectGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectProjectGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;

    // 如果有初始选中的 ID，设置默认选中行
    if (props.currentSelectProjectId) {
      const selectedRow = tableData.value.find(row => row.project_id === props.currentSelectProjectId);
      if (selectedRow) {
        selectProjectGrid.value?.setRadioRow(selectedRow);
        currentSelectProject.value = selectedRow.name;
      }
    }

    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    description: '',
    project_id: props.currentSelectProjectId,
    enabled: '',
    name: '',
    node: props.node,
  };
  // reloadGrid();
}
const handleRadioChange = () => {
  let selectedRow = selectProjectGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectProject.value = `${selectedRow.name}`
    emit('update:currentSelectProjectId', selectedRow.project_id)
  }
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  // if (filters)
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
      filterOptions.value.name = filters.filters.name.value.text;      
    }
  reloadGrid();
}

// 监听 node 变化 
watch(() => props.node,  (newNode, oldNode) => {
  if (newNode && newNode !== oldNode) {
    filterOptions.value.node  = newNode;
    reloadGrid();
  }
});


</script>
<style lang="less" scoped>
.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>