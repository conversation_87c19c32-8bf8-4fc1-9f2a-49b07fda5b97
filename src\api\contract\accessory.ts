import { request } from '/@/utils/service';
import { UserPageQuery, DelReq, InfoReq } from '@fast-crud/fast-crud';
import { Session } from '/@/utils/storage';

export const apiPrefix = '/api/contract/accessory/';

export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}
export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

export function AddObj(files: File[]) {
  const formData = new FormData();
  files.forEach(file => {
      formData.append('project_package', file);
  });

  return request({
      url: apiPrefix + 'upload/',
      method: 'post',
      data: formData,
      headers: {
    'Content-Type': 'multipart/form-data', // 设定请求头为 multipart/form-data
    'Authorization': 'JWT ' + Session.get('token')
      },
  });
}

export function DelObj(id: DelReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

export function GetLink(id:any){
  return request({
      url: apiPrefix + 'link/',
      params: {id},
      method: 'get'
  })
}