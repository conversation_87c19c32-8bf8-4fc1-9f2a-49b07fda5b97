<template>
  <tiny-statistic 
    :value="formattedValue" 
    :title="{ position: 'top' }"
    :suffix="config.unit"  
    :prefix="config.prefix" 
    :class="['statistic-card', percentClass]"
  >
    <template #title>
      <div class="statistic-title">
        <span class="title-text">{{ config.name  }}</span>
        <tiny-tooltip 
          v-if="config.tooltip"   
          :content="config.tooltip"   
          placement="top" 
          effect="light"
        >
          <tiny-icon-help class="tooltip-icon" />
        </tiny-tooltip>
        <span 
          v-if="config.rate  !== undefined" 
          class="percent-badge"
          :class="percentClass"
        >
          {{ formatPercent(config.rate)  }}
        </span>
      </div>
    </template>
    <template v-if="config.extra"  #extra>
      <span class="extra-content">{{ config.extra  }}</span>
    </template>
  </tiny-statistic>
</template>
 
<script lang="ts" setup>
import { computed } from 'vue'
import type { PropType } from 'vue'
import { iconHelp } from '@opentiny/vue-icon'
 
const TinyIconHelp = iconHelp()
 
interface StatisticConfig {
  name: string 
  tooltip?: string 
  unit?: string 
  prefix?: string 
  rate?: number // 0-100的百分比值 
  extra?: string 
  // eslint-disable-next-line no-unused-vars
  formatter?: (value: number | string) => string 
}
 
const props = defineProps({
  data: {
    type: [String, Number],
    required: true,
    default: 0 
  },
  config: {
    type: Object as PropType<StatisticConfig>,
    required: true,
    default: () => ({
      name: '',
      tooltip: ''
    })
  }
})
 
const percentClass = computed(() => {
  if (props.config.rate  === undefined) return ''
  if (props.config.rate  >= 80) return 'high'
  if (props.config.rate  >= 40) return 'medium'
  return 'low'
})
 
const formattedValue = computed(() => {
  if (props.config.formatter)  {
    return props.config.formatter(props.data) 
  }
  return props.data  
})
 
const formatPercent = (percent?: number) => {
  return percent !== undefined ? `${Math.round(percent)}%`  : '0%'
}
</script>
 
<style scoped>
/* 基础样式 */
.statistic-title {
  display: flex;
  align-items: center;
  gap: 6px;
}
 
.title-text {
  font-weight: 600;
  color: #333;
}
 
.tooltip-icon {
  color: #999;
  cursor: pointer;
}
 
.tooltip-icon:hover {
  color: #1890ff;
}
 
.percent-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}
 
/* 低百分比样式 (0-39%) */
.percent-badge.low  {
  background-color: #f6ffed;
  color: #52c41a;
}
.statistic-card.low  :deep(.tiny-statistic__value) {
  color: #52c41a;
}
 
/* 中百分比样式 (40-79%) */
.percent-badge.medium  {
  background-color: #fffbe6;
  color: #faad14;
}
.statistic-card.medium  :deep(.tiny-statistic__value) {
  color: #faad14;
}
 
/* 高百分比样式 (80-100%) */
.percent-badge.high  {
  background-color: #fff2f0;
  color: #f5222d;
  font-weight: 600;
}
.statistic-card.high  :deep(.tiny-statistic__value) {
  color: #f5222d;
}
 
.extra-content {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}
</style>