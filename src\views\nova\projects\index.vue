<template>
	<fs-page class="PageFeatureSearchMulti">
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #cell_url="scope">
				<el-tag size="small">{{ scope.row.url }}</el-tag>
			</template>
		</fs-crud>
		
	</fs-page>
</template>

<script lang="ts" setup name="novaProject">
import { onMounted} from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import createCrudOptions  from './crud';

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });  


// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
});

</script>