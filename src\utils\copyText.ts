import { Modal as TinyModal } from "@opentiny/vue";

// 复制ID功能
export const copyText = async (text:string) => {
  // 检查 navigator.clipboard 是否存在
  if (navigator.clipboard) {
    try {
      // 请求剪贴板权限
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'clipboard-write' } as unknown as PermissionDescriptor);
        if (permission.state !== 'granted') {
          TinyModal.message(
            {
              message: '未获取到剪贴板权限',
              status: 'error',
            }
          )
        }
      }

      // 写入剪贴板
      await navigator.clipboard.writeText(text);
      TinyModal.message({
        message: '已复制到剪贴板',
        status: 'success',
      });
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      TinyModal.message({
        message: '当前浏览器不支持，复制失败，请手动复制',
        status: 'error',
      });
    }
  } else {
    // 使用 document.execCommand 作为备用方法
    try {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
      TinyModal.message({
        message: '已复制到剪贴板',
        status: 'success',
      });
    } catch (error) {
      TinyModal.message({
        message: '当前浏览器不支持，复制失败，请手动复制',
        status: 'error',
      });
    }
  }
};