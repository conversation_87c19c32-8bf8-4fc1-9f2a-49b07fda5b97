<template>
  <div style="display: inline-block">
    <div style="text-align: center">
      <el-dialog v-model="rowPermDialogVisible" width="80%" :before-close="handleDialogClose">
            <el-form ref="rowTargetRoleFormRef" :model="rowPermissionData" :rules="rowTargetRoleRules" label-width="auto">
              <el-form-item label="">
                <el-tag type="danger" size="large"> 仅为预览页面</el-tag>
              </el-form-item>
              <el-form-item label="当前用户">
                <el-tag type="primary" size="large"> {{ props.userName }}</el-tag>
              </el-form-item>
              <el-form-item label="数据源" prop="row_origin">
                <el-select
                  v-model="rowPermissionData.row_origin"
                  placeholder="请选择数据来源"
                  size="large"
                  filterable
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="item in sourceModelNames"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="数据授权" width="100%" prop="row_ids" v-if="rowPermissionData.row_origin">
                <el-transfer
            v-model="rowPermissionData.row_ids"
            style="text-align: left; display: inline-block"
            filterable
            :titles="['未授权数据', '已授权数据']"
            :button-texts="['取消', '授权']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}',
            }"
            :data="sourceData"
          >
            <template #default="{ option }">
              <span>{{ option.label }}</span>
            </template>
          </el-transfer>
              </el-form-item>
            </el-form>
          
            
          <div class="footer-button">
            <el-button type="primary" @click="handleDialogClose">关闭</el-button>
          </div>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from 'vue'
import { GetModelRowList, getModelList } from '/@/api/models/search';
// import { GetModelRowList, getModelList, AddRowTargetRole, revokeRoleRowIds } from '/@/api/models/search';
import { getUserRowPermission } from '/@/views/system/api/user';
import { FormInstance, FormRules } from 'element-plus';
// import { FormInstance, FormRules, TransferKey, TransferDirection } from 'element-plus';

// import { successMessage } from '/@/utils/message';


interface Option {
  key: number
  label: string
  disabled: boolean
}

const props = defineProps({
  userId: {
    type: Number,
    default: -1
  },
  userName: {
    type: String,
    default: 'Unknown'
  },
  rowPermDialogVisible: {
    type: Boolean,
    default: false
  }
})

// eslint-disable-next-line vue/no-dupe-keys
let rowPermDialogVisible = ref(false)
watch(
    () => props.rowPermDialogVisible,
    (val) => {
      rowPermDialogVisible.value = val;
    }
);
const emit = defineEmits(['update:rowPermDialogVisible'])
const handleDialogClose = () => {
  emit('update:rowPermDialogVisible', false);
}

const rowTargetRoleFormRef = ref<FormInstance>()


// Modles Show Config
let modelsShowConfig: any = {}


// model show
let modelShowConfig: any = {}


let rowPermissionData = reactive({
  // [Important]在此处获取时为第一次初始化的默认参数: -1
  user_id: props.userId,
  row_ids: [],
  row_origin: '',
  is_external: false
})

const sourceData = ref<Option[]>([])

// 监听数据表的选择
watch(() => rowPermissionData.row_origin, () => {
  modelShowConfig = modelsShowConfig[rowPermissionData.row_origin]
  getSourceData();
  // 获取已选择的 资源ID 列表
  getRoleRowIdsData();
}, {immediate: false, deep: true})

const getSourceData = async () => {
  if (rowPermissionData.row_origin) {
    const apiRes = await GetModelRowList({
      row_origin: rowPermissionData.row_origin
    })
    const response = apiRes.data
    sourceData.value = []
    let modelKey = 'id'
    let modelValue = 'name'
    if (modelShowConfig) {
      modelKey = modelShowConfig.key
      modelValue = modelShowConfig.value
    }
    response.forEach((val: any) => {
    sourceData.value.push({
      key: val[modelKey],
      label: val[modelValue],
      disabled: true
      })
    })
  }
}

// model 数据源选择
interface SelectOption {
  key: number
  label: string
  disabled: boolean,
  value: string
}
let modelNames: SelectOption[] = []
const getModelData = async () => {
    const apiRes = await getModelList({app_name: 'resource'})
    const response = apiRes.data
    let models = response.models
    modelsShowConfig = response.models_show
    models.forEach((val: any) => {
      modelNames.push({
        key: val.key,
        label: val.title,
        // @ts-ignore
        value: val.app + '.' + val.key,
        disabled: true,
      })
    })
}

// 获取已授权的资源
const getRoleRowIdsData = async () => {
    const apiRes = await getUserRowPermission({user_id: props.userId})
    const response = apiRes.data
    rowPermissionData.row_ids = response
}


// const handleChange =  (value: TransferKey[], direction: TransferDirection, movedKeys: TransferKey) => {
//   if (direction === 'left') {
//     // 取消授权
//     const revokeData = {
//       'row_ids': movedKeys,
//       'row_origin': rowPermissionData.row_origin,
//       'user_id': props.userId
//     }
//     revokeRoleRowIds(revokeData).then(
//       (res: any) => {
//         if (res.code === 2000) {
//           successMessage(res.msg)
//         }
//       }
//     )
//   } else if (direction === 'right') {
//     // 授权
//     const addData = {
//       'row_ids': movedKeys,
//       'row_origin': rowPermissionData.row_origin,
//       'user_id': props.userId
//     }
//     AddRowTargetRole(addData).then(
//       (res: any) => {
//         if (res.code === 2000) {
//           successMessage(res.msg)
//         }
//       }
//     )
//   }
// };


// 数据授权给用户 rules
const rowTargetRoleRules = reactive<FormRules>({
  row_ids: [{required: true, message: '请选择', trigger: 'blur'}],
  row_origin: [{required: true, message: '请选择', trigger: 'blur'}],
  user_id: [{required: true, message: '请选择', trigger: 'blur'}],
})


const sourceModelNames = ref(modelNames)

onMounted(() => {
  getModelData();
})
</script>

<style scoped>
.transfer-footer {
  margin-left: 15px;
  padding: 6px 5px;
}
.footer-button {
  display: flex;
  justify-content: center;
}
</style>
