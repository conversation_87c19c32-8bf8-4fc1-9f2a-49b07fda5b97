<template>
  <div>
    <div style="margin-bottom: 12px;">
              <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image :src="securityGroupImg" alt="安全组图标" style="width: 24px; height: 16px;"></tiny-image>当前安全组</span>
              <tiny-tag type="success" size="medium"> {{ currentSelectSecurityGroup }} </tiny-tag>
              
            </div>
    <tiny-grid ref="selectSecurityGroupGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @radio-change="handleRadioChange" header-align="center">
      <tiny-grid-column type="radio" width="40"></tiny-grid-column>
      <tiny-grid-column field="sg_id" title="ID/名称" :filter="nameFilter">
        <template #default="{row}">
          <div class="id-cell">
              <tiny-link :underline="false" type="primary">{{ row.sg_id.slice(0, 8) }}</tiny-link>
              <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.sg_id)"></tiny-link>
          </div>
          <p>{{ row.name }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="description" title="描述" align="center">
      </tiny-grid-column>
      
      <tiny-grid-column field="is_shared" title="共享" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.is_shared ? '是': '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="node" title="区域" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="sync_time" title="同步时间" align="center" :sortable="true">
        <template #default="{row}">
          {{ formatNow(row.sync_time) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectSecurityGroupForm">
import { ref, reactive, toRefs, watch } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyTag,
  TinyImage,
  TinyInput,
  TinyPager,
} from '@opentiny/vue';
import { GetList } from '/@/api/tenant/opSecurityGroup';

import { formatNow } from '/@/utils/formatTime';
import { iconCopy } from '@opentiny/vue-icon';
import { copyText } from '/@/utils/copyText';
import securityGroupImg from '/@/assets/img/security-group.svg';


const props = defineProps({
  projectId: {
    type: String,
    required: true,
    default: ''
  },
  currentSelectSecurityGroupId: {
    type: String,
    required: false,
    default: '',
  },
   node: {
    type: String,
    required: false,
    default: '',
  }
});
// 当前选中值
let currentSelectSecurityGroup = ref<string>('--');
const emit = defineEmits(['update:currentSelectSecurityGroupId']);

const TinyIconCopy = iconCopy();

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input, base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})


// 初始化请求数据
interface FilterOptions {
  sg_id: string;
  is_shared: string;
  description: string;
  name: string;
  project_id: string;
  node: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    description: '',
    sg_id: '',
    is_shared: '',
    name: '',
    project_id: props.projectId,
    node: props.node,
  },
});
let tableData = ref<Array<FilterOptions>>([]);

const selectSecurityGroupGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectSecurityGroupGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;

    // 如果有初始选中的 ID，设置默认选中行
    if (props.currentSelectSecurityGroupId) {
      // @ts-ignore
      const selectedRow = tableData.value.find(row => row.name === props.currentSelectSecurityGroupId);
      if (selectedRow) {
        selectSecurityGroupGrid.value?.setRadioRow(selectedRow);
        // @ts-ignore
        currentSelectSecurityGroup.value = selectedRow.name;
      }
    }
    if (total === 1) {
      const selectedRow = tableData.value[0];
      if (selectedRow) {
        selectSecurityGroupGrid.value?.setRadioRow(selectedRow);
        currentSelectSecurityGroup.value = selectedRow.name;
      }
      emit('update:currentSelectSecurityGroupId', selectedRow.sg_id);
    } else if (total === 0) {
      currentSelectSecurityGroup.value = '--';
      emit('update:currentSelectSecurityGroupId', '');
    }

    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    description: '',
    sg_id: '',
    is_shared: '',
    name: '',
    project_id: props.projectId,
    node: props.node,
  };
  // reloadGrid();
}
const handleRadioChange = () => {
  let selectedRow = selectSecurityGroupGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectSecurityGroup.value = `${selectedRow.name}`
    emit('update:currentSelectSecurityGroupId', selectedRow.name)
  }
  
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  // if (filters)
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
      filterOptions.value.name = filters.filters.name.value.text;
    }
  reloadGrid();
}


watch(() => props.projectId, (newProjectId) => {
  if (newProjectId) {
    filterOptions.value.project_id = newProjectId
    reloadGrid();
  }
});

// 监听 node 变化 
watch(() => props.node,  (newNode, oldNode) => {
  if (newNode && newNode !== oldNode) {
    filterOptions.value.node  = newNode;
    reloadGrid();
  }
});

</script>
<style lang="less" scoped>
.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>