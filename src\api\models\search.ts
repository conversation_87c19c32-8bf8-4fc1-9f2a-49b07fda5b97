import { pa } from 'element-plus/es/locale';
import { request } from '/@/utils/service';
import {
  UserPageQuery,
  AddReq,
  // PageQuery,
  EditReq
} from '@fast-crud/fast-crud';
/**
 * 获取所有model
 */
export function getModelList(query: UserPageQuery) {
	return request({
		url: '/api/system/column/get_models/',
		method: 'get',
    params: query
	});
}

// 获取 model -> 行数据
export function GetModelRowList(query: UserPageQuery) {
	return request({
		url: '/api/system/row_permission/get_rows_info/',
		method: 'get',
		params: query,
	});
}


// 获取 model -> 行数据
export function getRoleRowStatistics(query: UserPageQuery) {
	return request({
		url: '/api/system/row_permission/get_role_row_statistics/',
		method: 'get',
		params: query,
	});
}



export function AddRowTargetRole(data: AddReq) {
  return request({
    url: '/api/system/row_permission/add_role_row_ids/',
    method: 'post',
    data: data,
  })
}


export function getRoleRowIds(query: UserPageQuery) {
  return request({
    url: '/api/system/row_permission/get_role_row_ids/',
    method: 'get',
    params: query,
  })
}


export function revokeRoleRowIds(data: EditReq) {
  return request({
    url: '/api/system/row_permission/revoke_role_row_ids/',
    method: 'post',
    data: data,
  })
}

