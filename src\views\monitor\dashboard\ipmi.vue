<template>
  <div class="app-container">
    <iframe
      :src="iframeUrl"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
const iframeUrl = ref("http://**********:3001/d/f291d1c1-cd5a-4dad-b832-f5c6691156cb/ipmie79b91-e68ea7-e4bfa1-e681af?orgId=1&refresh=1m&from=now-1h&to=now&theme=light")

// 根据url query参数，拼接grafana url地址变量
let route = useRoute()
const area = route.query.area
const bmc_ip = route.query.bmc_ip
if (area !== undefined) {
  iframeUrl.value += '&var-DS_PROMETHEUS=' + area
}
if (bmc_ip !== undefined) {
  iframeUrl.value += '&var-instance=' + bmc_ip
}
</script>

<style lang="scss" scoped>
/** 关闭tag标签  */
.app-container {
  /* 50px = navbar = 50px */
  height: calc(100vh - 50px);
}

/** 开启tag标签  */
.hasTagsView {
  .app-container {
    /* 84px = navbar + tags-view = 50px + 34px */
    height: calc(100vh - 84px);
  }
}

iframe {
  width: 100%;
  height: 100%;
}
</style>