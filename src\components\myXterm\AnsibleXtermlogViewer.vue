<!-- 修正后的完整组件代码 -->
<template>
  <div>
      <!-- 连接状态指示器 -->
    <div v-if="connectionStatus === 'connecting'" class="status-indicator">
      {{ statusMessages[connectionStatus] }}
    </div>
    <div ref="terminalContainer" class="xterm-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, PropType,watch, onBeforeUnmount } from 'vue';
import { Terminal, ITerminalOptions } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';

type WebSocketConfig = {
  url: string;
  protocols?: string | string[] | 'singleMsgs';
  isFormatterLogger?: boolean;
  isAnsiColorFixed?: boolean;
  isOnlyPreviewer?: boolean;
};

// 定义 LogsEntry 类型
interface LogEntry {
  timestamp: string;
  level: string;
  msg: string;
}

const props = defineProps({
  wsConfig: {
    type: Object as PropType<WebSocketConfig>,
    required: true,
    default: () => {
      return {
        url: '',
        protocols: 'ws',
        isFormatterLogger: false,
        isAnsiColorFixed: true,
      }
    }
  },
  options: {
    type: Object as PropType<ITerminalOptions>,
    default: () => {
      return {
        theme: { background: '#1a1a1a', foreground: '#ffffff' },
        cursorStyle: 'block',
        fontFamily: 'Consolas, "Andale Mono", "Ubuntu Mono", monospace',
        convertEol: true, // 自动将换行符转换为回车换行; 不然可能会出现无法左对齐的情况
        scrollback: 999999, // 扩大缓冲区容量
        disableStdin: true,   // 禁用输入模式
      }
    }
  },
  message: {
    type: String,
    default: '暂无日志',
  },
});

// 连接状态管理 
const connectionStatus = ref<'connecting' | 'connected' | 'closed'>('connecting');
const statusMessages = {
  connecting: '连接中......',
  connected: '已连接',
  closed: '连接已关闭'
};

const terminalContainer = ref<HTMLElement>();
const term = ref<Terminal>();
const fitAddon = new FitAddon();
let socket: WebSocket | null = null;
// 初始化终端 
const initTerminal = () => {
  if (!terminalContainer.value) return;

  term.value = new Terminal({
    ...props.options
  });

  term.value.loadAddon(fitAddon);
  term.value.open(terminalContainer.value);
};


// 格式化日志条目
const formatLogEntry = (log: LogEntry): string => {
  let formattedMessage = `[${log.timestamp}] ${log.level}: ${log.msg}\n`;
  switch (log.level.toLowerCase()) {
    case 'info':
      formattedMessage = `\x1b[34m${formattedMessage}\x1b[0m`; // 蓝色
      break;
    case 'error':
      formattedMessage = `\x1b[31m${formattedMessage}\x1b[0m`; // 红色
      break;
    case 'warn':
      formattedMessage = `\x1b[33m${formattedMessage}\x1b[0m`; // 黄色
      break;
    case 'debug':
      formattedMessage = `\x1b[36m${formattedMessage}\x1b[0m`; // 青色
      break;
    default:
      formattedMessage = `\x1b[37m${formattedMessage}\x1b[0m`; // 白色
  }
  return formattedMessage;
};

const ansiColorFixedFormat = (msg: string) => {
  return msg
  // 关键字染色
  .replace(/\b(ERROR|Exception|failed|unreachable)/gi, '\x1B[31m$1\x1B[0m') // 红色
  .replace(/\b(WARN|warnning)\b/gi, '\x1B[33m$1\x1B[0m')  // 黄色
  .replace(/\b(INFO|ok|success)\b/gi, '\x1B[32m$1\x1B[0m')  // 绿色
  // IP地址高亮
  .replace(/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/g, '\x1B[34m$1\x1B[0m');
}

// 建立WebSocket连接 
const connectWebSocket = () => {
  socket = new WebSocket(props.wsConfig.url);

  socket.onopen = () => {
    connectionStatus.value = 'connected';
    socket?.send('run'); // 连接成功后发送触发指令
  };

  socket.onmessage = (event) => {
    // 格式化并添加到终端中
    // 若 props.wsConfig.isFormatterLogger 为 true，则格式化并添加到终端中
    // 若 props.wsConfig.isFormatterLogger 为 false，则直接添加到终端中
    let logMsg: string = '';
    if (props.wsConfig.isFormatterLogger) {
      logMsg = formatLogEntry({
        'timestamp': new Date().toISOString(),
        'level': 'info',
        'msg': event.data,
      })
    }
    if (props.wsConfig.isAnsiColorFixed) {
      logMsg = ansiColorFixedFormat(event.data);
    } else {
      logMsg = event.data;
    }
    term.value?.write(logMsg);
  };

  socket.onerror = () => {
    connectionStatus.value = 'closed';
  };

  socket.onclose = () => {
    connectionStatus.value = 'closed';
    term.value?.writeln('\x1B[31mConnection  closed\x1B[0m');
  };
};

const destoryXtermTerminal = () => {
  socket?.close();
  if (props.wsConfig.protocols === 'ws') {
    term.value?.dispose();
  }
}

onMounted(() => {
  initTerminal();
  if (props.wsConfig.protocols === 'singleMsgs') {
    // 无需连接，直接置为已连接
    connectionStatus.value = 'connected'
    term.value?.clear()
    term.value?.write(props.message.toString())
  }
  else if (props.wsConfig.protocols === 'ws') {
    connectWebSocket();
  } else {
    connectWebSocket();
  }
  // TODO: 适配API方式获取日志
  
});

onBeforeUnmount(() => {
  destoryXtermTerminal();
});
</script>

<style scoped>
.xterm-container {
  width: 100%;
  background: #1a1a1a;
  border-radius: 4px;
  position: relative;
  height: auto;
  overflow: hidden;
  /* 禁用外部滚动条 */
}

.status-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 5px 10px;
  border-radius: 3px;
  z-index: 1;
}

:deep(.xterm-viewport) {
  overflow-y: auto !important;
  /* 启用内置滚动 */
}
</style>