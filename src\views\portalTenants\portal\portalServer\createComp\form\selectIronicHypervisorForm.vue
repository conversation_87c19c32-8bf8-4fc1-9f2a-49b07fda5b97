<template>
  <div>
    <div style="margin-bottom: 12px;">
              <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image :src="ironicHypervisorNodeImg" alt="裸机节点图标" style="width: 24px; height: 16px;"></tiny-image>当前裸机节点</span>
              <tiny-tag type="success" size="medium"> {{ currentSelectIronicHyperisor }} </tiny-tag>
              <span style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">一般情况下，无需配置此项，配置时，请注意节点状态及调度情况，非必要情况下，请不要设置！！！，管理员可配置。</span>
            </div>
    <tiny-grid ref="selectIronicHyperisorGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @radio-change="handleRadioChange" header-align="center">
      <tiny-grid-column type="radio" width="40"></tiny-grid-column>
      <tiny-grid-column field="name" title="ID/名称" align="left" width="180" :sortable="true" :filter="nameFilter">
        <template #default="{row}">
          <div class="id-cell">
              <!-- <span class="id-text">{{ row.ironicHyperisor_id.slice(0, 16) }}</span> -->
              <tiny-link :underline="false" type="primary">{{ row.ironic_hyper_id.slice(0, 8) }}</tiny-link>
              <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.ironic_hyper_id)"></tiny-link>
          </div>
          <p>{{ row.name }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="power_state" title="电源状态" align="center">
      </tiny-grid-column>
      <tiny-grid-column field="provision_state" title="配置状态" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="is_protected" title="预留" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.is_protected ? '是' : '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="is_maintenance" title="维护状态" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.is_maintenance ? '是' : '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="resource_class" title="资源类" align="center" :sortable="true" :filter="resourceClassFilter">
      </tiny-grid-column>
      <tiny-grid-column field="network_interface" title="网络接口" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="management_interface" title="管理接口" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="sync_time" title="同步时间" align="center" :sortable="true">
        <template #default="{row}">
          {{ formatNow(row.sync_time) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectIronicHypervisorForm">
import { ref, reactive, toRefs, watch } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyTag,
  TinyImage,
  TinyInput,
  TinyPager,
} from '@opentiny/vue';
import { GetList } from '/@/api/tenant/opIronicHypervisor';

import { iconCopy } from '@opentiny/vue-icon';
import { formatNow } from '/@/utils/formatTime';
import { copyText } from '/@/utils/copyText';
import ironicHypervisorNodeImg from '/@/assets/img/ironic-hypervisor-node.svg';

const props = defineProps({
  projectId: {
    type: String,
    required: false,
    default: ''
  }
});
// 当前选中值
let currentSelectIronicHyperisor = ref<string>('--');
const emit = defineEmits(['update:currentSelectIronicHypervisorId']);

const TinyIconCopy = iconCopy();
const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input,default,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})
const resourceClassFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input,enum,default,extends,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ],
  },
  enumable: true,
})

// 初始化请求数据
interface FilterOptions {
  ironic_hyper_id: string;
  power_state: string;
  provision_state: string;
  is_protected: boolean;
  is_maintenance: boolean;
  resource_class: string;
  name: string;
  network_interface: string;
  management_interface: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    ironic_hyper_id: '',
    power_state: '',
    provision_state: '',
    is_protected: false,
    is_maintenance: false,
    resource_class: '',
    name: '',
    network_interface: '',
    management_interface: '',
  },
});
let tableData = ref([]);

const selectIronicHyperisorGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectIronicHyperisorGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    ironic_hyper_id: '',
    power_state: '',
    provision_state: '',
    is_protected: false,
    is_maintenance: false,
    resource_class: '',
    name: '',
    network_interface: '',
    management_interface: '',
  };
  // reloadGrid();
}
const handleRadioChange = () => {
  let selectedRow = selectIronicHyperisorGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectIronicHyperisor.value = `${selectedRow.name}`
    emit('update:currentSelectIronicHypervisorId', selectedRow.ironic_hyper_id)
  }
  
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
      filterOptions.value.name = filters.filters.name.value.text;
    }
  else if (filters.filters.resource_class && filters.filters.resource_class.type === 'input') {
    filterOptions.value.resource_class = filters.filters.resource_class.value.text;
    }
  reloadGrid();
}

watch(() => props.projectId, (newProjectId) => {
  if (newProjectId) {
    // TODO 待优化
    // filterOptions.value.project_id = newProjectId
    // reloadGrid();
  }
});

</script>
<style lang="less" scoped>

.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>