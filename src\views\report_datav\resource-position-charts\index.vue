<template>
  <fs-page>
    <div class="page-header">
      <div class="header-controls">
        <div class="current-date-container">
          <span class="current-date">
            <img :src="idcMachineIcon" class="date-icon" />
            <span class="date-text">机房：</span>
            <el-select v-model="selectedDataCenter" @change="handleDataCenterChange" size="small"
              style="width: 120px; margin-right: 15px;" :disabled="reportId !== undefined">
              <el-option v-for="item in dataCenterOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </span>
        </div>

        <!-- 将按钮组放在最后并使用flex spacer推送到右侧 -->
        <div class="flex-spacer"></div>

        <div class="button-group">
          <el-button type="primary" size="small" @click="isShowChartDescription = !isShowChartDescription">
            <img :src="descriptionIcon" class="button-icon" />
            {{ isShowChartDescription ? "隐藏" : "显示" }}详细说明
          </el-button>
          <el-button size="small" type="primary" @click="openSaveDialog" :disabled="reportId">
            <img :src="saveIcon" class="button-icon" />
            保存
          </el-button>
          <el-button type="primary" size="small" @click="exportPDF">
            <img :src="pdfIcon" class="button-icon" />
            导出PDF
          </el-button>
        </div>
      </div>
    </div>

    <Export2PDF ref="pdfExporter" :file-name="`资源统计_${selectedDataCenter}_${currentDate}.pdf`" :scale="1.5"
      background-color="#F5F5F5">
      <div class="export-content">
        <!-- Header will be included in PDF -->
        <div class="pdf-header">
          <h2>资源统计报表</h2>
          <div class="pdf-subheader">
            <span class="current-date">
              <img :src="idcMachineIcon" class="date-icon" />
              <span class="date-text">机房：{{ selectedDataCenter }}</span>
            </span>
            <span class="current-date">
              <img :src="dateIcon" class="date-icon" />
              <span class="date-text">日期：{{ currentDate }}</span>
            </span>
          </div>
        </div>

        <div class="resource-overview-container">
          <ChartDescription :data="descriptions" />
        </div>
        <div class="resource-overview-container">
          <ChartDescription :data="chartDataDescriptions" :config="{ name: '详细说明' }" v-if="isShowChartDescription" />
        </div>
        <div class="resource-overview-container">
          <ResourceStatisical :data="resourceStatiscalData" />
        </div>
        <div class="resource-overview-container">
          <ChartGauge :data="resourceStatiscalData.had_used_rate"
            :config="{ title: '在维主机使用率', subTitle: '所有在维主机已使用百分比', name: '使用率' }" />
          <ChartGauge :data="resourceStatiscalData.had_rental_rate"
            :config="{ title: '在维主机出租率', subTitle: '所有在维主机已出售百分比', name: '出租率' }" />
        </div>
        <div class="resource-overview-container">
          <ChartBar :data="resourceStatiscalData.buffer_machine_tags"
            :config="{ title: 'Buffer池主机分类', subTitle: 'Buffer池主机按网络类型/显卡分类', xColumn: 'network_class', direction: 'vertical' }" />
          <ChartLine :data="resourceStatiscalData.month_commit_server_occurrence"
            :config="{ title: '月交付频次', subTitle: '每月交付主机量(包含测试主机)', xColumn: 'month', xColumnName: '月份', yColumnName: '频次', showMin: false }" />
        </div>
        <div class="resource-overview-container">
          <ChartPie :data="resourceStatiscalData.operator_server_feature_category"
            :config="{ title: '在维主机功能分类', subTitle: '所有在维主机功能分类' }" />
          <ChartPie :data="resourceStatiscalData.operator_server_gpu_category"
            :config="{ title: '可售主机GPU分类', subTitle: '可售主机GPU分类' }" />
        </div>
        <div class="resource-overview-container">
          <ChartBar :data="resourceStatiscalData.customer_resource_count_top5"
            :config="{ title: '客户主机数Top5', subTitle: '仅统计客户在用的裸机', xColumn: 'company', direction: 'horizontal' }">
          </ChartBar>
          <ChartBar :data="resourceStatiscalData.server_power_watts_top10"
            :config="{ title: '服务器功率Top10', subTitle: '服务器功率Top10', xColumn: 'instance', direction: 'horizontal' }">
          </ChartBar>
        </div>
        <div class="resource-overview-container">
          <ChartChinaMap style="width: 100%; height: 800px;"
            :mapData="resourceStatiscalData.china_province_customer_had_resource_map" mapTitle="客户全国分布"
            subTitle="仅展示已有主机的客户地图分布" />
        </div>
      </div>
    </Export2PDF>

    <!-- 保存报表的对话框 -->
    <el-dialog v-model="dialogVisible" title="保存报表" width="600px" :close-on-click-modal="false"
      @closed="handleDialogClosed">
      <el-form ref="reportForm" :model="addReportData" :rules="addReportFormRules" label-width="120px">
        <el-form-item label="报表名称" prop="name">
          <el-input v-model="addReportData.name" placeholder="请输入报表名称" clearable />
        </el-form-item>

        <el-form-item label="报表分类" prop="category">
          <el-select v-model="addReportData.category" placeholder="请选择分类" style="width: 100%">
            <el-option v-for="item in categoryData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="isSaving" @click="handleSave">
          确认
        </el-button>
      </template>
    </el-dialog>
  </fs-page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, Ref, reactive, } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { GetResoureStatisicalCharts, GetObj, AddObj, } from '/@/api/report_datav/report';
import { dictionary } from '/@/utils/dictionary';
import ResourceStatisical from './comp/ResourceStatisical.vue';
import ChartLine from '/@/components/chaosCharts/chartLine.vue';
import ChartPie from '/@/components/chaosCharts/chartPie.vue';
import ChartGauge from '/@/components/chaosCharts/chartGauge.vue';
import ChartChinaMap from '/@/components/chaosCharts/chartChinaMap.vue';
import ChartBar from '/@/components/chaosCharts/chartBar.vue';
import ChartDescription from '/@/components/chaosCharts/chartDescription.vue';
import Export2PDF from '/@/components/export2PDF/index.vue';
import dateIcon from '/@/assets/img/date.svg';
import idcMachineIcon from '/@/assets/img/idc-machine.svg';
import descriptionIcon from '/@/assets/img/description.svg';
import pdfIcon from '/@/assets/img/pdf.svg';
import saveIcon from '/@/assets/img/save.svg';


const route = useRoute();
const reportId: Ref<undefined | string> = ref(route.params.reportId as string | undefined);

const pdfExporter = ref();
const selectedDataCenter = ref('全部');
const dataCenterOptions = ['全部', '金华', '上海'];

// Format current date as YYYY-MM-DD 
const currentDate = ref('');
// 修改后的日期格式化函数 
const formatDate = (dateString?: string) => {
  const date = dateString ? new Date(dateString) : new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  currentDate.value = `${year}-${month}-${day}`;
};

const exportPDF = () => {
  pdfExporter.value.exportAsPDF();
};

const descriptions = [
  '目前展示的机房数量仅展示正式使用的资源池。',
  'Buffer资源池(成品机位、机器、电力、网络具备)。',
  '在维主机：目前运维接入的主机(不包含未接电、下线等特殊情况的主机)。',
  '可售主机：目前可直接出租(出售)的主机(具备网络、成品机位、电力、显卡的主机)。',
];

const isShowChartDescription = ref(false);
const chartDataDescriptions = [
  '机房总量：数据来源：配置中心；可选项：["全部","金华","上海"]；去除掉"全部"后计算数量。',
  'IDC机柜：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 主机状态!=下线；仅获取关联的idc机柜信息去重后计算数量。',
  '物理服务器：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 主机状态 != 下线；去重后计算数量。',
  'Buffer池机器：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 资源类型=裸金属机 + 主机状态=空闲；去重后计算数量。',
  '已租用：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 资源类型包含[裸金属机, 客户存储机] + 主机状态=启用；去重后计算数量。',
  '已使用：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 主机状态=启用；去重后计算数量。',
  '在维主机使用率：数据来源：以上已计算的结果；公式：已使用/物理服务器。',
  '在维主机出租率：数据来源：以上已计算的结果；公式：已租用/物理服务器。',
  'Buffer池主机分类：数据来源：Buffer池机器 + 云资源池->基础配置->裸机节点；查询条件：查询Buffer池机器后，查询裸机节点资源类信息(Openstack已注册管理的节点的资源类)；以网络带宽为基准，计算GPU类型属数量。',
  '月交付频次：数据来源：云产品及服务->弹性计算->裸金属；查询条件：按月份为组+排除掉"曹相鹏网络测试项目"创建的主机，计算已创建的主机数量，默认查询月份：前12个月。',
  '在维主机功能分类：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 主机状态包含[空闲,启用]；以功能分类标签为组计算数量。',
  '可售主机GPU分类：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 资源类型包含=裸金属机 + 主机状态包含[空闲,启用]；以GPU分类标签为组计算数量。',
  '客户主机数Top5：数据来源：运维管理->主机管理；查询条件：机房=已选机房 + 主机类型=裸金属 + 主机状态=启用 + 资源类型=裸金属机；以客户为组计算主机数量+计算显卡类型数量。',
  '主机功率Top10：数据来源：金华Prometheus监控；查询条件：功率指标[node:ipmi_power_watts:sum]；查询功率Top10的主机信息。',
  '客户全国分布：数据来源：运维管理->主机管理+采销管理->客户管理；查询条件：获取所有有主机的客户+客户所属省份；以客户省份为组计算数量。',

];


let resourceStatiscalData = ref({
  "had_used_rate": 0,
  "had_rental_rate": 0,
  "had_used_amount": 0,
  "had_rental_amount": 0,
  "buffer_machine_amount": 0,
  "buffer_machine_tags": [],
  "machine_room_amount": 0,
  "idc_rack_machine_amount": 0,
  "physical_server_machine_amount": 0,
  "month_commit_server_occurrence": [],
  "operator_server_feature_category": [],
  "customerLocationMap": [],
  "operator_server_gpu_category": [],
  "customer_resource_count_top5": [],
  "server_power_watts_top10": [],
  "china_province_customer_had_resource_map": []
})

const handleDataCenterChange = (value: string) => {
  getData(value);
};

// 报表数据
const addReportData = reactive({
  name: '核心资源统计',
  code: 'resource-statisical-charts',
  category: '运营',
  source_data: resourceStatiscalData.value,
  path_name: 'adminReportResourceStatisicalDetail',
  selected_data: {
    selected_data_center: '',
  },
});
const categoryData = ref(dictionary('report_datav:report:category', undefined));
// 加载状态 
const isSaving = ref(false);
const reportForm = ref();
// 对话框控制 
const dialogVisible = ref(false);
// 表单验证规则 
const addReportFormRules = ref({
  name: [
    { required: true, message: '请输入报表名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到255个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择报表分类', trigger: 'change' }
  ],
})
// 对话框关闭时的处理
const handleDialogClosed = () => {
  // 正确调用方式：在表单引用上调用clearValidate
  if (reportForm.value) {
    reportForm.value.clearValidate()
  }
}

// 数据保存到报表内
const saveReport = async () => {
  // 更新源数据
  addReportData.source_data = resourceStatiscalData.value;
  addReportData.selected_data.selected_data_center = selectedDataCenter.value;
  const response = await AddObj(addReportData);
  if (response.code === 2000) {
    ElMessage.success(' 报表保存成功')

  } else {
    ElMessage.error(response.message || '保存失败')
  }
  isSaving.value = false;
  dialogVisible.value = false;
}
// 打开对话框 
const openSaveDialog = () => {
  // 确保每次打开对话框时都是新鲜的表单 
  dialogVisible.value = true;
  addReportData.name = addReportData.name + '_' + selectedDataCenter.value + '_' + currentDate.value;
}
// 处理保存 
const handleSave = () => {
  reportForm.value.validate((valid: any) => {
    if (valid) {
      saveReport()
    } else {
      ElMessage.warning(' 请填写完整的报表信息!!!')
    }
  })
}

const getData = async (areaNode = '全部') => {
  try {
    const { data } = await GetResoureStatisicalCharts({ 'area_node': areaNode })
    resourceStatiscalData.value = data;
  } catch (error) {
    ElMessage.error('数据加载失败');
  }
}


const getReportData = async (reportId: string) => {
  try {
    const { data } = await GetObj(reportId);
    resourceStatiscalData.value  = data.source_data; 
    
    // Set data center from report data if available 
    if (data?.selected_data?.selected_data_center) {
      selectedDataCenter.value  = data.selected_data.selected_data_center; 
      
      // Ensure the data center exists in options 
      if (!dataCenterOptions.includes(selectedDataCenter.value))  {
        dataCenterOptions.push(selectedDataCenter.value); 
      }
    }
 
    // Set date from report or current time 
    formatDate(data?.create_datetime);
    
    // Fetch data for the selected data center 
    if (selectedDataCenter.value)  {
      await getData(selectedDataCenter.value); 
    }
  } catch (error) {
    ElMessage.error(' 数据加载失败');
  }
};


// 监听 reportId 的变化 
watch(reportId, (newReportId) => {
  if (newReportId) {
    getReportData(newReportId);
  } else {
    // Reset to default when not viewing a report 
    selectedDataCenter.value  = '全部';
    getData();
    formatDate();
  }
}, { immediate: true });

onMounted(() => {
  // 初始化时根据是否有reportId决定如何设置日期 
  if (!reportId.value) {
    formatDate();
  }
})
</script>

<style scoped>
.page-header {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  margin: 10px;
  padding: 12px 15px;
}

.header-controls {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  /* Prevent wrapping */
}

.flex-spacer {
  flex-grow: 1;
}

.button-group {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.button-group .el-button {
  margin-left: 10px;
  display: flex;
  align-items: center;
}

.button-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}

.current-date-container {
  display: flex;
  align-items: center;
  margin-right: 15px;
  min-width: 160px;
  /* Ensure enough space for date */
}

.current-date {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.date-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  flex-shrink: 0;
  /* Prevent icon from shrinking */
}

.date-text {
  white-space: nowrap;
}

.export-btn {
  margin-left: auto;
  flex-shrink: 0;
  /* Prevent button from shrinking */
}

.pdf-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.pdf-subheader {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.resource-overview-container {
  display: flex;
  gap: 16px;
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-controls {
    flex-wrap: wrap;
  }

  .current-date-container {
    order: 1;
    width: 100%;
    margin: 10px 0;
  }

  .export-btn {
    margin-left: 0;
    width: 100%;
  }
}
</style>