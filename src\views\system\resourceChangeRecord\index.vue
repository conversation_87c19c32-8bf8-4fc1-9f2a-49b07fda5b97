<template>
  <fs-page>
  <div class="card-wrap">
    <div v-if="resourceChangeRecordData.length >0">
      <tiny-card class="card-padding-item" :auto-width="true" v-for="item in resourceChangeRecordData" :key="item.id">
        <template #title-left>
          <div class="card-title-bolder">
            <tiny-tag type="success" size="medium" effect="light"><TinyIconUser />&nbsp;{{item.creator_name}}</tiny-tag>
          </div>
        </template>
        <template #title>
          <span class="card-title-bolder">&nbsp;在&nbsp;<TinyIconTime />&nbsp;{{ `${item.create_datetime}&nbsp;` }}</span>
        </template>
        <template #title-right>
          <div class="card-title-bolder">
            <tiny-tag type="success" size="medium" effect="light"><TinyIconEdit />&nbsp;{{item.action}}了</tiny-tag>
          </div>
          
        </template>
        <tiny-layout :cols="24">
          <tiny-row :gutter="10">
            <tiny-col :span="12">
              <tiny-card :auto-width="true" v-if="item.old_values" status="danger">
                <template #title>
                  <span class="card-title-bolder">原数据</span>
                </template>
                <el-descriptions
                  :column="1"
                  size="small"
                  border
                  v-for="(subItem, index) in item.old_values" :key="index">
                  <el-descriptions-item width="50%" min-width="20%" :label="subItem.field_cn_name">{{subItem.value}}</el-descriptions-item>
              </el-descriptions>
              </tiny-card>
            </tiny-col>
            <tiny-col :span="12">
              <tiny-card :auto-width="true" status="success">
                <template #title>
                  <span class="card-title-bolder">新数据</span>
                </template>
                <el-descriptions
                :column="1"
                size="small"
                border
                v-for="(subItem, index) in item.new_values" :key="index">
                <el-descriptions-item width="50%" min-width="20%" :label="subItem.field_cn_name">{{subItem.value}}</el-descriptions-item>
            </el-descriptions>
              </tiny-card>
            </tiny-col>
          </tiny-row>
        </tiny-layout>
      </tiny-card>
    </div>
    <div v-else>
      <p class="no-data-wrap">暂无数据</p>
      <div class="no-data-wrap">
        <tiny-image :src=noListDataImg fit="scall-down"></tiny-image>
      </div>
      
    </div>
    </div>
    
</fs-page>
</template>

<script setup name="AuditLog">
import { Card as TinyCard, Image as TinyImage } from '@opentiny/vue';
import { IconUser, IconEdit, IconTime } from '@opentiny/vue-icon';
import { onMounted, ref } from 'vue';
import * as api from '/@/views/system/api/auditLog';
import { useRoute } from "vue-router";
import noListDataImg from "/src/assets/img/no-list-data.png"


const TinyIconUser = IconUser();
const TinyIconEdit = IconEdit();
const TinyIconTime = IconTime();

const resourceChangeRecordData = ref([]);


const route = useRoute();
const searchResourceParams = {
  resource_id: route.query.resource_id
};


const getResourceChangeRecordList = async () => {
  const listData = await api.GetResourceLogsList(searchResourceParams)
  resourceChangeRecordData.value = listData.data
}


// 页面打开后获取列表数据
onMounted(() => {
	getResourceChangeRecordList()
});
</script>

<style scoped>
.card-wrap {
  padding: 16px;
}
.card-padding-item {
  margin-bottom: 10px;
}
.card-title-bolder {
  font-weight: bolder;
  font-size: 1.5em;
  padding-bottom: 10px;
}
.no-data-wrap {
  display: flex;
  justify-content: center;
  padding: 20px;
  font-size: 1.5em;
}
</style>
