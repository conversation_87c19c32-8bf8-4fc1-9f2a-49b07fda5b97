import { Base } from '/@/views/interface/baseInterface/commonInterface';


export interface DetailPhysicalServerMachine extends Base {
  physical_machine_sn: string | undefined;
  instance_id: string | undefined;
  machine_type: string | undefined;
  machine_room: string | undefined;
  private_room: string | undefined;
  idc_rack_machine: string | undefined;
  rack_unit: number | undefined;
  manufacturer: string | undefined;
  machine_specs: string | undefined;
  belong: string | undefined;
  specs: string | undefined;
  normal_power: number | undefined;
  gpu_model: string | undefined;
}
