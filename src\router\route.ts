import { RouteRecordRaw } from 'vue-router';

/**
 * 路由meta对象参数说明
 * meta: {
 *      title:          菜单栏及 tagsView 栏、菜单搜索名称（国际化）
 *      isLink：        是否超链接菜单，开启外链条件，`1、isLink: 链接地址不为空`
 *      isHide：        是否隐藏此路由
 *      isKeepAlive：   是否缓存组件状态
 *      isAffix：       是否固定在 tagsView 栏上
 *      isIframe：      是否内嵌窗口，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
 *      roles：         当前路由权限标识，取角色管理。控制路由显示、隐藏。超级管理员：admin 普通角色：common
 *      icon：          菜单、tagsView 图标，阿里：加 `iconfont xxx`，fontawesome：加 `fa xxx`
 * }
 */

/**
 * 定义动态路由
 * 前端添加路由，请在顶级节点的 `children 数组` 里添加
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */
export const dynamicRoutes: Array<RouteRecordRaw> = [
	{
		path: '/',
		name: '/',
		component: () => import('/@/layout/index.vue'),
		redirect: '/home',
		meta: {
			isKeepAlive: true,
		},
		children: [],
	},
  {
		path: '/personal/messageNotice',
		name: 'personalMessageNotice',
		component: () => import('/@/views/system/personal/messageNotice/index.vue'),
		meta: {
			title: '我的消息通知',
      isLink: '',
			isHide: false,
			isKeepAlive: true,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
	{
		path: '/personal',
		name: 'personal',
		component: () => import('/@/views/system/personal/index.vue'),
		meta: {
			title: 'message.router.personal',
			isLink: '',
			isHide: false,
			isKeepAlive: true,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/resource/idcRackMachine/add',
		name: '创建机柜',
		component: () => import('/@/views/resource/idcRackMachine/add.vue'),
		meta: {
			title: '创建机柜',
			isLink: '',
			isHide: true,
			isKeepAlive: true,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/system/dataPermission/config',
		name: '数据配置',
		component: () => import('/@/views/system/dataPermission/config.vue'),
		meta: {
			title: '数据配置',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/portalTenants/ecs/form',
		name: 'ECS',
		component: () => import('/@/views/portalTenants/ecs/form.vue'),
		meta: {
			title: 'ECS',
			isLink: '',
			isHide: false,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/system/resourceChangeRecords/detail',
		name: 'resourceLogs',
		component: () => import('/@/views/system/resourceChangeRecord/index.vue'),
		meta: {
			title: 'resoruceChangeRecord',
			isLink: '',
			isHide: false,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/portalTenants/portal/portalServer/create',
		name: '创建主机',
		component: () => import('/@/views/portalTenants/portal/portalServer/create.vue'),
		meta: {
			title: '创建主机',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/portalTenants/portal/portalServer/applyBaremetalServerTicket',
		name: '申请裸金属订单',
		component: () => import('/@/views/ticket/ticket/apply_ticket.vue'),
		meta: {
			title: '申请裸金属订单',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/portalTenants/portal/portalServer/applyTenantBaremetalServerTicket',
		name: '申请裸金属',
		component: () => import('/@/views/ticket/ticket/portalTenantTicket/create.vue'),
		meta: {
			title: '申请裸金属',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面
		path: '/portalTenants/portal/portalAccount/create',
		name: '创建租户账号',
		component: () => import('/@/views/portalTenants/portal/portalAccount/create.vue'),
		meta: {
			title: '创建租户账号',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/portalTenants/ticket/success',
		name: '申请主机订单表创建成功',
		component: () => import('/@/views/ticket/ticket/reportComp/01applyTicketsucess.vue'),
		meta: {
			title: '申请主机订单表创建成功',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/portalTenants/ticket/:ticket_id/:step',
		name: '订单审批-确认账号和主机信息',
		component: () => import('/@/views/ticket/ticket/step1ApprovalTicket.vue'),
		meta: {
			title: '订单审批-确认账号和主机信息',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/portalTenants/ticket/:ticket_id/:step/reportResult',
		name: '订单报告预览',
		component: () => import('/@/views/ticket/ticket/resultReport.vue'),
		meta: {
			title: '订单报告预览',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
  },
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/operatorCMDB/host/detail/:id',
		name: 'opHostDetail',
		component: () => import('/@/views/operatorCMDB/host/detail.vue'),
		meta: {
			title: '资源主机详情',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/portalTenants/server/index',
		name: '主机连接',
		component: () => import('/@/views/portalTenants/portal/portalServer/webssh/index.vue'),
		meta: {
			title: '主机连接',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/portalTenants/server/detail/:id',
		name: 'opServerDetail',
		component: () => import('/@/views/portalTenants/portal/portalServer/detail/index.vue'),
		meta: {
			title: '主机详情',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/portalTenants/ironicHypervisior/detail/:id',
		name: 'opIronHypervisiorDetail',
		component: () => import('/@/views/portalTenants/tenantIronicHypervisior/detail.vue'),
		meta: {
			title: '裸机节点详情',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: '/resource/physicalAsset/physicalServerMachine/detail/:id',
		name: 'physicalServerMachineDetail',
		component: () => import('/@/views/resource/physicalAsset/physicalServerMachine/detail.vue'),
		meta: {
			title: '物理服务器详情',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: `/report_datav/report/admin/resource-statisical/detail/:reportId`,
		name: 'adminReportResourceStatisicalDetail',
		component: () => import('/@/views/report_datav/resource-position-charts/index.vue'),
		meta: {
			title: '运营资源概览',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: `/report_datav/report/admin/customer-resource-statisical/detail/:reportId`,
		name: 'customerReportResourceStaticalDetail',
		component: () => import('/@/views/report_datav/customer-resource-charts/index.vue'),
		meta: {
			title: '租户资源概览',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
    
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: `/g6/example`,
		name: 'g6-example',
		component: () => import('/@/components/chaosG6/ERCharts.vue'),
		meta: {
			title: '关系图',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
    
	},
  {
    // 无需权限设置，单-静态-新增页面, 订单提交成功页面
		path: `/g6/network`,
		name: 'g6-network',
		component: () => import('/@/components/chaosG6/network.vue'),
		meta: {
			title: '交换机网口图',
			isLink: '',
			isHide: true,
			isKeepAlive: false,
			isAffix: false,
			isIframe: false,
			icon: 'iconfont icon-gerenzhongxin',
		},
    
	},
  {
    // 维修报表详情
    path: `/report_datav/report/admin/maintenance-resource-charts/detail/:reportId`,
    name: 'adminReportMaintenanceDetail',
    component: () => import('/@/views/report_datav/maintenance-resource-charts/index.vue'),
    meta: {
      title: '维修报告',
      isLink: '',
      isHide: true,
      isKeepAlive: false,
      isAffix: false,
      isIframe: false,
      icon: 'iconfont icon-gerenzhongxin',
    },
  },
];

/**
 * 定义404、401界面
 * @link 参考：https://next.router.vuejs.org/zh/guide/essentials/history-mode.html#netlify
 */
export const notFoundAndNoPower = [
	{
		path: '/:path(.*)*',
		name: 'notFound',
		component: () => import('/@/views/system/error/404.vue'),
		meta: {
			title: 'message.staticRoutes.notFound',
			isHide: true,
		},
	},
	{
		path: '/401',
		name: 'noPower',
		component: () => import('/@/views/system/error/401.vue'),
		meta: {
			title: 'message.staticRoutes.noPower',
			isHide: true,
		},
	},
];

/**
 * 定义静态路由（默认路由）
 * 此路由不要动，前端添加路由的话，请在 `dynamicRoutes 数组` 中添加
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
	{
		path: '/login',
		name: 'login',
		component: () => import('/@/views/system/login/index.vue'),
		meta: {
			title: '登录',
		},
	},
];
