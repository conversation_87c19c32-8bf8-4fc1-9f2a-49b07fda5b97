import * as api from '/@/api/ticket/ticket';

import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  useCompute,
  dict
} from '@fast-crud/fast-crud';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { dictionary } from '/@/utils/dictionary';
import { useUserInfo } from '/@/stores/userInfo';
import { Modal as TinyModal } from '@opentiny/vue';

const { compute } = useCompute()

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const userInfo = useUserInfo()
  const pageRequest = async (query: UserPageQuery) => {
    query = {
      ...query,
      'creator': userInfo.userInfos.user_id,
    }
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            type: 'primary',
            text: '申请主机',
            plain: true,
            show: auth('ticket:myselfTicket:Create'),
            click() {
              if (userInfo.userInfos.user_type === 1) {
                router.push("/portalTenants/portal/portalServer/applyTenantBaremetalServerTicket")
              } else {
                router.push("/portalTenants/portal/portalServer/applyBaremetalServerTicket")
              }
            }
          },
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 280,
        buttons: {
          view: {
            type: 'primary',
            link: true,
            // show: auth('ticket:myselfTicket:Retrieve')
            show: false,
          },
          edit: {
            type: 'primary',
            link: true,
            show: auth('ticket:myselfTicket:Update')
          },
          remove: {
            type: 'danger',
            link: true,
            show: auth('ticket:myselfTicket:Delete')
          },
          stepApproval: {
            type: 'primary',
            text: '审批订单',
            link: true,
            disabled: compute((context) => {
              // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
              return context.row.current_step_index !== 1
            }),
            show: auth('ticket:myselfTicket:viewApprovalTicket') && compute((context) => {
              // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
              return context.row.is_finished === false
            }),
            click(context) {
              router.push(`/portalTenants/ticket/${context.row.id}/${context.row.current_step_index}`)
            }
          },
          reportResult: {
            type: 'primary',
            text: '查看报告',
            link: true,
            show: auth('ticket:myselfTicket:viewReportTicket') && compute((context) => {
              // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
              return context.row.is_finished === true
            }),
            click(context) {
              if (userInfo.userInfos.user_type === 1) {
                router.push(`/portalTenants/ticket/${context.row.id}/tenant/reportResult`)
              } else {
                router.push(`/portalTenants/ticket/${context.row.id}/${context.row.current_step_index}/reportResult`)
              }
              
            }
          },
          downloadReport: {
            link: true,
            text: '下载报告',
            type: 'primary',
            show: auth('ticket:ticketResultReport:TenantBaremetalServerManualGenReport') && compute((context) => {
              // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
              return context.row.report_url !== ''
            }),
            click(context) {
              // 获取报告的 URL
              const reportUrl = context.row.report_url;

              if (reportUrl) {
                fetch(reportUrl)
                  .then(response => {
                    if (!response.ok) {
                      throw new Error('文件下载失败');
                    }
                    return response.blob();
                  })
                  .then(blob => {
                    // 设置自定义文件名
                    const fileName = `${context.row.ticket_id}交付报告.docx`;

                    // 创建一个临时的 <a> 标签
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;  // 确保这里设置了文件名
                    a.click();

                    // 释放 URL 对象
                    window.URL.revokeObjectURL(url);
                    TinyModal.message({
                      message: '获取报告地址成功',
                      status: 'success',
                    });
                  })
                  .catch(error => {
                    TinyModal.message({
                      message: '下载失败',
                      status: 'danger',
                    });
                  })
              } else {
                // 如果没有报告 URL，提示用户
                TinyModal.message({
                  message: '报告尚未生成，请稍后再试。',
                  status: 'warning',
                });
              }
            }
          },
        },
      },
      pagination: {
        show: true,
        'default-page-size': 10,
        'default-current': 1,
      },
      table: {
        rowKey: 'id',
      },
      form: {
        labelWidth: 100,
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        name: {
          title: '名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 150,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '名称必填项' },
              { max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' }
            ],
            component: {
              placeholder: '请输入订单名称',
            }
          }
        },
        current_status: {
          title: '状态',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('ticket:ticket:createBarematealServerTicketStatus', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            show: false,
            rules: [
              // 表单校验规则
              { required: false, message: '当前状态必填项' },
            ],
            component: {
              placeholder: '请输入当前状态',
            }
          }
        },
        ticket_id: {
          title: '订单ID',
          search: {
            show: true,
          },
          type: 'string',
          column: {
            minWidth: 90,
          },
          form: {
            show: false,
            rules: [
              // 表单校验规则
              { required: false, message: '订单ID必填项' },
            ],
            component: {
              placeholder: '请输入订单ID',
            }
          }
        },
        creator_name: {
          title: '创建人',
          search: {
            show: true,
          },
          type: 'string',
          column: {
            minWidth: 90,
          },
          form: {
            show: false,
            rules: [
              // 表单校验规则
              { required: false, message: '创建人必填项' },
            ],
            component: {
              placeholder: '请输入创建人',
            }
          }
        },
        // is_finished: {
        //   title: '任务调度状态',
        //   search: {
        //     show: false,
        //   },
        //   column: {
        //     minWidth: 90,
        //     formatter({value, row ,index}){
        //         // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
        //         if (row.is_finished)
        //         {
        //           return '已结束'
        //         } else if (!row.is_finished && row.current_step_index === 1) {
        //           return '未开始'
        //         } else {
        //           return '进行中'
        //         }
        //   }
        //   },
        //   form: {
        //     show: false,
        //   }
        // },
      },
    },
  };
};
