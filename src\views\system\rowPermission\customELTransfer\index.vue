<template>
  <div style="display: inline-block">
    <div style="text-align: center">
      <el-button size="default" type="success" @click="handleEmpower()">
        <slot>授权</slot>
      </el-button>
      <el-dialog v-model="transferShow" width="80%">
        <el-tabs v-model="tabActive" type="border-card">
          <el-tab-pane label="用户" name="user">
            <el-form ref="rowTargetUserFormRef" :model="rowPermissionData" :rules="rowTargetUserRules" label-width="auto">
              <el-form-item label="用户" prop="user_id">
                <el-select
                  v-model="rowPermissionData.user_id"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="请选择用户"
                  :remote-method="selectRemoteMethod"
                  :loading="selectLoading"
                  style="width: 240px"
                >
                  <el-option
                    v-for="item in userInfos"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="数据源" prop="row_origin">
                <el-select
                  v-model="rowPermissionData.row_origin"
                  placeholder="请选择数据来源"
                  size="large"
                  style="width: 240px"
                >
                  <el-option
                    v-for="item in sourceModelNames"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="选择数据" width="100%" prop="row_ids" v-if="rowPermissionData.row_origin">
                <el-transfer
            v-model="rowPermissionData.row_ids"
            style="text-align: left; display: inline-block"
            filterable
            :titles="['源数据', '已选择']"
            :button-texts="['取消', '选择']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}',
            }"
            :data="sourceData"
          >
            <template #default="{ option }">
              <span>{{ option.label }}</span>
            </template>
          </el-transfer>
              </el-form-item>
            </el-form>
            
          <div class="footer-button">
            <el-button @click="transferShow = false">取消</el-button>
            <el-button type="primary" @click="submitForm(rowTargetUserFormRef)">
              确认
            </el-button>
          </div>
          </el-tab-pane>
          <el-tab-pane label="部门" name="dept">优化中，请等待.......</el-tab-pane>
          <el-tab-pane label="角色" name="role">优化中，请等待.......</el-tab-pane>
        </el-tabs>
      </el-dialog>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from 'vue'
import { GetModelRowList, GetUserList, AddRowTargetUser } from '../api'
import { getModelList } from "/@/api/models/search"
import { FormInstance, FormRules } from 'element-plus'

interface Option {
  key: number
  label: string
  disabled: boolean
}

const props = defineProps({
  userId: {
    type: Number,
    default: -1
  }
})

const transferShow = ref(false)
const selectLoading = ref(false)
const rowTargetUserFormRef = ref<FormInstance>()
const tabActive = ref('user')

// Modles Show Config

let modelsShowConfig: any = {}

// model show

let modelShowConfig: any = {}

/** 授权按钮操作 */

const handleEmpower = function () {
  transferShow.value = true
}

const rowPermissionData = reactive({
  user_id: props.userId,
  row_ids: [],
  row_origin: '',
  is_external: false

})

const sourceData = ref<Option[]>([])

// 监听数据表的选择
watch(() => rowPermissionData.row_origin, () => {
  modelShowConfig = modelsShowConfig[rowPermissionData.row_origin]
  getSourceData();
}, {immediate: false, deep: true})

const getSourceData = async () => {
    const apiRes = await GetModelRowList({
      row_origin: rowPermissionData.row_origin
    })
    const response = apiRes.data
    sourceData.value = []
    let modelKey = 'id'
    let modelValue = 'name'
    if (modelShowConfig) {
      modelKey = modelShowConfig.key
      modelValue = modelShowConfig.value
    }
    response.forEach((val: any) => {
    sourceData.value.push({
    key: val[modelKey],
    label: val[modelValue],
    disabled: false
    })
  })
    console.error(sourceData)

}

// model 数据源选择
interface SelectOption {
  key: number
  label: string
  disabled: boolean,
  value: string
}
let modelNames: SelectOption[] = []
const getModelData = async () => {
    const apiRes = await getModelList({app_name: 'resource'})
    const response = apiRes.data
    let models = response.models
    modelsShowConfig = response.models_show
    models.forEach((val: any) => {
      modelNames.push({
        key: val.key,
        label: val.title,
        // @ts-ignore
        value: val.app + '.' + val.key,
        disabled: false,
      })
    })
}

// 数据授权给用户 rules
const rowTargetUserRules = reactive<FormRules>({
  row_ids: [{required: true, message: '请选择', trigger: 'blur'}],
  row_origin: [{required: true, message: '请选择', trigger: 'blur'}],
  user_id: [{required: true, message: '请选择', trigger: 'blur'}],
  // is_external: [{required: false, message: '请选择', trigger: 'blur'}]
})

const submitForm = async (elForm: FormInstance | undefined) => {
  console.error(elForm)
  if (!elForm) return
  await elForm.validate((valid, fields) => {
    console.error(valid, fields)
    if (valid) {
      console.error('submit!')
      AddRowTargetUser(rowPermissionData).then(
        (res: any) => {
          if (res.code === 2000) {
            transferShow.value = false
          }
        }
      )
    }
  })
}
const selectSearchValue = ref('')
let userInfos = ref<SelectOption[]>([])
const getUserData = async () => {
    const apiRes = await GetUserList({
      search: selectSearchValue.value
    })
    const response = apiRes.data
    userInfos.value = []
    response.forEach((val: any) => {
      userInfos.value.push({
        key: val.id,
        label: val.name,
        value: val.id,
        disabled: false,
      })
    })
}


// select 选择组件远程搜索
const selectRemoteMethod = (query: string) => {
  if (query || userInfos.value.length === 0) {
    selectLoading.value = true
    setTimeout(() => {
      selectLoading.value = false
      selectSearchValue.value = query
      getUserData()
    }, 200)
  } else {
    userInfos.value = []
  }
}

const sourceModelNames = ref(modelNames)
console.error(modelNames)

onMounted(() => {
  getModelData();
})
</script>

<style scoped>
.transfer-footer {
  margin-left: 15px;
  padding: 6px 5px;
}
.footer-button {
  display: flex;
  justify-content: flex-start;
}
</style>
