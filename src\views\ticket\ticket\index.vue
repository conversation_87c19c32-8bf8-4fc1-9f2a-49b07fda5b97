<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
      <!-- <template #actionbar-right>
        <importExcel api="api/resource/machine_room/" v-if="isShowImportBtn">导入</importExcel>
      </template> -->
    </fs-crud>
	</fs-page>
</template>

<script lang="ts" setup name="ticket">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
// import importExcel from '/@/components/importExcel/index.vue'
// import { auth } from "/@/utils/authFunction";
const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });


// 导入按钮显示权限
// const isShowImportBtn: boolean = auth("resource:machineRoom:Import")


// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>
