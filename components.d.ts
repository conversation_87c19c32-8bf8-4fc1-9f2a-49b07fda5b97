/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AnsibleXtermlogViewer: typeof import('./src/components/myXterm/AnsibleXtermlogViewer.vue')['default']
    Auth: typeof import('./src/components/auth/auth.vue')['default']
    AuthAll: typeof import('./src/components/auth/authAll.vue')['default']
    Auths: typeof import('./src/components/auth/auths.vue')['default']
    AvatarSelector: typeof import('./src/components/avatarSelector/index.vue')['default']
    BatchDeleteObjs: typeof import('./src/components/batchDeleteObjs/index.vue')['default']
    ChartBar: typeof import('./src/components/chaosCharts/chartBar.vue')['default']
    ChartCalendarHeatMap: typeof import('./src/components/chaosCharts/chartCalendarHeatMap.vue')['default']
    ChartChinaMap: typeof import('./src/components/chaosCharts/chartChinaMap.vue')['default']
    ChartDescription: typeof import('./src/components/chaosCharts/chartDescription.vue')['default']
    ChartGauge: typeof import('./src/components/chaosCharts/chartGauge.vue')['default']
    ChartLine: typeof import('./src/components/chaosCharts/chartLine.vue')['default']
    ChartPie: typeof import('./src/components/chaosCharts/chartPie.vue')['default']
    ChartStatistics: typeof import('./src/components/chaosCharts/chartStatistics.vue')['default']
    Cropper: typeof import('./src/components/cropper/index.vue')['default']
    CustomTransfer: typeof import('./src/components/customTransfer/index.vue')['default']
    DeptFormat: typeof import('./src/components/dept-format/index.vue')['default']
    DvaSelect: typeof import('./src/components/dvaSelect/index.vue')['default']
    Editor: typeof import('./src/components/editor/index.vue')['default']
    ERCharts: typeof import('./src/components/chaosG6/ERCharts.vue')['default']
    Example: typeof import('./src/components/customTransfer/example.vue')['default']
    Export2PDF: typeof import('./src/components/export2PDF/index.vue')['default']
    ForeignKey: typeof import('./src/components/foreignKey/index.vue')['default']
    FsTransfer: typeof import('./src/components/customFastCrud/fsTransfer/index.vue')['default']
    IconSelector: typeof import('./src/components/iconSelector/index.vue')['default']
    ImportExcel: typeof import('./src/components/importExcel/index.vue')['default']
    Indexv1: typeof import('./src/components/portal/base/listPage/indexv1.vue')['default']
    List: typeof import('./src/components/iconSelector/list.vue')['default']
    ListPage: typeof import('./src/components/portal/base/listPage/index.vue')['default']
    LogViewer: typeof import('./src/components/logViewer/LogViewer.vue')['default']
    ManyToMany: typeof import('./src/components/manyToMany/index.vue')['default']
    NavigationBar: typeof import('./src/components/portal/base/NavigationBar/index.vue')['default']
    Network: typeof import('./src/components/chaosG6/network.vue')['default']
    NoticeBar: typeof import('./src/components/noticeBar/index.vue')['default']
    OnlyOffice: typeof import('./src/components/onlyoffice/OnlyOffice.vue')['default']
    Pagination: typeof import('./src/components/Pagination/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./src/components/svgIcon/index.vue')['default']
    Table: typeof import('./src/components/table/index.vue')['default']
    TableMultipleSelector: typeof import('./src/components/tableMultipleSelector/index.vue')['default']
    TableSignleSelector: typeof import('./src/components/tableSignleSelector/index.vue')['default']
    TableSplitIdValueSelector: typeof import('./src/components/tableSplitIdValueSelector/index.vue')['default']
    TinyCard: typeof import('@opentiny/vue')['Card']
    Webssh: typeof import('./src/components/webssh/index.vue')['default']
    XtermLogViewer: typeof import('./src/components/logViewer/XtermLogViewer.vue')['default']
    XTermLogViewer: typeof import('./src/components/myXterm/XTermLogViewer.vue')['default']
  }
}
