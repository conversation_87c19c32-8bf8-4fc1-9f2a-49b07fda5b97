<template>
  <fs-page>
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <!-- 可以在这里添加自定义的操作按钮 -->
    </fs-crud>
  </fs-page>
</template>

<script lang="ts" setup name="virtualSwitchPhysicalInterface">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { useRoute } from 'vue-router';

const route = useRoute();
const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
	const requestQuery = route.query;
	if (requestQuery.virtual_switch) {
		crudExpose.doSearch({ form: { virtual_switch: requestQuery.virtual_switch } });
	} else {
		crudExpose.doRefresh();
	}
});
</script>
