<template>
	<fs-page>
		<div class="m-5">
			<el-row>
				<el-col :span="7">
					<el-card header="主机清单" style="height:45%">
						<div>
							<el-button
								v-if="item.execHostList.length == 0"
								type="primary"
								plain
								@click="hostDialogVisible = true"
							>选择主机</el-button>
							<el-button v-else type="primary" plain @click="hostDialogVisible = true">
								已选择
								<el-text size="large">{{ item.execHostList.length }}</el-text>台主机
							</el-button>
							<el-card>
								<el-table scrollbar-always-on :data="filterHostData" style="width: 100%" height="200">
									<el-table-column prop="ip">
										<template #header>
											<el-input v-model="search" style="width:150px" placeholder="搜索IP"></el-input>
										</template>
									</el-table-column>
									<el-table-column fixed="right" width="140px" label="操作">
										<template #default="scope">
											<el-button link type="warning" @click="deleteTableRow(scope.$index)">删除</el-button>
										</template>
									</el-table-column>
								</el-table>
							</el-card>
						</div>
					</el-card>
					<el-card header="模板清单" style="height:55%">
						<div style="height:285px">
							<el-select
								v-model="execSelectionTempModel"
								value-key="id"
								placeholder="选择执行模板"
								style="width:100%;margin-bottom:10px"
								filterable
								size="large"
							>
								<el-option
									v-for="item in templateList"
									:key="item.id"
									:label="item.description ? item.name + '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + item.description : item.name"
									:value="item"
								/>
							</el-select>
							<el-input type="textarea" v-model="item.description" placeholder="填写任务描述(必填)"></el-input>
							<el-descriptions title="配置参数" style="margin-top:15px" :column="1" border>
								<el-descriptions-item
									:width="100"
									v-for="envObj in item.execSelectionTemp.params"
									:key="envObj.key"
									:label="envObj.desc ? envObj.key + '(' + envObj.desc + ')' : envObj.key"
								>
									<el-input v-model="envObj.value"></el-input>
								</el-descriptions-item>
							</el-descriptions>
						</div>
						<template #footer>
							<el-button type="primary" size="large" plain @click="runExec" v-if="auth('scheduleTask:work:startExec')">开始执行</el-button>
						</template>
					</el-card>
				</el-col>
				<el-col :span="17">
					<el-card style="height: 855px">
						<template #header>
							<div class="card-header">
								<div style="width:60px">任务列表</div>
								<el-tooltip placement="top">
									<template #content>
										任务创建流程
										<br />方式一：选择主机 -> 选择模板 -> 填写任务描述、修改参数值(可选) -> 点击开始执行
										<br />方式二：对已有任务点击`重新执行` -> 修改描述和参数值(可选) -> 点击开始执行
									</template>
									<el-icon size="20">
										<QuestionFilled />
									</el-icon>
								</el-tooltip>
							</div>
						</template>
						<fs-crud ref="crudRefLocal" v-bind="crudBindingLocal">
							<template #cell_host_list="scope">
								<el-tag type="info" v-for="(host, index) in scope.row.host_list" :key="index">{{ host }}</el-tag>
							</template>
							<template #cell_params="scope">
								<el-tag
									type="info"
									v-for="(key, value) in scope.row.params"
									:key="key"
								>{{ value }} -- {{ key }}</el-tag>
							</template>
							<template #cell_status="scope">
								<el-tag :type="workRunStatuMap[scope.row.status].type">
									{{ workRunStatuMap[scope.row.status].msg ||
									'未知状态' }}
								</el-tag>
							</template>
							<template #cell-rowHandle-right="scope">
								<el-button type="primary" link @click="openWorkLog(scope.row)">日志</el-button>
								<el-button type="primary" link @click="handleApplyWork(scope.row)">重新执行</el-button>
							</template>
						</fs-crud>
					</el-card>
				</el-col>
			</el-row>
		</div>
		<div>
			<!-- 选择主机表单 -->
			<el-drawer v-model="hostDialogVisible" :show-close="false" size="70%">
				<fs-page>
					<fs-crud ref="crudRefComponent" v-bind="crudBindingComponent">
						<template #actionbar-right>
							<el-button type="primary" plain @click="handleSelectHost" size="large">提交主机</el-button>
						</template>
					</fs-crud>
				</fs-page>
			</el-drawer>
		</div>
		<div>
			<!-- 日志显示表单 会话版本-->
			<el-dialog
				style="width:50%;height:85%;"
				v-model="journalDialogVisible"
				title="执行日志"
				:before-close="beforeDialogClose"
				center
			>
				<XtermLogViewer
					:logData="logContentMap"
					:closeTerminal="journalDialogVisible"
					fontSize="16"
					:isAnsiColorFixed="true"
				/>
			</el-dialog>
			<!-- 日志显示表单 抽屉版本-->
			<!-- <el-drawer v-model="journalDialogVisible" :show-close="false" size="40%">
				<template #header="{ close, titleId, titleClass }">
					<h4 :id="titleId" :class="titleClass">任务日志</h4>
				</template>
				<LogViewer :logData="logContentMap" />
			</el-drawer>-->
		</div>
	</fs-page>
</template>

<script lang="ts" setup name="areas">
import { onMounted, ref, reactive, toRefs, computed } from 'vue';
import { useFs, useExpose } from '@fast-crud/fast-crud';
import { createCrudOptions as createCrudOptionsLocal } from './crud';
import { createCrudOptions as createCrudOptionsComponent } from '/@/views/operatorCMDB/host/crud';
import { auth } from '/@/utils/authFunction';
import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';
import XtermLogViewer from '/@/components/logViewer/XtermLogViewer.vue';
// import LogViewer from '/@/components/logViewer/LogViewer.vue';
import * as api from '/@/api/scheduletask/template';
import * as workApi from '/@/api/scheduletask/work';

const route = useRoute();
const { crudBinding: crudBindingLocal, crudRef: crudRefLocal, crudExpose: crudExposeLocal } = useFs({ createCrudOptions: createCrudOptionsLocal });
const {
	crudBinding: crudBindingComponent,
	crudRef: crudRefComponent,
	crudExpose: crudExposeComponent,
	selectedHosts,
} = useFs({ createCrudOptions: createCrudOptionsComponent });

// const crudOptions = createCrudOptionsComponent({ crudExpose: crudExposeComponent });
const hostDialogVisible = ref(false);
const tempDialogVisible = ref(false);
const journalDialogVisible = ref(false);
const activeNames = ref(['']);
const search = ref('');
let curWs = ref();

const data = reactive({
	templateList: [],
	hostList: [],
	item: {
		execSelectionTemp: {},
		execHostList: [],
		execParams: {},
		work_id: 'work-b3e6a7679190402f96320f47d436c0c0',
		description: '',
	},
	logContentMap: {},
	workRunStatuMap: {
		0: {
			msg: '待执行',
			type: 'info',
		},
		1: {
			msg: '执行中',
			type: 'primary',
		},
		2: {
			msg: '成功',
			type: 'success',
		},
		3: {
			msg: '失败',
			type: 'danger',
		},
	},
	options: {
		hostSelectionList: [],
		tempSelectionList: [],
	},
});

const filterHostData = computed(() =>
	data.item.execHostList.filter((item) => !search.value || item.ip.toLowerCase().includes(search.value.toLowerCase()))
);

let execSelectionTempModel = computed({
	get() {
		return Object.keys(data.item.execSelectionTemp).length === 0 ? null : data.item.execSelectionTemp;
	},
	set(value) {
		data.item.execSelectionTemp = value;
	},
});

// 删除行的方法
const deleteTableRow = (index) => {
	data.item.execHostList.splice(index, 1);
};

const handleApplyWork = (row) => {
	workApi.ApplyWork(row).then((response) => {
		if (response.code === 2000) {
			data.item.execSelectionTemp = response.data.template_info;
			data.item.execHostList = response.data.host_info_list.map((item) => ({
				...item,
				ip: item.ip_private,
			}));
			data.item.description = row.description;
			// 应用任务执行参数
			if (data.item.execSelectionTemp.params.length > 0) {
				data.item.execSelectionTemp.params.forEach((item) => {
					if (row.params.hasOwnProperty(item.key)) {
						item.value = row.params[item.key];
					}
				});
			}

			ElMessage.success(response.msg);
		} else {
			ElMessage.error(response.msg);
		}
	});
};

const handleSelectHost = () => {
	data.item.execHostList = selectedHosts.value;
	hostDialogVisible.value = false;
};

const beforeDialogClose = () => {
	closeWS(curWs.value);
	journalDialogVisible.value = false;
};

const openWorkLog = (row) => {
	data.logContentMap.msg = '';
	data.item.work_id = row.id;
	const newWs = createWS();
	curWs.value = newWs;
	journalDialogVisible.value = true;
};

const runExec = () => {
	if (!data.item.execSelectionTemp.id || data.item.execHostList.length == 0) {
		ElMessage.error('请先选择主机和模板,再执行任务...');
		return;
	}
	if (!data.item.description) {
		ElMessage.error('请填写描述信息,再执行任务...');
		return;
	}

	const conver_params = data.item.execSelectionTemp.params.reduce((acc, item) => {
		acc[item.key] = item.value;
		return acc;
	}, {});

	const request_data = {
		template_id: data.item.execSelectionTemp.id,
		host_ids: data.item.execHostList.map((item) => item.id),
		params: conver_params,
		description: data.item.description,
	};

	workApi.RunWork(request_data).then((response) => {
		if (response.code === 2000) {
			crudExposeLocal.doRefresh();
			data.item.work_id = response.data;
			data.logContentMap.msg = '';
			const newWs = createWS();
			curWs.value = newWs;
			journalDialogVisible.value = true;
		} else {
			ElMessage.error(response.msg);
		}
	});
};

const closeWS = (ws: any) => {
	ws.close();
};

// 创建websocket
const createWS = () => {
	// 拼接webSocket地址
	let websocketUrl = '';
	// ws wss: ws -> http wss -> https
	// kubeasy.com /
	//             /api
	// 1. npm run sit -> ws://127.0.0.1:8888
	// 2. 部署后 --> 拿到当前的origin
	//                       http -> ws  https -> wss
	let origin = '';
	// 判断是否是development环境
	if (import.meta.env.MODE == 'development') {
		origin = 'ws://************:8000';
	} else {
		// 拿到当前域名
		let temp = window.location.origin + '/api';
		// 把http换成ws
		origin = temp.replaceAll('http', 'ws');
	}
	websocketUrl = `${origin}/work/${data.item.work_id}/`;
	let newWs = new WebSocket(websocketUrl);
	newWs.onopen = function () {
		console.log('链接建立时的事件');
		newWs.send(JSON.stringify('run'));
	};
	newWs.onmessage = function (msgContent) {
		data.logContentMap.time = new Date().toISOString();
		data.logContentMap.msg = msgContent.data;
	};
	newWs.onerror = function () {
		ElMessage({
			type: 'error',
			message: '无法建立WebSocket连接',
		});
		beforeDialogClose();
	};
	newWs.onclose = function () {
		console.log('关闭Socket链接的事件');
	};
	return newWs;
};

const fetchTempData = async () => {
	try {
		const response = await api.GetList({ limit: 10000 });
		data.templateList = response.data;
	} catch (error) {
		ElMessage.error(`获取模板列表失败 错误：${error}`);
	}
};

// 页面打开后获取列表数据
onMounted(() => {
	const requestQuery = route.query;
	if (requestQuery.id) {
		crudExposeLocal.doSearch({ form: { id: requestQuery.id } });
		openWorkLog(requestQuery);
	} else {
		crudExposeLocal.doRefresh();
	}
	crudExposeComponent.doRefresh();
	fetchTempData();

	// 修改actionbar按钮的显示状态
	crudBindingComponent._value.actionbar.buttons.add.show = false;
	crudBindingComponent._value.actionbar.buttons.selectionsDeleted.show = false;
});

const { options, item, templateList, workRunStatuMap, logContentMap } = toRefs(data);
</script> 
<style scoped>
.card-header {
	display: flex;
	flex-direction: row;
	align-items: center;
}
</style>