<template>
  <tiny-card class="asset-overview-card">
    <header class="card-header">
      <h2 class="card-title">资产概况</h2>
    </header>
    
    <div class="stats-container">
      <!-- 使用CSS Grid布局替代TinyRow/TinyCol -->
      <div class="stats-grid">
        <div 
          v-for="(item, index) in statsItems" 
          :key="index"
          class="stat-item"
        >
          <ChartStatistics 
            :data="currentData[item.key]"
            :config="item.config" 
          />
        </div>
        
        <!-- 添加空div保持最后一行对齐（当总数不是4的倍数时） -->
        <div 
          v-for="i in placeholderCount" 
          :key="'placeholder-'+i"
          class="stat-item placeholder"
        ></div>
      </div>
    </div>
  </tiny-card>
</template>
 
<script setup lang="ts">
import { TinyCard } from '@opentiny/vue'
import ChartStatistics from '/@/components/chaosCharts/chartStatistics.vue' 
import { PropType, ref, watchEffect, computed } from 'vue'
 
interface AssetStatistics {
  machine_room_amount: number 
  idc_rack_machine_amount: number 
  physical_server_machine_amount: number 
  buffer_machine_amount: number 
  had_rental_amount: number 
  had_used_amount: number 
  had_used_rate: number 
  had_rental_rate: number 
}

interface StatItem {
  key: keyof AssetStatistics  // 确保 key 只能是 AssetStatistics 的属性名 
  config: {
    name: string
    unit: string 
    tooltip?: string 
  }
}
 
const props = defineProps({
  data: {
    type: Object as PropType<AssetStatistics>,
    required: true,
    default: () => ({
      machine_room_amount: 0,
      idc_rack_machine_amount: 0,
      physical_server_machine_amount: 0,
      buffer_machine_amount: 0,
      had_rental_amount: 0,
      had_used_amount: 0,
      had_used_rate: 0,
      had_rental_rate: 0 
    })
  }
})
 
const currentData = ref<AssetStatistics>(props.data) 
 
const statsItems = computed<StatItem[]>(() => [
  { key: 'machine_room_amount', config: { name: '机房总量', unit: '个' } },
  { key: 'idc_rack_machine_amount', config: { name: 'IDC机柜', unit: '个', tooltip: '在维服务器所属机柜数量' } },
  { key: 'physical_server_machine_amount', config: { name: '物理服务器', unit: '台', tooltip: '在维服务器' } },
  { key: 'buffer_machine_amount', config: { name: 'Buffer池机器', unit: '台', tooltip: 'Buffer资源池(成品机位、机器、电力、网络具备)' } },
  { 
    key: 'had_rental_amount', 
    config: { 
      name: '已租用', 
      unit: '台',
      rate: currentData.value.had_rental_rate  
    } 
  },
  { 
    key: 'had_used_amount', 
    config: { 
      name: '已使用', 
      unit: '台',
      rate: currentData.value.had_used_rate  
    } 
  }
])
 
// 计算需要多少个占位元素（确保总数为4的倍数）
const placeholderCount = computed(() => {
  const remainder = statsItems.value.length  % 4 
  return remainder > 0 ? 4 - remainder : 0 
})
 
watchEffect(() => {
  currentData.value  = props.data  
})
</script>
 
<style scoped>
.asset-overview-card {
  --card-padding: 20px;
  --card-margin: 16px;
  
  width: calc(100% - var(--card-margin) * 2);
  margin: var(--card-margin);
  padding: var(--card-padding);
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}
 
.card-header {
  margin-bottom: 24px;
}
 
.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}
 
.stats-container {
  width: 100%;
}
 
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}
 
.stat-item {
  min-width: 0; /* 防止内容溢出 */
}
 
.placeholder {
  visibility: hidden; /* 隐藏占位元素 */
}
 
/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
 
@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
 
@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .asset-overview-card {
    --card-padding: 16px;
    --card-margin: 8px;
  }
  
  .card-header {
    margin-bottom: 16px;
  }
}
</style>