
<template>
  <el-drawer
  v-model="usersInfoDrawerDialogVisible"
  title="已授权的用户信息"
  :direction="direction"
  size="60%"
  :before-close="handleDrawerClose"
>
<div class="el-table-relative">
  <el-table
  :data="roleUsers"
  :row-class-name="tableRowClassName"
  :border="true"
  :style="{
    width: '100%',
  }"
  height="250">
    <el-table-column prop="id" label="用户ID" />
    <el-table-column prop="username" sortable label="用户名" width="180" />
    <el-table-column prop="name" sortable label="花名" width="180" />
    <el-table-column prop="email" label="邮箱" width="120" show-overflow-tooltip />
    <el-table-column prop="mobile" label="手机号" width="120" show-overflow-tooltip />
  </el-table>
</div>
</el-drawer>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { getRoleUsersInfo } from '/@/views/system/api/user'
import type { DrawerProps } from 'element-plus'


interface User {
  id: number
  username: string
  name: string
  email: string
  mobile: string
}


const props = defineProps({
  roleId: {
    type: Number,
    default: -1
  },
  roleName: {
    type: String,
    default: 'Unknown'
  },
  usersInfoDrawerDialogVisible: {
    type: Boolean,
    default: false
  }
})

let roleUsers = ref([]);

const direction = ref<DrawerProps['direction']>('rtl')


const tableRowClassName = ({
  // eslint-disable-next-line no-unused-vars
  row,
  rowIndex,
}: {
  row: User
  rowIndex: number
}) => {
  if (rowIndex % 2 === 0) {
    return 'warning-row'
  } else {
    return 'success-row'
  }
}

// eslint-disable-next-line vue/no-dupe-keys
let usersInfoDrawerDialogVisible = ref(false)
watch(
    () => props.usersInfoDrawerDialogVisible,
    (val) => {
      usersInfoDrawerDialogVisible.value = val;
      if (val === true) {
        getRoleUsersInfoData();
      }
    }
);
const emit = defineEmits(['update:usersInfoDrawerDialogVisible'])
const handleDrawerClose = () => {
  emit('update:usersInfoDrawerDialogVisible', false);
}


// 获取已授权的资源
const getRoleUsersInfoData = async () => {
    const apiRes = await getRoleUsersInfo({role_id: props.roleId})
    const response = apiRes.data
    roleUsers.value = response
}
onMounted(() => {
  // getModelData();
})
</script>

<style>
/* 相对定位 */
.el-table-relative {
  position: relative;
  padding: 10px;
}
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>