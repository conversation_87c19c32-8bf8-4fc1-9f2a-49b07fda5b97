<template>
  <div>
    <tiny-grid ref="publicIPGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" header-align="center">
      <tiny-grid-column type="index" width="60"></tiny-grid-column>
      <tiny-grid-column type="expand" width="60">
        <template #default="{row}">
          <tiny-grid :data="row.nat_server_policies" align="center">
            <tiny-grid-column type="index" width="24"></tiny-grid-column>
            <tiny-grid-column field="id" title="映射策略名称" width="360">
              <template #default="{row}">
                <div class="id-cell">
                    <span>{{ row.name }}</span>
                    <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.id)"></tiny-link>
                </div>
              </template>
            </tiny-grid-column>
            <tiny-grid-column field="op_server" title="主机" align="center">
            </tiny-grid-column>
            <tiny-grid-column field="public_port" title="公网端口" align="center">
            </tiny-grid-column>
            <tiny-grid-column field="inside_ip" title="内网IP" align="center">
            </tiny-grid-column>
            <tiny-grid-column field="inside_port" title="内网端口" align="center">
            </tiny-grid-column>
            <tiny-grid-column field="protocol" title="协议" align="center">
            </tiny-grid-column>
            <tiny-grid-column field="description" title="描述" align="center">
            </tiny-grid-column>
            <tiny-grid-column title="创建于" align="center" :sortable="true">
              <template #default="{row}">
                {{ formatNow(row.create_datetime) }}
              </template>
            </tiny-grid-column>
          </tiny-grid>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="public_ip" title="公网IP" align="center">
      </tiny-grid-column>
      <tiny-grid-column title="状态" align="center">
        <template #default>
          <tiny-tag type="success">使用中</tiny-tag>
        </template>
      </tiny-grid-column>
      <tiny-grid-column title="创建于" align="center" :sortable="true">
        <template #default="{row}">
          {{ formatNow(row.create_datetime) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="publicIPServer">
import { ref, reactive, toRefs, watch } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyTag,
  TinyImage,
  TinyInput,
  TinyPager,
} from '@opentiny/vue';
import { getPublicIPChildrenList, GetList } from '/@/api/tenant/hisecEngineNatServerPolicy';

import { formatNow } from '/@/utils/formatTime';
import { iconCopy } from '@opentiny/vue-icon';
import { copyText } from '/@/utils/copyText';
import securityGroupImg from '/@/assets/img/security-group.svg';


const props = defineProps({
  projectId: {
    type: String,
    required: true,
    default: ''
  },
  opServer: {
    type: String,
    required: true,
    default: ''
  },
});

const TinyIconCopy = iconCopy();

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input, base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})


// 初始化请求数据
interface FilterOptions {
  id: string;
  public_ip: string;
  name: string;
  op_server: string;
  project_id: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    id: '',
    public_ip: '',
    name: '',
    op_server: props.opServer,
    project_id: props.projectId,
  },
});
let tableData = ref<Array<FilterOptions>>([]);

const publicIPGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  publicIPGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await getPublicIPChildrenList(queryParmas);
    const { data, total } = response;
    tableData.value = data;
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    id: '',
    public_ip: '',
    name: '',
    op_server: props.opServer,
    project_id: props.projectId,
  };
  // reloadGrid();
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  // if (filters)
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
      filterOptions.value.name = filters.filters.name.value.text;
    }
  reloadGrid();
}


watch(() => props.projectId, (newProjectId) => {
  if (newProjectId) {
    filterOptions.value.project_id = newProjectId
    reloadGrid();
  }
});

</script>
<style lang="less" scoped>
.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>