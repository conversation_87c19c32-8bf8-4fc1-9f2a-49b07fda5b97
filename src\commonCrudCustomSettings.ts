import { FsButton } from "@fast-crud/fast-crud";
export  let commentCurdCustomSettings = {
  toolbar:{
    buttons:{
      //查询按钮
      search:{
         show: false,
      },
      // 刷新按钮
      refresh:{
        show: false,
      },
      // 紧凑模式
      compact:{
        show: false,
      },
      // 列设置按钮
      columns:{
        show: true,
        ...FsButton,
        circle: true,
        buttonProps: {
          plain: true,
        },
        icon: 'setting',
        order:1,  // 列排序号，数字越小越靠前排列。 默认值为1, 当配置0或负数则排到最前面，配置2则排到最后面
      },
      // 导出按钮
      export:{
        show: true,
        plain: true,
      },
  }
},
container:{
    is: 'CardCustomLayout',
},
search: {
  container: {
    layout: "multi-line",
    collapseButton: {
      type: 'primary'
    },
    col:{ span: 6}, // 默认列宽度配置
  },
},
}


