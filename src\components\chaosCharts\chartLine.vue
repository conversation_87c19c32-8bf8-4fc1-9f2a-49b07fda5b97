<template>
  <tiny-card custom-class="stat-cards" type="text">
    <div class="chart-header">
      <div class="chart-titles">
        <h1 class="chart-title" v-if="props.config.title">{{props.config.title}}</h1>
        <h2 class="chart-sub-title" v-if="props.config.subTitle">{{props.config.subTitle}}</h2>
      </div>
      <slot name="extra"></slot>
    </div>
    <div>
      <tiny-layout>
        <tiny-row :flex="true">
          <tiny-col>
            <!-- 峰值标记，暂只标记最大值 -->
            <!-- <tiny-chart-line :options="commitServerOccurrenceLineOptions" :extend="extChart" :toolbox="toolbox" :mark-point="markPoint"></tiny-chart-line> -->
            <tiny-chart-line :options="options" :toolbox="toolbox" :extend="extChart" :mark-point="markPoint"></tiny-chart-line>
          </tiny-col>
        </tiny-row>
      </tiny-layout>
    </div>
  </tiny-card>

</template>

<script lang="ts" setup name="ChartLine">
import { ref, watch, PropType, } from 'vue';
import { TinyChartLine, TinyLayout, TinyRow, TinyCol, TinyCard, } from '@opentiny/vue';


const props = defineProps({
  data: {
    type: Array<any>,
    required: true,
    default() {
      return []
    },
  },
  config: {
    type: Object as PropType<{
      title: string;
      subTitle: string;
      xColumn: string;
      xColumnName: string;
      yColumnName: string;
      showMin: boolean;
      showLabel: boolean;
      showSymbol: boolean;
      showMax: boolean;
    }>,
    required: true,
    default() {
      return {
        title: '',
    subTitle: '',
    xColumn: 'x轴值键名',
    xColumnName: 'x轴名称',
    yColumnName: 'y轴名称',
    showMin: false,
    showLabel: true,
    showSymbol: true,
    showMax: true,
      }
    },
  }
});
const showLabel = ref(props.config.showLabel !== false);
const showSymbol = ref(props.config.showSymbol !== false);
const showMax = ref(props.config.showMax !== false);

const markPoint = ref({
  data: <any[]>([])
})


if (props.config.showMin) {
  markPoint.value.data.push(
    {
      name: '最小值',
      type: 'min'
    },
  )
}
if (showMax.value) {
  markPoint.value.data.push(
    {
      name: '最大值',
      type: 'max'
    },
  )
}

const extChart = ref({
  series: [
    {
      showSymbol: showSymbol.value,
      label: {
        show: showLabel.value,
      }
    }
  ]
});

const toolbox = ref({
  feature: {
    dataView: {},
    magicType: { type: ['line', 'bar'] },
    saveAsImage: {}
  }
})

let options: any = ref({
  padding: [50, 30, 50, 20],
  type: 'bar',
  legend: {
    show: true,
    icon: 'line'
  },
  data: props.data,
  xAxis: {
    name: props.config.xColumnName,
    data: props.config.xColumn,

  },
  yAxis: {
    name: props.config.yColumnName,
  }
})


// Watch for props changes and update chart options 
watch(() => props.data, () => {
  options.value.data = props.data
}, { deep: true });
// Watch for props changes and update chart options 
watch(() => props.config, () => {
  options.value.xAxis.data = props.config.xColumn
  options.value.xAxis.name = props.config.xColumnName
  options.value.yAxis.name = props.config.yColumnName
}, { deep: true });
</script>
<style scoped>
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}
.chart-title {
  font-size: 16px;
  font-weight: bold;
  
}
.chart-sub-title {
  font-size: 12px;
  color: gray;
}

/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px);
  /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important;
  /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>