FROM harbor.hzxingzai.cn/chaos/base/chaos-base-web-frontend:3.0.0
WORKDIR /web/
COPY ../. .
# 清除缓存
RUN npm cache clean --force 

# 安装依赖
RUN npm ci --registry=https://registry.npmmirror.com

RUN npm run build:prod

FROM harbor.hzxingzai.cn/tools/nginx_v1:1.22
RUN mkdir -p /code/media/
# 此时在容器内的/web/路径下
COPY ./release/nginx/my.conf /etc/nginx/conf.d/my.conf
COPY --from=0 /web/dist /usr/share/nginx/html
