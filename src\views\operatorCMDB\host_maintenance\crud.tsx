import * as api from '/@/api/operatorCMDB/hostMaintenance';
import { useRouter } from 'vue-router';
import { GetObjHumanInfo as getOPHostDetail } from '/@/api/operatorCMDB/host';
import { UserPageQuery, AddReq, DelReq, EditReq, CreateCrudOptionsProps, CreateCrudOptionsRet, dict, useCompute } from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};
	const { asyncCompute, compute } = useCompute();
	const router = useRouter();
	return {
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			actionbar: {
				buttons: {
					add: {
						show: auth('operatorcmdb:hostMaintenance:Create'),
						plain: true,
						type: 'primary',
					},
					export: {
						show: true,
						text: "查看报表",//按钮文字
						title: "查看报表",//鼠标停留显示的信息
						click() {
							router.push({path: '/report/admin/maintenance-resource-charts',})
						}
					  }
				},
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 180,
				buttons: {
					view: {
						show: auth('operatorcmdb:hostMaintenance:Retrieve'),
						type: 'primary',
						link: true,
					},
					edit: {
						link: true,
						type: 'primary',
						show: auth('operatorcmdb:hostMaintenance:Update'),
					},
					remove: {
						link: true,
						type: 'danger',
						show: auth('operatorcmdb:hostMaintenance:Delete'),
					},
				},
			},
			pagination: {
				show: true,
				'default-page-size': 10,
				'default-current': 1,
			},
			table: {
				rowKey: 'id',
			},
      form: {
        // 具体可配置请参考 grid 布局： http://www.ruanyifeng.com/blog/2019/03/grid-layout-tutorial.html
        display: "grid"
      },
			columns: {
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
						// columnSetDisabled: true, //禁止在列设置中选择
					},
				},
				issue_type: {
					title: '问题类型',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:host_maintenance:issue_type', undefined),
					}),
					column: {
						minWidth: 90,
					},
          form: {
            value: '显卡',
						rules: [
							// 表单校验规则
							{ required: true, message: '问题类型必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入问题类型',
            },
					},
				},
				issue_severity: {
					title: '严重等级',
					search: {
						show: true,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:host_maintenance:issue_severity', undefined),
					}),
					column: {
						minWidth: 90,
					},
          form: {
            value: '低',
						rules: [
							// 表单校验规则
							{ required: true, message: '严重等级必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入严重等级',
						},
					},
				},
				operatorcmdb_host: {
					title: '主机',
					search: {
						show: true,
					},
					column: {
						minWidth: 90,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/operatorcmdb/host/?host_type=裸金属&limit=1000',
						value: 'id',
						label: 'ip_bmc',
						// cache: true,
					}),
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '主机必填项' },
						],
            valueChange: {
								handle({ value, key, form }) {
									console.log('valueChange,', key, value, form);
									console.info(`valueChanged:${key}=${value}`);
									const hostId = form.operatorcmdb_host;
									if (hostId) {
										// 发起请求获取主机详情
										getOPHostDetail(form.operatorcmdb_host).then((res) => {
											const hostInfo = res.data;
											// 格式化主机信息
                      const formattedInfo = `故障基础信息:
城市: ${hostInfo.area || '无'}
机房: ${hostInfo.machine_room_name  || '无'}
机柜&包间: ${hostInfo.idc_rack_machine_name || '无'}
U位: ${hostInfo.u_position || '无'}
SN: ${hostInfo.physical_server_machine_name || '无'}
BMC-IP: ${hostInfo.ip_bmc || '无'}
故障: ${hostInfo.fault || 'GPU掉卡,槽位XX:00'}`;
											// 更新问题描述字段
											form.issue_desc = formattedInfo;
										});
									}
								},
							},
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							rules: [],
							placeholder: '请选择主机',
							
						},
					},
        },
        customer: {
          title: '客户',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
						url: '/api/customer/customer/get_list_by_ids/?is_all=true',
						value: 'id',
						label: 'name',
          }),
          column: {
            minWidth: 90,
            showOverflowTooltip: true,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '客户必填项'},
            ],
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							placeholder: '请选择客户',
						},
          }
        },
				issue_desc: {
					title: '问题描述',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
						showOverflowTooltip: true,
					},
					type: 'textarea',
					form: {
            col: {
              span: 24,
              style: { gridColumn: "span 2", gridRow: "span 2" } // grid 模式控制跨列
            },
						rules: [
							// 表单校验规则
							{ required: true, message: '问题描述必填项' },
						],
						component: {
              placeholder: '请输入问题描述',
              rows: 8
						},
					},
				},
				issue_status: {
					title: '问题状态',
					search: {
						show: true,
					},
					column: {
						minWidth: 90,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:host_maintenance:issue_status', undefined),
					}),
					form: {
						value: '进行中',
						rules: [
							// 表单校验规则
							{ required: true, message: '问题状态必填项' },
						],
						component: {
							placeholder: '请选择问题状态',
						},
					},
				},
				technician: {
					title: '处理人',
					search: {
						show: true,
					},
					column: {
						minWidth: 90,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/system/user/?dept=8&limit=100',
						value: 'id',
						label: 'name',
						// cache: true,
					}),
          form: {
            helper: {
							render() {
								return <div style="color:red">在系统运维组中选择处理人</div>;
							},
            },
            rules: [
              // 表单校验规则
              {required: true, message: '处理人必填项'},
            ],
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							placeholder: '请选择处理人',
						},
					},
				},
				start_resolve_time: {
					title: '开始解决时间',
					search: {
						show: false,
					},
					column: {
						minWidth: 120,
						sortable: 'custom',
					},
					type: 'datetime',
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '开始解决时间必填项' },
						],
						component: {
							placeholder: '请输入开始解决时间',
							valueFormat: 'YYYY-MM-DD HH:mm:ss',
						},
					},
				},
				end_resolve_time: {
					title: '结束解决时间',
					search: {
						show: false,
					},
					column: {
						minWidth: 120,
						sortable: 'custom',
					},
					type: 'datetime',
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '结束解决时间必填项' },
						],
						component: {
							placeholder: '请输入结束解决时间',
							valueFormat: 'YYYY-MM-DD HH:mm:ss',
						},
					},
				},
				downtime_duration: {
					title: '停机时长',
					search: {
						show: false,
					},
					column: {
						minWidth: 90,
						sortable: 'custom',
						cellRender({ row }: { row: any }) {
						if (row.downtime_duration != null && row.downtime_duration_unit) {
							return `${row.downtime_duration} ${row.downtime_duration_unit}`;
						} else if (row.downtime_duration != null) {
							return row.downtime_duration;
						} else {
							return '';
						}
						},
					},
					type: 'number',
					form: {
						value: 0,
						rules: [
							// 表单校验规则
							{ required: false, message: '停机时长必填项' },
						],
						component: {
							placeholder: '请输入停机时长',
							render(context) {
								const options = [
									{ label: '秒', value: '秒' },
									{ label: '分', value: '分' },
									{ label: '时', value: '时' },
								];
								return (
									<div>
										<el-input-number placeholder={'请输入停机时长'} style="width: 80%" v-model={context.form.downtime_duration} />
										<el-select placeholder={'单位'} style="width: 20%" v-model={context.form.downtime_duration_unit}>
											{options.map((item) => (
												<el-option key={item.value} label={item.label} value={item.value} />
											))}
										</el-select>
									</div>
								);
							},
						},
					},
				},
				downtime_duration_unit: {
					title: '停机时长单位',
					search: {
						show: false,
					},
					column: {
						minWidth: 90,
						show: false,
					},
					type: 'input',
					form: {
						show: false,
						value: '秒',
						rules: [
							// 表单校验规则
							{ required: false, message: '停机时长必填项' },
						],
						component: {
							placeholder: '请输入停机时长',
						},
					},
				},
				resolution_desc: {
					title: '解决描述',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
						showOverflowTooltip: true,
					},
					type: 'textarea',
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '解决描述必填项' },
						],
						component: {
							placeholder: '请输入解决描述',
						},
					},
				},
			},
		},
	};
};
