<template>
      <div class="transfer-container">
        <el-transfer
  v-model="rowPermissionData.row_ids"
  filterable
  :titles="['未授权数据', '已授权数据']"
  :button-texts="['取消', '授权']"
  @left-check-change="handleLeftChecked"
  @right-check-change="handleRightChecked"
  :format="{
    noChecked: '${total}',
    hasChecked: '${checked}/${total}',
  }"
  :data="sourceData"
>
  <template #default="{ option }">
    <span>{{ option.label }}</span>
  </template>
  <!--  暂不使用底部按钮 -->
  <!-- <template #left-footer>
    <div class="left-footer-operator">
      <el-button @click="handleLeftChecked" type="primary">
        授权
      </el-button>
    </div>
  </template> -->
  
</el-transfer>
      </div>
</template>

<script setup lang="ts" name="dataToGroup">
import { GetModelRowList, AddRowTargetRole, getRoleRowIds, revokeRoleRowIds } from '/@/api/models/search'
import { TransferKey, ElMessage } from 'element-plus';
import { Reactive, reactive, ref, onMounted } from "vue";

const props = defineProps({
  roleId: {
    type: Number,
    default: -1
  },
  roleOrigin: {
    type: String,
    default: 'Unknown'
  },
  showConfig: {
    type: Object,
    default: () => ({
      key: 'id',
      value: 'name',
    })
  }
})

interface Option {
  key: number
  label: string
  disabled: boolean
}

interface rowPermissionType {
  role_id: number,
  row_ids: any[],
  row_origin: string,
  is_external: boolean,
  
}

let rowPermissionData: Reactive<rowPermissionType> = reactive<rowPermissionType>({
  // [Important]在此处获取时为第一次初始化的默认参数: -1
  role_id: props.roleId,
  row_ids: [],
  row_origin: props.roleOrigin,
  is_external: false
})


const sourceData = ref<Option[]>([])


const getSourceData = async () => {
  if (rowPermissionData.row_origin) {
    const apiRes = await GetModelRowList({
      row_origin: rowPermissionData.row_origin
    })
    const response = apiRes.data
    sourceData.value = []
    let modelKey = props.showConfig.key
    let modelValue = props.showConfig.value
    response.forEach((val: any) => {
    sourceData.value.push({
      key: val[modelKey],
      label: val[modelValue],
      disabled: false
      })
    })
  }
}

// 获取已授权的资源
const getRoleRowIdsData = async () => {
    const apiRes = await getRoleRowIds({role: props.roleId})
    const response = apiRes.data
    rowPermissionData.row_ids = response
}

const handleLeftChecked = (value: TransferKey[], movedKeys: TransferKey[]) => {
  const addData = {
      'row_ids': movedKeys,
      'row_origin': rowPermissionData.row_origin,
      'role_id': props.roleId
    }
  
  AddRowTargetRole(addData).then(
    (res: any) => {
      if (res.code === 2000) {
        ElMessage.success(res.msg)
      }
    }
  )
  const newKeys = movedKeys.filter((key) => !rowPermissionData.row_ids.includes(key));
  rowPermissionData.row_ids = [...rowPermissionData.row_ids, ...newKeys];
}


const handleRightChecked = (value: TransferKey[], movedKeys: TransferKey[]) => {
  const revokeData = {
      'row_ids': movedKeys,
      'row_origin': rowPermissionData.row_origin,
      'role_id': props.roleId
    }
  
  revokeRoleRowIds(revokeData).then(
    (res: any) => {
      if (res.code === 2000) {
        ElMessage.success(res.msg)
      }
    }
  )
  rowPermissionData.row_ids = rowPermissionData.row_ids.filter(key => !movedKeys.includes(key));
}


onMounted(() => {
  getSourceData();
  // 获取已选择的 资源ID 列表
  getRoleRowIdsData();
})
</script>


<style>

/*
* 重构 el-穿梭框 UI
*/
.transfer-container .el-transfer {
  display: flex;
}

.transfer-container .el-transfer__buttons {
  display: none; /* 隐藏中间的按钮 */
}

.transfer-container .el-transfer-panel:first-child {
  width: 500px; 
}
.transfer-container .el-transfer-panel:last-child {
  width: 500px;
  margin-right: 0; /*最后一个不需要右间距*/
}

.transfer-container .el-transfer-panel {
  margin-right: 30px; 
}
.left-footer-operator {
  display: inline-flex;
  align-items: center;
  margin-left: 10px; 
}
 

</style>