<template>
  <div style="display: inline-block">
    <el-button size="default" type="success" @click="handleBatchDeleteObjsDialog">
      <slot>批量删除</slot>
    </el-button>
    <el-dialog
    v-model="batchDeleteObjsConfigDialog"
    title="确定批量删除资源？"
    width="500"
    :before-close="closeBatchDeleteConfigDialog"
  >
    <span>This is a message</span>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="batchDeleteObjsConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="handleMultipleDelete">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
  </div>
</template>

<script lang="ts" setup name="batchDeleteObjs">
import { ref } from "vue";


let props = defineProps({
  selectedIds: {
    type: Array<String>,
    default: () => [],
  },
  api: {
    type: String,
    default: ''
  }
})


let batchDeleteObjsConfigDialog = ref(false)

const handleBatchDeleteObjsDialog = function () {
  batchDeleteObjsConfigDialog.value = true
}

const closeBatchDeleteConfigDialog = function () {
  batchDeleteObjsConfigDialog.value = false
}


const handleMultipleDelete = function () {
  console.error('api:.........', props.api)
  console.error('selectedIds*************', props.selectedIds)
  batchDeleteObjsConfigDialog.value = false
}

</script>

<style scoped>

</style>
