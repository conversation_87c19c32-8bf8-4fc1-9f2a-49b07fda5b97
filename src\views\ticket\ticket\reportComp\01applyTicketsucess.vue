<template>
  <fs-page>
    <div class="contain">
      <tin-row justify="center" align="middle" class="steps-container">
        <tiny-col :span="8" :offset="4">
          <tiny-steps vertical advanced size="large" :data="steps" flex></tiny-steps>
        </tiny-col>
      </tin-row>
      <tin-row justify="center" align="middle" class="text-container">
        <tiny-col :span="8" :offset="4">
          <span class="h1">创建成功</span>
          <span class="h1">
            <tiny-image :src="successfulIcon" fit="scale-down" class="icon-image" />
          </span>
          
        </tiny-col>
      </tin-row>
      <tin-row justify="center" align="middle" class="icon-container">
        <tiny-col :span="8" :offset="4">
          <div class="icon-wrapper">
           
          </div>
        </tiny-col>
      </tin-row>
    </div>
  </fs-page>
</template>

<script setup name="portalServerCreate" lang="ts">
import { reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import successfulIcon from '../../../../assets/img/successful.svg';

const active = ref(0);

// 路由
const router = useRouter();

const pushTicketPage = () => {

};

const steps = ref([
  {
    name: '租户配置',
    key: 'account_config',
    status: 'done',
    active: 0,
  },
  {
    name: '基础配置',
    key: 'basical_config',
    status: 'done',
    active: 1,
  },
  {
    name: '主机配置',
    key: 'host_config',
    status: 'done',
    active: 2,
  },
]);
</script>

<style scoped lang="less">
.contain {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  height: 100%;
  padding: 20px; /* 添加内边距以避免内容紧贴边缘 */
}

.steps-container,
.text-container,
.icon-container {
  width: 100%;
  margin-bottom: 40px; /* 在每个容器之间添加一些间距 */
}

.h1 {
  font-size: 48px;
  font-weight: 600;
  text-align: center;
  height: 48px;
}

.icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100px;
}

.icon-image {
  max-width: 48px;
  max-height: 48px;
}
</style>