<template>
  <div class="webssh-container">
    <form @submit.prevent="connectSSH">
      <label for="host">Host:</label>
      <input type="text" id="host" v-model="sshInfo.host" required />

      <label for="user">User:</label>
      <input type="text" id="user" v-model="sshInfo.user" required />

      <label for="password">Password:</label>
      <input type="password" id="password" v-model="sshInfo.password" />

      <label for="port">Port:</label>
      <input type="number" id="port" v-model="sshInfo.port" required />

      <button type="submit">Connect</button>
    </form>

    <div ref="terminal" class="terminal"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue'
import { Terminal } from '@xterm/xterm'
import { FitAddon } from '@xterm/addon-fit';
import { ClipboardAddon } from '@xterm/addon-clipboard';
import { SearchAddon } from '@xterm/addon-search';
import 'xterm/css/xterm.css'

export default defineComponent({
  setup() {
    const terminal = ref<HTMLElement | null>(null)
    const fitAddon = new FitAddon()
    const clipboardAddon = new ClipboardAddon();
    const searchAddon = new SearchAddon();
    let terminalSocket: WebSocket | null = null
    let term: Terminal | null = null
    const sshInfo = ref({
      host: '***********',
      user: 'root',
      password: 'xingzai!@#456',
      port: 22,
      id: 'your-session-id', // 确保有正确的 id 参数
    })

    // 初始化 WebSocket
    const initWS = () => {
      if (!terminalSocket || (terminalSocket && terminalSocket.readyState > 1)) {
        createWS()
      }
    }

    // 创建 WebSocket 连接
    const createWS = () => {
      const wsUrl = `ws://127.0.0.1:8000/webssh/?id=${encodeURIComponent(sshInfo.value.id)}`
      terminalSocket = new WebSocket(wsUrl)

      terminalSocket.binaryType = 'arraybuffer'  // 设置为 arraybuffer 类型

      terminalSocket.onopen = () => {
        console.log('WebSocket Connected')
        runRealTerminal()
      }

      terminalSocket.onmessage = onWSReceive

      terminalSocket.onerror = (error) => {
        console.error('WebSocket Error:', error)
        errorRealTerminal(error)
      }

      terminalSocket.onclose = (event) => {
        console.log('WebSocket Closed with code:', event.code)
        closeRealTerminal()
      }
    }

    // WebSocket 连接已建立
    const runRealTerminal = () => {
      sendSSHInfo()
    }

    // 发送 SSH 连接信息到后端
    const sendSSHInfo = () => {
      if (isWsOpen()) {
        terminalSocket?.send(
          JSON.stringify({
            type: 'ssh_info',
            data: {
              host: sshInfo.value.host,
              username: sshInfo.value.user,
              password: sshInfo.value.password,
              port: sshInfo.value.port,
              type: 0,
              pkey: null,
            },
          })
        )
      }
    }

    // WebSocket 收到服务器消息
    const onWSReceive = (message: MessageEvent) => {
      const data = message.data
      if (data instanceof ArrayBuffer) {
        const uint8Array = new Uint8Array(data)
        const decoder = new TextDecoder('utf-8')
        const decodedData = decoder.decode(uint8Array)
        term?.write(decodedData)
      } else if (typeof data === 'string') {
        term?.write(data)
      }
      term?.element && term?.focus()
    }

    // WebSocket 连接出错
    const errorRealTerminal = (ex: Event) => {
      let message = (ex as ErrorEvent).message || 'disconnected'
      term?.write(`\x1b[31m${message}\x1b[m\r\n`)
      console.error('WebSocket Error:', message)
    }

    // WebSocket 连接已关闭
    const closeRealTerminal = () => {
      console.log('WebSocket Closed')
    }

    // 初始化 Terminal
    const initTerm = () => {
      term = new Terminal({
        fontSize: 14,
        fontFamily: "Monaco, Menlo, Consolas, 'Courier New', monospace",
        theme: {
          background: '#181d28',
        },
        cursorBlink: true,
        cursorStyle: 'underline',
      })
      term.open(terminal.value!) // 挂载 DOM 窗口
      term.loadAddon(fitAddon); // 加载自适应插件
      term.loadAddon(clipboardAddon); // 加载访问浏览器的剪贴板
      term.loadAddon(searchAddon); // 加载搜索缓冲区
      searchAddon.findNext('foo'); // 加载搜索缓冲区
      fitAddon.fit(); // 自适应大小
      termData() // 终端事件挂载
    }

    // 终端输入触发事件
    const termData = () => {
      term?.onData((data) => {
        if (isWsOpen()) {
          terminalSocket?.send(
            JSON.stringify({
              type: 'terminal',
              data: {
                base64: btoa(data),
              },
            })
          )
        }
      })

      term?.onResize(({ cols, rows }) => {
        resizeRemoteTerminal(cols, rows)
      })
    }

    // 尺寸同步，发送给后端调整终端大小
    const resizeRemoteTerminal = (cols: number, rows: number) => {
      if (isWsOpen()) {
        terminalSocket?.send(
          JSON.stringify({
            type: 'resize',
            data: {
              rows: rows,
              cols: cols,
            },
          })
        )
      }
    }

    // 判断 WebSocket 是否打开
    const isWsOpen = (): boolean => {
      return !!terminalSocket && terminalSocket.readyState === WebSocket.OPEN
    }

    // 连接 SSH
    const connectSSH = () => {
      initWS()
    }

    onMounted(() => {
      initTerm()
      window.addEventListener('resize', () => fitAddon.fit())
    })

    onBeforeUnmount(() => {
      window.removeEventListener('resize', () => fitAddon.fit())
      terminalSocket?.close()
    })

    return {
      terminal,
      sshInfo,
      connectSSH,
    }
  },
})
</script>

<style lang="scss" scoped>
.webssh-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  form {
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    label {
      min-width: 80px;
    }

    input {
      flex: 1;
    }

    button {
      margin-top: 10px;
    }
  }

  .terminal {
    width: 100%;
    height: calc(100vh - 100px);
  }
}
</style>