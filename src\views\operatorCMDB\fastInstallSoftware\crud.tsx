import * as api from '/@/api/operatorCMDB/fastInstallSoftware';
import {
	UserPageQuery,
	AddReq,
	DelReq,
	EditReq,
	CreateCrudOptionsProps,
	CreateCrudOptionsRet,
	dict,
	ValueBuilderContext,
	ValueResolveContext,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { verifyKey } from '/@/utils/toolsValidate';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};
	const validateKey = async (rule: object, value: string) => {
		if (!verifyKey(value)) {
			throw new Error('仅允许英文及—_');
		}
	};

	return {
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			actionbar: {
				buttons: {
					add: {
						show: auth('operatorcmdb:fastInstallSoftware:Create'),
						plain: true,
						type: 'primary',
					},
				},
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 180,
				buttons: {
					view: {
						show: auth('operatorcmdb:fastInstallSoftware:Retrieve'),
						type: 'primary',
						link: true,
					},
					edit: {
						link: true,
						type: 'primary',
						show: auth('operatorcmdb:fastInstallSoftware:Update'),
					},
					remove: {
						link: true,
						type: 'danger',
						show: auth('operatorcmdb:fastInstallSoftware:Delete'),
					},
				},
			},
			pagination: {
				show: true,
				'default-page-size': 10,
				'default-current': 1,
			},
			table: {
				rowKey: 'id',
			},
			columns: {
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
						// columnSetDisabled: true, //禁止在列设置中选择
					},
				},
				name: {
					title: '软件名称',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'input',
					column: {
						minWidth: 100,
						showOverflowTooltip: true,
						sortable: 'custom',
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '软件名称必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入软件名称',
						},
					},
				},
				key: {
					title: '软件简称',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 100,
						showOverflowTooltip: true,
						sortable: 'custom',
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '软件简称必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
							{ validator: validateKey, message: '仅允许英文及_-', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入软件简称',
						},
					},
				},
				description: {
					title: '描述',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 100,
						showOverflowTooltip: true,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '描述必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入描述',
						},
					},
				},
				category: {
					title: '类型',
					search: {
						show: true,
					},
					column: {
						minWidth: 90,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:fastInstallSoftware:category', undefined),
					}),
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '软件类型必填项' },
						],
						component: {
							placeholder: '请输入软件类型',
						},
					},
				},
				// version: {
				// 	title: '版本',
				// 	search: {
				// 		show: true,
				// 	},
				// 	column: {
				// 		minWidth: 60,
				// 	},
				// 	type: 'input',
				// 	form: {
				// 		rules: [
				// 			// 表单校验规则
				// 			{ required: true, message: '版本必填项' },
				// 		],
				// 		component: {
				// 			placeholder: '请输入版本',
				// 		},
				// 	},
				// },
				// versions: {
				//   title: '版本',
				//   // type: 'json',
				//   form: {
				//     component: {
				//       name: 'fs-json-editor',
				//       showBtns: true,
				//       modes: ['tree', 'code', 'view'],
				//       // mode: 'tree',
				//       // schema: {
				//       //   type: 'object',
				//       //   properties: {
				//       //     key: { type:'string' },
				//       //     value: { type:'string' },
				//       //   },
				//       // },
				//       //   required: ['key', 'value'],
				//       // },
				//       rules: [
				//         // 表单校验规则
				//         // {required: false, message: '参数配置必填项'},
				//       ],
				//     },
				//     valueBuilder({ form }: ValueBuilderContext) {
				//       if (form.versions == null) {
				//         return;
				//       }
				//       form.versions = JSON.parse(form.versions);
				//     },
				//     valueResolve({ form }: ValueResolveContext) {
				//       if (form.versions == null) {
				//         return;
				//       }
				//       form.versions = JSON.stringify(form.versions);
				//     }
				//   }
				// },
				ansible_template: {
					title: '模版',
					search: {
						show: true,
					},
					column: {
						minWidth: 180,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/scheduletask/template/get_list_by_ids/?is_all=true',
						value: 'id',
						label: 'name',
						// cache: true,
					}),
					form: {
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							rules: [
								// 表单校验规则
								// {required: false, message: '机房必填项'},
							],
							placeholder: '请选择模版',
						},
					},
				},
				enabled: {
					title: '启用',
					search: {
						show: true,
					},
					column: {
						minWidth: 60,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('button_whether_bool', undefined),
					}),
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '启用状态必填项' },
						],
						component: {
							placeholder: '请选择启用状态',
						},
					},
				},
				status: {
					title: '软件状态',
					search: {
						show: true,
					},
					column: {
						minWidth: 90,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:fastInstallSoftware:status', undefined),
					}),
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '软件状态必填项' },
						],
						component: {
							placeholder: '请输入软件状态',
						},
					},
				},
				adaptation_system_version: {
					title: '适配系统版本',
					search: {
						show: false,
					},
					column: {
						minWidth: 90,
						show: true,
					},
					type: 'input',
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '适配系统版本必填项' },
						],
						component: {
							placeholder: '请输入适配系统版本',
						},
					},
				},
				is_support_repeat: {
					title: '支持重试',
					search: {
						show: true,
					},
					column: {
						minWidth: 60,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('button_whether_bool', undefined),
					}),
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '是否支持重试必填项' },
						],
						component: {
							placeholder: '请选择是否支持重试',
						},
					},
				},
				install_path: {
					title: '安装路径',
					search: {
						show: false,
					},
					column: {
						minWidth: 90,
						sortable: 'custom',
						show: false,
					},
					type: 'input',
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '安装路径必填项' },
						],
						component: {
							placeholder: '请输入安装路径',
						},
					},
				},
        versions: {
					title: '版本配置',
					type: 'json',
					form: {
            component: {
              name: 'fs-json-editor',
              showBtns: false,
              mode: 'code',
              rules: [
                // 表单校验规则
                // {required: false, message: '参数配置必填项'},
              ],
            },
            valueBuilder({ form }: ValueBuilderContext) {
              if (form.versions == null) {
                return;
              }
              form.versions = JSON.parse(form.versions);
            },
						valueResolve({ form }: ValueResolveContext) {
							if (form.versions == null) {
								return;
							}
							form.versions = JSON.stringify(form.versions);
						},
					},
				},
        options: {
					title: '任务参数配置',
					type: 'json',
					form: {
            component: {
              name: 'fs-json-editor',
              showBtns: false,
              mode: 'code',
              rules: [
                // 表单校验规则
                // {required: false, message: '参数配置必填项'},
              ],
            },
            valueBuilder({ form }: ValueBuilderContext) {
              if (form.options == null) {
                return;
              }
              form.options = JSON.parse(form.options);
            },
						valueResolve({ form }: ValueResolveContext) {
							if (form.options == null) {
								return;
							}
							form.options = JSON.stringify(form.options);
						},
					},
				},
			},
		},
	};
};
