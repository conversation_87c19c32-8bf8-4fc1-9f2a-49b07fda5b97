/* src/styles/global.css 或 src/assets/css/main.css */

/* @font-face {
  font-family: 'dingtalkFontFamily';
  src: url('dingtalkFont/DingTalk-JinBuTi.woff2') format('woff2'),
       url('dingtalkFont/DingTalk-JinBuTi.woff') format('woff'),
       url('dingtalkFont/DingTalk-JinBuTi.ttf') format('truetype');
} */

@font-face {
  font-family: 'harmonyosScans';
  src: url('harmonyos_scans/harmonyosScans.woff2') format('woff2'),
  url('harmonyos_scans/harmonyosScans.woff') format('woff'),
  url('harmonyos_scans/harmonyosScans.ttf') format('truetype');
}

.el-dialog {
  border-radius: 8px; /* 设置边框圆角 */
  border: 1px solid #e1e1e1; /* 设置边框样式 */
}

body,
#app {
  font-family:'harmonyosScans', Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;; /* 设置全局字体为harmonyos */
}
