<template>
  <fs-page>
    <fs-crud ref="crudRef" v-bind="crudBinding">
    </fs-crud>
  </fs-page>
</template>

<script lang="ts" setup name="personalMessageCenter">
import {ref, onMounted} from 'vue';
import {useExpose, useCrud} from '@fast-crud/fast-crud';
import {createCrudOptions} from './crud';
// crud组件的ref
const crudRef = ref();
// crud 配置的ref
const crudBinding = ref();
// 暴露的方法
const {crudExpose} = useExpose({crudRef, crudBinding});

// 你的crud配置
const {crudOptions} = createCrudOptions({crudExpose});
// 初始化crud配置
// @ts-ignore
// eslint-disable-next-line no-unused-vars
const { resetCrudOptions } = useCrud({crudExpose, crudOptions});

// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
});


</script>
