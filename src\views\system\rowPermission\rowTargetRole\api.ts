import { request } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/system/row_permission/';
export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

// 获取 model -> 行数据
export function GetModelRowList(query: UserPageQuery) {
	return request({
		url: apiPrefix + 'get_rows_info/',
		method: 'get',
		params: query,
	});
}

export function AddRowTargetUser(data: AddReq) {
  return request({
    url: apiPrefix + 'add_row_target_user/',
    method: 'post',
    data: data,
  })
}

export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + id,
		method: 'get',
	});
}

export function AddObj(obj: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

export function UpdateObj(obj: EditReq) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export function DelObj(id: DelReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

/*
获取所有的model及字段信息
 */
export function GetAssociationTable() {
	return request({
		url: '/api/system/column/get_models/',
		method: 'get',
		params: {},
	});
}


export function GetUserList(query: UserPageQuery) {
  return request({
      url: '/api/system/user/',
      method: 'get',
      params: query,
  });
}


export function saveContent(data: any) {
	return request({
		url: apiPrefix + 'save_content/',
		method: 'put',
		data: data,
	});
}
