<template>
  <fs-page>
    <template #header>
      <div class="new-title">
        <!-- <tiny-steps vertical advanced size="large" :data="steps" :active="active" flex @click="stepsClick"></tiny-steps> -->
        <!-- 此页面关闭点击步骤跳转-->
        <tiny-steps vertical advanced size="large" :data="steps" :active="active" flex></tiny-steps>
      </div>
    </template>
    <div id="container-list" class="container-list">

      <div class="contain">
        <div id="account_config" class="create-step-container" v-if="active === 0">
          <div class="container-step-head">
            <span>租户配置</span>
          </div>
          <tiny-form ref="createServerAccountFormValid" :inline="false" label-position="left" label-width="150px"
            style="border-radius: 0px;" :model="createServerFormData" :rules="createServerAccountFormRules">
            <tiny-form-item label="选择租户" prop="account_id">
              <span
                style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px;">选择已有租户，则创建此租户下的服务器；若无租户，请联系管理员。</span>
              <SelectAccountForm v-model:current-select-account-id="createServerFormData.account_id">
              </SelectAccountForm>
            </tiny-form-item>
          </tiny-form>
          <tiny-divider></tiny-divider>
          <div style="
      display: flex;
      justify-content: center;
      justify-items: center;
      margin: 20px;
      ">
            <tiny-button type="primary" @click="prevStep" :disabled="true">上一步</tiny-button>
            <tiny-button type="primary" @click="active0NextStep">下一步</tiny-button>
          </div>
        </div>
        <div id="basical_config" class="create-step-container" v-if="active === 1">
          <div class="container-step-head">
            <span>基础配置</span>
          </div>
          <tiny-form ref="createServerBasicalFormVaild" :model="createServerFormData" :inline="false"
            label-position="left" label-width="150px" :label-align="true" style="border-radius: 0px"
            :rules="createServerBasicalFormRules">
            <tiny-form-item label="区域" prop="node">
              <tiny-button-group v-model="createServerFormData
            .node" style="border-radius: 0px; margin-right: 10px"
              :data="allNodes" :text-field="nodeValueField" :value-field="nodeValueField"></tiny-button-group>
              <span
                style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">不同区域的云服务产品之间内网互不相通；请就近选择靠近您业务的区域，可减少网络时延，提高访问速度。</span>
            </tiny-form-item>
            <tiny-form-item label="主机类型" prop="instance_type">
              <tiny-button-group v-model="createServerFormData.instance_type"
                style="border-radius: 0px; margin-right: 10px"
                :data="[{ text: '裸金属', value: '裸金属' }, { text: '云主机', value: '云主机' }]"></tiny-button-group>
            </tiny-form-item>
            <tiny-form-item label="主机名称" prop="name">
              <tiny-input placeholder="请输入" v-model="createServerFormData.name" style="width: 40%; margin-right: 10px"
                :maxlength="63" show-word-limit></tiny-input>
            </tiny-form-item>
            <tiny-form-item label="主机描述" prop="description">
              <tiny-input placeholder="请输入" type="textarea" v-model="createServerFormData.description"
                style="width: 40%; margin-right: 10px" :maxlength="255" show-word-limit></tiny-input>
            </tiny-form-item>
            <tiny-form-item label="分配公网" prop="is_need_extra_public_ip">
              <tiny-radio-group v-model="createServerFormData.is_need_extra_public_ip">
                <tiny-radio :label="true">是</tiny-radio>
                <tiny-radio :label="false">否</tiny-radio>
              </tiny-radio-group>
              <span
              style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">开通公网后，默认开放规则为<span style="color: #E6A23C">IP绑定</span>,即访问{公网IP}:{内网端口}。
            </span>
            </tiny-form-item>
            <tiny-form-item label="公网IP数量" prop="count" v-if="createServerFormData.is_need_extra_public_ip === true">
              <tiny-input type="number" placeholder="请输入" v-model="createServerFormData.count"
                style="width: 40%; margin-right: 10px" :disabled="true"></tiny-input>
            </tiny-form-item>
            <tiny-form-item label="过期时间" prop="expire_at">
              <tiny-date-picker v-model="createServerFormData.expire_at" :picker-options="pickerOptions" type="datetime"
                placeholder="请选择过期日期" default-time="23:59:59" style="width: 40%; margin-right: 10px"
                value-format="yyyy-MM-dd HH:mm:ss"></tiny-date-picker>
            </tiny-form-item>
            <tiny-form-item label="创建数量" prop="count">
              <tiny-input type="number" placeholder="请输入" v-model="createServerFormData.count"
                style="width: 40%; margin-right: 10px" :step="1" :min="1" :max="10"></tiny-input>
            </tiny-form-item>
          </tiny-form>
          <tiny-divider></tiny-divider>
          <div style="
      display: flex;
      justify-content: center;
      justify-items: center;
      margin: 20px;
      ">
            <tiny-button type="primary" @click="prevStep">上一步</tiny-button>
            <tiny-button type="primary" @click="active1NextStep">下一步</tiny-button>
          </div>
        </div>
        <div id="host_config" class="create-step-container" v-if="active === 2">
          <div class="container-step-head">
            <span>主机配置</span>
          </div>
          <tiny-form ref="createServerHostFormValid" :model="createServerFormData" :inline="false" label-position="left"
            label-width="150px" style="border-radius: 0px" :rules="createServerHostFormRules">
            <tiny-form-item label="选择规格" prop="flavor_id">
              <SelectFlavorForm v-model:current-select-flavor-id="createServerFormData.flavor_id" :flavor_type="createServerFormData.instance_type" :node="createServerFormData.node" />
            </tiny-form-item>
            <tiny-form-item label="选择镜像" prop="image_id">
              <SelectImageForm v-model:current-select-image-id="createServerFormData.image_id" :image_type="createServerFormData.instance_type" :node="createServerFormData.node" />
            </tiny-form-item>
            <tiny-form-item label="选择预装软件" prop="software_ids">
              <SelectSoftwareForm v-model:current-select-softwarees="createServerFormData.softwares"/>
            </tiny-form-item>
          </tiny-form>
          <tiny-divider></tiny-divider>
          <div style="
      display: flex;
      justify-content: center;
      justify-items: center;
      margin: 20px;
      ">
            <tiny-button type="primary" @click="prevStep">上一步</tiny-button>
            <tiny-button type="primary" @click="nextStep" :disabled="true">下一步</tiny-button>
          </div>
        </div>
        <div  class="show-footer-button" v-if="active === 2">
          <tiny-button text="提交申请" type="danger" style="max-width: unset" @click="clickCreate"
            :reset-time="5000" v-if="auth('ticket:createBaremetalServerTicket:Create')"></tiny-button>
        </div>
      </div>
    </div>
  </fs-page>
</template>

<script setup name="portalServerCreate" lang="ts">
import { ref, } from 'vue';
import {
  TinyDivider,
  Modal as TinyModal,
  TinyForm,
  TinyButton,
  TinyButtonGroup,
  TinyFormItem,
  TinySteps,
  TinyRadio,
  TinyRadioGroup,
} from '@opentiny/vue';
import SelectFlavorForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectFlavorForm.vue';
import SelectImageForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectImageForm.vue';
import SelectAccountForm from '/@/views/portalTenants/portal/portalAccount/createComp/selectAccountForm.vue';
import SelectSoftwareForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectPreInstallSoftwareForm.vue';
import { AddObj as createTicket } from '/@/api/ticket/ticket';
import { generateUUIDString } from '/@/utils/tools';
import { useRouter } from 'vue-router';
import { pickerOptions } from '/@/utils/pickDatetimeOptions';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';


const active = ref(0);
const nodeValueField = ref('value');
const allNodes = dictionary('operator_cmdb:host:area_node', undefined);

// 路由
const router = useRouter()

let createServerFormData = ref({
  image_id: '',
  flavor_id: '',
  count: 1,
  name: generateUUIDString(),
  description: '',
  instance_type: '裸金属',
  node: '',
  account_id: '',
  expire_at: '',
  softwares: [],
  is_need_extra_public_ip: false,
});

const createServerAccountFormRules = ref({
  account_id: [
    { required: true, message: '请选择账号', trigger: 'blur' },
  ],
});

const createServerBasicalFormRules = ref({
  name: [
    { required: true, message: '请输入主机名称', trigger: 'blur' },
    { min: 3, max: 63, message: '最小3个字符，最大63个字符', trigger: 'blur' },
  ],
  description: [
    { max: 255, message: '最大255个字符', trigger: 'blur' }
  ],
  node: [
    { required: true, message: '请选择区域', trigger: 'blur' }
  ],
  instance_type: [
    { required: true, message: '请选择主机类型', trigger: 'blur' }
  ],
  expire_at: [
    { required: true, message: '请选择过期时间', trigger: 'blur' }
  ],
  count: [
    { required: true, message: '请输入创建数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '最小为 1，最大为 10', trigger: 'blur' },
  ],
});

const createServerHostFormRules = ref({
  image_id: [
    { required: true, message: '请选择镜像', trigger: 'blur' },
    { min: 10, message: '请选择镜像', trigger: 'blur' },
  ],
  flavor_id: [
    { required: true, message: '请选择规格', trigger: 'blur' },
    { min: 10, message: '请选择规格', trigger: 'blur' },
  ],

  count: [
    { required: true, message: '请输入创建数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '最小为 1，最大为 10', trigger: 'blur' },
  ],
});

// 租户数据验证
const createServerAccountFormValid = ref();
// 基础数据验证
const createServerBasicalFormVaild = ref();
// 主机配置数据验证
const createServerHostFormValid = ref();


const steps = ref([
  {
    name: '租户配置',
    key: 'account_config',
    status: 'doing',
    active: 0,
  },
  {
    name: '基础配置',
    key: 'basical_config',
    status: 'doing',
    active: 1,
  },
  {
    name: '主机配置',
    key: 'host_config',
    status: 'doing',
    active: 2,
  },
]);

const nextStep = () => {
  active.value += 1
  if (active.value > 2) {
    active.value = 0
  }
}

const active0NextStep = () => {
  createServerAccountFormValid.value.validate((valid: any) => {
    if (!valid) {
      TinyModal.message({
        message: '创建租户信息有误，请确认?',
        status: 'error',
      });
      return
    }
    else {
      stepsDoneClick(active.value)
      active.value += 1
      if (active.value > 2) {
        active.value = 0
      }
    }
  })
}

const active1NextStep = () => {
  createServerBasicalFormVaild.value.validate((valid: any) => {
    if (!valid) {
      TinyModal.message({
        message: '主机基础信息配置有误，请确认?',
        status: 'error',
      });
      return
    }
    else {
      stepsDoneClick(active.value)
      active.value += 1
      if (active.value > 2) {
        active.value = 0
      }
    }
  })
}


const prevStep = () => {
  active.value -= 1
  if (active.value < 0) {
    active.value = 2
  }
}

const scrollToActive = (active_step: number) => {
  active.value = active_step;
};


const stepsDoneClick = (index: number) => {
  steps.value[index].status = 'done'
}

const stepsClick = (index: number) => {
  scrollToActive(steps.value[index]?.active || 0)
}

// 显示消息的辅助函数
const showMessage = (message: string, status: 'success' | 'error') => {
  TinyModal.message({
    message,
    status,
  });
};

// 表单验证函数
const validateForm = (): Promise<boolean> => {
  return new Promise((resolve) => {
    createServerHostFormValid.value.validate((valid: boolean) => {
      resolve(valid);
    });
  });
};

// 提交表单并创建工单的函数
const clickCreate = async () => {
  // 验证表单
  const isValid = await validateForm();
  if (!isValid) {
    showMessage('主机配置信息有误，请确认', 'error');
    return;
  }

  try {
    // 提交工单
    const response = await createTicket({
      name: '裸金属工单申请表',
      source_form_data: createServerFormData.value,
    });

    // 处理成功响应
    showMessage(response.msg, 'success');
    // 可选：跳转到成功页面或其他操作
    // router.push('/tenant/producter_and_services/elasti-computes/baremetal');
    router.go(-1);
  } catch (error) {
    // 处理错误
    showMessage('创建失败', 'error');
  }
};
</script>
<style scoped lang="less">
.new-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
  padding: 8px;
}

.fixed-header {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.show-footer-button {
  display: flex;
  align-items: end;
  justify-content: end;
  margin-right: 60px;
  margin-bottom: 20px;
}

.create-server-steps {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-step-container {
  margin: 20px;
}

.container-step-head {
  font-size: 24px;
  font-weight: bolder;
  margin-bottom: 24px;
}

.content {
  margin-top: 150px;
  /* 确保内容不会被步骤条覆盖 */
  overflow-y: auto;
  /* 允许内容区域垂直滚动 */
}

.section {
  height: 100vh;
  /* 每个部分的高度为视口高度 */
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.container-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 20px);
  overflow-x: hidden;
  overflow-y: auto;
}

/* Tiny Vue 组件搜索栏修改圆角 */
:deep(.tiny-input__inner) {
  border-radius: 6px;
  /* 设置圆角大小 */
}

:deep(.tiny-button) {
  border-radius: 16px;
  /* 设置圆角大小 */
}

.line {
  height: 1px;
  color: rgb(213, 213, 213);
  background-color: rgb(213, 213, 213);
  border: 0;
}

.contain {
  flex: 1 1 auto;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);

  .contain-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 10px 0 10px;

    hr {
      .line();

      width: 86%;
      margin: 0 20px;
    }

    span {
      color: #1a1818;
      font-size: 16px;
    }
  }

  .contain-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 52px;
  }

  .contain-img {
    position: relative;
    display: flex;
    cursor: pointer;

    img:hover {
      background: whitesmoke;
    }
  }

  .contain-text {
    padding-left: 10px;
    color: #4e5969;
    font-size: 14px;
    cursor: pointer;
  }
}

.bottom-line {
  padding-bottom: 8px;

  hr {
    .line();

    width: 96%;
    margin: 0 auto;
  }
}

.filter-form {
  margin: 10px 0;
}

.col {
  width: 96%;
}

:deep(.tiny-grid) {

  &-header__column,
  &-body__column {

    &.col__selection,
    &.col__radio {
      padding: 0 8px 0 8px;

      &+th,
      +td {
        padding-left: 0;
      }
    }
  }
}

:deep(.tiny-pager) {
  float: right;
}

:deep(.tiny-grid-button__wrapper) {
  width: 100%;
}

:deep(.tiny-form-item__label) {
  color: #494747;
  font-weight: normal;
}

:deep(.tiny-grid-header__column) {
  height: 35px;
  color: rgb(139, 137, 137);
  background-color: #f5f6f7;
}

.operation {
  color: #5e7ce0;
}

.btn {
  display: flex;
  width: 100%;

  .screen {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    text-align: center;
    cursor: pointer;

    span {
      margin-left: 10px;
      color: #4e5969;
      font-size: 14px;
    }
  }
}

.btn>div:last-child {
  margin-left: auto;
}

.tiny-fullscreen {
  background: #fff;
}

.tiny-fullscreen-wrapper {
  min-height: 710px;
  padding: 0 30px;
}

.tiny-fullscreen-scroll {
  overflow-y: auto;
}

.search-btn {
  display: flex;

  button {
    height: 34px;
  }
}

.id-cell {
  display: flex;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    /* 根据需要调整宽度 */
  }
}

.status-success {
  fill: #52c41a;
  margin-right: 8px;
}

.status-danger {
  fill: #C7000B;
  margin-right: 8px;
}

.status-warning {
  fill: #FA9841;
  margin-right: 8px;
}
</style>