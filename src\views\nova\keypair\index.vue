<!-- 秘钥对 -->
<template>
  <div class="app-container">
    
      <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="queryParams.search"
            placeholder="输入秘钥对ID、名称进行搜索"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetClick()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">

      <!-- <div class="mb-[10px]">
        <el-button type="success" @click="handleAddClick()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button
          type="danger"
          :disabled="ids.length === 0"
          @click="handleDelete()"
        >
          <i-ep-delete />
          删除
        </el-button>
      </div> -->

      <el-table
        v-loading="loading"
        highlight-current-row
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="秘钥对名称" prop="name" />
        <el-table-column label="项目" prop="project_name" />
        <el-table-column label="用户" prop="user_name" />
        <el-table-column label="Public Key" prop="public_key" />
        <el-table-column label="指纹" prop="fingerprint" />
        <el-table-column label="类型" prop="type" width="60"/>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!--秘钥对弹窗-->

    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="80%"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="computedRules"
        label-width="100px"
      >
        <el-card shadow="never">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入秘钥对名称" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitClick">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import "/@/styles/index.scss";   // 基础样式
import { reactive, ref, computed, onMounted, defineOptions } from "vue";
import { ElForm, ElInput, ElDrawer, ElButton, ElTag, ElMessage, ElMessageBox } from "element-plus";
import  Pagination from  "/@/components/Pagination/index.vue";

defineOptions({
  name: "Keypair",
  inherititems: false,
});

import KeypairAPI, { KeypairPageQuery, KeypairPageVO, KeypairForm } from "./api";
import NetworkAPI from "../network/api";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<KeypairPageQuery>({
  page: 1,
  limit: 10,
});

const tableData = ref<KeypairPageVO[]>();

// 秘钥对弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

const formData = reactive<KeypairForm>({});

const computedRules = computed(() => {
  const rules: Partial<Record<string, any>> = {
    name: [{ required: true, message: "请输入秘钥对名称", trigger: "blur" }],
  };
  return rules;
});


const netOptions = ref<any[]>([]);
// 将网络数组netOptions 改造成一个map，使用compute在元数据变化时自动更新
const netObject = computed(() => {
  return netOptions.value.reduce((obj, item) => {
    obj[item.net_id] = item.name;
    return obj;
  }, {});
});


// 查询网络组列表
function getNetworkList() { 
  NetworkAPI.getList()
    .then((data: any) => {
      netOptions.value = data.results;
    });
}

// 查询
function handleQuery() {
  loading.value = true;
  KeypairAPI.getPage(queryParams)
    .then((data: any) => {
      tableData.value = data.data;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetClick() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

// 行选择函数
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 新增秘钥对
function handleAddClick() {
  dialog.visible = true;
  dialog.title = "新增秘钥对";
}

// /**
//  * 编辑秘钥对
//  *
//  * @param id 秘钥对ID
//  */
// function handleEditClick(id: number, name: string) {
//   dialog.visible = true;
//   dialog.title = "【" + name + "】秘钥对修改";
//   KeypairAPI.getFormData(id).then((data) => {
//     Object.assign(formData, data);
//   });
// }

// 提交秘钥对表单
function handleSubmitClick() {
  dataFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      loading.value = true;
      const id = formData.keypair_id;
      ElMessage.info("点击了提交按钮" + id)
      // if (id) {
      //   KeypairAPI.update(id, formData)
      //     .then(() => {
      //       ElMessage.success("修改成功");
      //       handleCloseDialog();
      //       handleQuery();
      //     })
      //     .finally(() => (loading.value = false));
      // } else {
      //   KeypairAPI.add(formData)
      //     .then(() => {
      //       ElMessage.success("新增成功");
      //       handleCloseDialog();
      //       handleQuery();
      //     })
      //     .finally(() => (loading.value = false));
      // }
    }
  });
}

/** 关闭秘钥对弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.keypair_id = '';
}
/**
 * 删除秘钥对
 *
 * @param id 秘钥对ID
 */
function handleDelete(id?: number) {
  console.log("handleDelet" + id);
  // const attrGroupIds = [id || ids.value].join(",");
  // if (!attrGroupIds) {
  //   ElMessage.warning("请勾选删除项");
  //   return;
  // }
  // ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
  //   confirmButtonText: "确定",
  //   cancelButtonText: "取消",
  //   type: "warning",
  // }).then(
  //   () => {
  //     KeypairAPI.deleteByIds(attrGroupIds).then(() => {
  //       ElMessage.success("删除成功");
  //       handleResetClick();
  //     });
  //   },
  //   () => {
  //     ElMessage.info("已取消删除");
  //   }
  // );
}


onMounted(() => {
  handleQuery();
  getNetworkList();
});
</script>
