server {
    listen       8080;
    server_name localhost;
    client_max_body_size 100M;
    location / {
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        set_real_ip_from 0.0.0.0/0;
        real_ip_header X-Forwarded-For;
        root /usr/share/nginx/html;
        index  index.html index.php index.htm;
    }

    location /api/ {
        proxy_http_version 1.1;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Nginx-Proxy true;
        set_real_ip_from 0.0.0.0/0;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_connect_timeout 600s;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
        real_ip_header X-Forwarded-For;
        # rewrite ^/api/(.*)$ /$1 break;  #重写
        proxy_pass http://chaos-django:8000/; # 设置代理服务器的协议和地址
    }

    location /media  {
	    alias /code/media; 
	    expires 7d;
	    gzip  on;
	    gzip_min_length 1k;
	    gzip_comp_level 2;
	    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
	    gzip_vary on;
	}
	location /api/media  {
	    alias /code/media; 
	    expires 7d;
	    gzip  on;
	    gzip_min_length 1k;
	    gzip_comp_level 2;
	    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
	    gzip_vary on;
	}
 }