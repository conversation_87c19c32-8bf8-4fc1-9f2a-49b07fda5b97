import * as api from './api';
import {
  dict,
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  // compute,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet
} from '@fast-crud/fast-crud';
import { auth } from "/@/utils/authFunction";
import { GetLevelAllAreasList } from '/@/views/system/areas/api';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  const searchLevelAllAreasList = async (query: UserPageQuery) => {
    return await GetLevelAllAreasList(query)
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('purchaseSalesManage:customer:Create'),
            plain: true,
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            link: true,
            type: 'primary',
            show: auth('purchaseSalesManage:customer:Retrieve'),
          },
          edit: {
            link: true,
            type: 'primary',
            show: auth('purchaseSalesManage:customer:Update'),
          },
          remove: {
            link: true,
            type: 'danger',
            show: auth('purchaseSalesManage:customer:Delete'),
          },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        name: {
          title: '客户名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '名称必填项' },
            ],
            component: {
              placeholder: '请输入名称',
            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'textarea',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入描述',
            }
          }
        },
        province: { // [Importent] 多级选择、实时加载
          title: '省份',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/system/area/search_level_all_areas/?level=1',
            value: 'code',
            label: 'name',
            cache: true,
          }),
          form: {
            valueChange({form, value, getComponentRef}) {
              form.city = ""  // 将 province 的值置空
              form.district = ""  // 将 city 的值置空
              if (value) {
                getComponentRef("city").reloadDict();  // 执行 province 的 select 组件的 reloadDict() 方法，触发 province 重新加载字典
              }
            },
            rules: [
              // 表单校验规则
              {required: true, message: '省份必填项'},
            ],
            component: {
              placeholder: '请选择省份',
            }
          }
        },
        // city: {
        //   title: '城市',
        //   search: {
        //     show: true,
        //   },
        //   type: 'dict-select',
        //   dict: dict({
        //     cache: true,
        //     prototype: true,
        //     value: "code",
        //     label: 'name',
        //     async getData({form}) {
        //       const params = {
        //         level: 2,
        //         pcode: form.province
        //       }
        //       const apiRes = await searchLevelAllAreasList(params);
        //       return apiRes.data;
        //     }
        //   }),
        //   form: {
        //     // 注释同上
        //     valueChange({ value, form, getComponentRef }) {
        //       if (value) {
        //         form.distinct = ""; // 将 city 的 value 置空
        //         const districtSelect = getComponentRef("district");
        //         if (form && form.province && form.city) {
        //           districtSelect.reloadDict(); // 重新加载字典项
        //         } else {
        //           districtSelect.clearDict(); // 清空选项
        //         }
        //       }
        //     },
        //     rules: [
        //       // 表单校验规则
        //       {required: false, message: '城市必填项'},
        //     ],
        //     component: {
        //       placeholder: '请选择城市',
        //     }
        //   }
        // },
        // district: {
        //   title: '区县',
        //   search: {
        //     show: true,
        //   },
        //   column: {
        //     minWidth: 90,
        //   },
        //   type: 'dict-select',
        //   dict: dict({
        //     value: "code",
        //     label: "name",
        //     cache: true,
        //     prototype: true,
        //     async getData({form}) {
        //       const params = {
        //         level: 3,
        //         pcode: form.city
        //       }
        //       const apiRes = await searchLevelAllAreasList(params);
        //       return apiRes.data;
        //     }
        //   }),
        //   form: {
        //     rules: [
        //       // 表单校验规则
        //       {required: false, message: '区县必填项'},
        //     ],
        //     component: {
        //       placeholder: '请选择区县',
        //     }
        //   }
        // },
        officer: {
          title: '客户联系人',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入客户联系人',
            }
          }
        },
        // phone: {
        //   title: '联系人电话',
        //   search: {
        //     show: true,
        //   },
        //   type: 'input',
        //   column: {
        //     minWidth: 90,
        //   },
        //   form: {
        //     rules: [
        //       // 表单校验规则
        //       // {required: true, message: '公司描述必填项'},
        //     ],
        //     component: {
        //       placeholder: '请输入联系人电话',
        //     }
        //   }
        // }, 
        // email: {
        //   title: '联系人邮箱',
        //   search: {
        //     show: true,
        //   },
        //   type: 'input',
        //   column: {
        //     minWidth: 90,
        //   },
        //   form: {
        //     rules: [
        //       // 表单校验规则
        //       // {required: true, message: '公司描述必填项'},
        //     ],
        //     component: {
        //       placeholder: '请输入联系人邮箱',
        //     }
        //   }
        // },
        customer_manager_name: {
          title: '客户经理名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入客户经理名称',
            }
          }
        },
        customer_manager_phone: {
          title: '客户经理电话',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入客户经理电话',
            }
          }
        },
        customer_manager_email: {
          title: '客户经理邮箱',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入客户经理邮箱',
            }
          }
        },
      },
    },
  };
};
