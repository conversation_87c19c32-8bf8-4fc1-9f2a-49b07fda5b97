<template>
  <div>
    <div style="margin-bottom: 12px;">
      <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image :src="flavorImg" alt="规格图标" style="width: 24px; height: 16px;"></tiny-image>当前规格:</span>
      <tiny-tag type="success" size="medium"> {{ currentSelectFlavor }} </tiny-tag>
      </div>
    <tiny-grid ref="selectFlavorGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @radio-change="handleRadioChange" row-id="flavor_id">
      <tiny-grid-column type="radio" width="40"></tiny-grid-column>
      <tiny-grid-column field="name" title="ID/名称" align="left" width="180" :sortable="true" :filter="nameFilter">
        <template #default="{row}">
          <div class="id-cell">
              <!-- <span class="id-text">{{ row.ironicHyperisor_id.slice(0, 16) }}</span> -->
              <tiny-link :underline="false" type="primary">{{ row.flavor_id.slice(0, 8) }}</tiny-link>
              <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.flavor_id)"></tiny-link>
          </div>
          <p>{{ row.name }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="flavor_type" title="镜像类型" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="vcpus" title="虚拟内核" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="ram" title="内存" align="center" :sortable="true">
        <template #default="{row}">
          {{ Math.round(row.ram/1024) }} GiB
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="disk" title="磁盘" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.disk }} GiB
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="swap" title="临时内存" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.swap }} MiB
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="is_public" title="共享" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.is_public ? '是': '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="node" title="区域" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="sync_time" title="同步时间" align="center" :sortable="true">
        <template #default="{row}">
          {{ formatNow(row.sync_time) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectFlavorForm">
import { ref, reactive, toRefs, watch } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyInput,
  TinyPager,
  TinyTag,
} from '@opentiny/vue';
import { GetList } from '/@/api/tenant/opFlavor';
import { toNumber } from 'lodash';
import { iconCopy } from '@opentiny/vue-icon';
import { formatNow } from '/@/utils/formatTime';
import { copyText } from '/@/utils/copyText';
import flavorImg from '/@/assets/img/flavor.svg';

const TinyIconCopy = iconCopy();

const props = defineProps({
  projectId: {
    type: String,
    required: false,
    default: ''
  },
  currentSelectFlavorId: {
    type: String,
    required: false,
    default: '',
  },
  flavor_type: {
    type: String,
    required: false,
    default: ''
  },
  node: {
    type: String,
    required: false,
    default: ''
  },
});


// 当前选中值
let currentSelectFlavor = ref<string>('--');
// const radioConfigData = ref({
//   checkRowKey: props.currentSelectFlavorId
// })
const emit = defineEmits(['update:currentSelectFlavorId']);

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})


// 初始化请求数据
interface FilterOptions {
  vcpus: string;
  ram: string;
  disk: string;
  swap: string;
  name: string;
  is_to_portal: Boolean;
  flavor_id: string;
  flavor_type: string;
  node: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    vcpus: '',
    ram: '',
    disk: '',
    swap: '',
    name: '',
    is_to_portal: true,
    flavor_id: props.currentSelectFlavorId,
    flavor_type: props.flavor_type,
    node: props.node,
  },
});
let tableData = ref([]);

const selectFlavorGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectFlavorGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;

     // 如果有初始选中的 ID，设置默认选中行
     if (props.currentSelectFlavorId) {
      // @ts-ignore
      const selectedRow = tableData.value.find(row => row.flavor_id === props.currentSelectFlavorId);
      if (selectedRow) {
        selectFlavorGrid.value?.setRadioRow(selectedRow);
        // @ts-ignore
        currentSelectFlavor.value = selectedRow.name;
      }
    }

    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    vcpus: '',
    ram: '',
    disk: '',
    swap: '',
    name: '',
    is_to_portal: true,
    flavor_id: props.currentSelectFlavorId,
    flavor_type: props.flavor_type,
    node: props.node,
  };
  // reloadGrid();
}
const handleRadioChange = () => {
  let selectedRow = selectFlavorGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectFlavor.value = `${selectedRow.name} | ${selectedRow.vcpus}vCPUS | ${toNumber(selectedRow.ram/1024)} GiB`
    emit('update:currentSelectFlavorId', selectedRow.flavor_id)
  }
  
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
      filterOptions.value.name = filters.filters.name.value.text;
    }
  reloadGrid();
}

watch(() => props.flavor_type, (newFlavorType, oldFlavorType) => {
  if (newFlavorType && newFlavorType !== oldFlavorType) {
    filterOptions.value.flavor_type = newFlavorType;
    reloadGrid();
  }
});

// 监听 node 变化 
watch(() => props.node,  (newNode, oldNode) => {
  if (newNode && newNode !== oldNode) {
    filterOptions.value.node  = newNode;
    reloadGrid();
  }
});
</script>
<style lang="less" scoped>
.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>
