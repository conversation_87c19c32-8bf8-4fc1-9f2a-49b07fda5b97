import * as api from '../../../api/scheduletask/template';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet
} from '@fast-crud/fast-crud';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: false,
            plain: true,
            type: 'primary',
            click: () => {
              window.dispatchEvent(new CustomEvent('openUploadDialog'));
            },
          }
        }
      }, 
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            link: true,
            type: 'primary',
            show: true,
          },
          edit: {
            link: true,
            type: 'primary',
            show: false,
            // 触发自定义插槽的编辑事件
            click: (row) => {
              window.dispatchEvent(new CustomEvent('openEditDialog', { detail: row.row }));
            }
          },
          remove: {
            link: true,
            type: 'danger',
            show: true,
          },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        id: {
          title: '模板ID',
          search: {
            show: true,
            col:{span:8}
          },
          form: {
            show: false,
            component: {
              placeholder: 'ID匹配',
            }
          }
        },
        name: {
          title: '模板名称',
          search: {
            show: true,
            col:{span:8}
          },
          type: 'input',
          column: {
            fixed: 'left',
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '名称匹配',
            }
          }
        },
        project_path: {
          title: '项目路径',
          type: 'link',
          column: {
            minWidth: 150,
          },
          form: {
            component: {
              readonly: true
            }
          },
        },
        params: {
          title: '参数列表',
          type: 'input',
          column: {
            minWidth: 90,
          },
        },
        description: {
          title: '描述',
          type: 'input',
          search: {
            show: true,
            col:{span:8}
          },
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '描述匹配',
            }
          }
        },
      },
    },
  };
};
