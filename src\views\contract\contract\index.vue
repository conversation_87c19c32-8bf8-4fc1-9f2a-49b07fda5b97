<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #actionbar-left>
				<el-button v-if="auth('contract:contract:Create')" type="primary" plain @click="addContract">添加</el-button>
			</template>
			<template #cell_contract_life="{ row }">
				<span
					v-if="row.start_time  && row.end_time"
				>{{ formatDate(row.start_time) }} 至 {{ formatDate(row.end_time) }}</span>
				<span v-else>-</span>
			</template>
			<template #cell_sign_time="{ row }">
				<span v-if="row.sign_time">{{ row.sign_time }}</span>
				<span v-else>-</span>
			</template>
			<template #cell-rowHandle-left="scope">
				<el-button
					v-if="auth('contract:contract:Retrieve')"
					type="primary"
					link
					@click="viewContract(scope.row)"
				>详情</el-button>
				<el-button
					v-if="['待提交', '审批驳回'].includes(scope.row.status) && auth('contract:contract:Edit')"
					type="primary"
					link
					@click="editContract(scope.row)"
				>编辑</el-button>
				<el-button
					v-if="(['待审批'].includes(scope.row.status)) && auth('contract:contract:Approval')"
					type="primary"
					link
					@click="approvalContract(scope.row)"
				>审批</el-button>
				<el-dropdown
					@command="(cmd) => DialogHandler(scope.row,cmd)"
					v-if="['审批通过','提前完结','已归档'].includes(scope.row.status) && scope.row.is_payment === false && auth('contract:payment:Generate')"
				>
					<el-button type="primary" link>
						生成回款
						<el-icon class="el-icon--right">
							<arrow-down />
						</el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="month">按月生成</el-dropdown-item>
							<el-dropdown-item command="quarter">按季生成</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</template>
		</fs-crud>
		<el-drawer
			v-model="contractDialogVisible"
			:with-header="false"
			@open="isRemount = !isRemount"
			size="75%"
		>
			<!-- <template #header="{ close, titleId, titleClass }">
				<el-text tag="b" :id="titleId" :class="titleClass">{{ method=="Update"?"更新":"创建" }}合同</el-text>
			</template>-->
			<CreateOrEditFrame
				:key="isRemount"
				:method="method"
				@submitHandler="submit"
				:item="item"
				resourceType="contract"
			>
				<template #header>
					<div style="font-size: 25px; margin: 10px 0 10px 15px">
						{{
						method === 'Create'
						? '创建合同'
						: method === 'Update'
						? '更新合同'
						: method === 'Approval'
						? '审批合同'
						: '合同详情'
						}}
					</div>
				</template>
			</CreateOrEditFrame>
		</el-drawer>

		<!-- 生成回款表格 -->
		<el-dialog v-model="DialogVisible" title="生成回款" style="width:60%;" center>
			<el-table border scrollbar-always-on :data="items" style="width: 100%" height="200">
				<el-table-column prop label="回款周期" width="400px">
					<template #default="scope">
						<el-date-picker
							v-model="scope.row.rangeTime"
							type="daterange"
							unlink-panels
							start-placeholder="开始日期"
							end-placeholder="结束日期"
							value-format="YYYY-MM-DD"
						/>
					</template>
				</el-table-column>
				<el-table-column prop label="回款金额">
					<template #default="scope">
						<el-form-item label prop>
							<el-input v-model="scope.row.price" placeholder="请输入回款金额" />
						</el-form-item>
					</template>
				</el-table-column>
				<el-table-column prop="desc" label="居间方">
					<template #default="scope">
						<el-form-item label prop>
							<el-input v-model="scope.row.agent" placeholder="请输入居间方" />
						</el-form-item>
					</template>
				</el-table-column>
				<el-table-column prop="desc" label="居间费">
					<template #default="scope">
						<el-form-item label prop>
							<el-input v-model="scope.row.intermediary" placeholder="请输入居间费" />
						</el-form-item>
					</template>
				</el-table-column>
				<el-table-column fixed="right" width="140px">
					<template #header>
						<el-button link type="primary" @click="addTableRow">添加</el-button>
					</template>
					<template #default="scope">
						<el-button link type="warning" @click="deleteTableRow(scope.$index)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>

			<template #footer>
				<div class="dialog-footer-center">
					<el-button @click="editDialogVisible = false">取消</el-button>
					<el-button type="primary" @click="submit">确认</el-button>
				</div>
			</template>
		</el-dialog>
	</fs-page>
</template>

<script setup>
import { ref,onMounted,reactive,toRefs } from 'vue'
import { useRouter } from 'vue-router'
import * as api from '/@/api/contract/contract';
import * as paymentApi from '/@/api/contract/payment';
import { ElMessage } from 'element-plus'
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import CreateOrEditFrame from '../components/CreateOrEditFrame.vue';
import dayjs from 'dayjs';
import { auth } from '/@/utils/authFunction';
const router = useRouter()
const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
const contractDialogVisible = ref(false);
const isRemount = ref(false)
const DialogVisible = ref(false)
const method = ref("Create")
const items = ref([])
const data = reactive({
  item:{}
})

const formatDate = (datetime) => {
    return dayjs(datetime).format('YYYY-MM-DD')
};

const DialogHandler = (row,mode) => {
  const data = {
    id:row.id,
    type:mode
  }
  const customer_id = row.customer
  paymentApi.GenerateObj(data).then((response) => {
    if (response.code === 2000) {
      router.replace({
        path: '/purchaseSalesManage/payment',
        query: {
          code: row.code
        }
      })
      row.is_payment = true
      const req_data = {
        contract: row,
        accessory: [],
        delete_accessory_list: []
      }
      req_data.contract.customer = {}
      req_data.contract.customer.id = customer_id
      submit(req_data)
		} else {
			ElMessage.error(response.msg);
		}
  })
}

// 添加行的方法
const addTableRow = () => {
	const newRow = {
		rangeTime: '',
		price: '',
    agent: '',
    intermediary: ""
	};
	// 在列表后面添加数据
	items.value.unshift(newRow);
};

// 删除行的方法
const deleteTableRow = (index) => {
	items.value.splice(index, 1);
};

const submit = (row) => {
  api.SaveObj(row).then((response) => {
    if (response.code === 2000) {
      crudExpose.doRefresh();
		} else {
			ElMessage.error(response.msg);
		}
  })
  contractDialogVisible.value = false;
}

const addContract = () => {
  method.value = "Create"
  contractDialogVisible.value = true;
}

const viewContract = async(row) => {
  method.value = "Detail"
  await api.GetObj(row.id).then((response) => {
		if (response.code === 2000) {
      data.item = response.data
		} else {
			ElMessage.error(response.msg);
		}
  });
  contractDialogVisible.value = true;
}

const editContract = async(row) => {
  method.value = "Update"
  await api.GetObj(row.id).then((response) => {
		if (response.code === 2000) {
      data.item = response.data
		} else {
			ElMessage.error(response.msg);
		}
  });
  contractDialogVisible.value = true;
}

const approvalContract = async(row) => {
  method.value = "Approval"
  await api.GetObj(row.id).then((response) => {
		if (response.code === 2000) {
      data.item = response.data
		} else {
			ElMessage.error(response.msg);
		}
  });
  contractDialogVisible.value = true;
}

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});

const { item } = toRefs(data);
</script>
<style scoped>
</style>