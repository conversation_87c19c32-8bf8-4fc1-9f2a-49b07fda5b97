<template>
  <fs-page>
    <div class="container-content">
      <div class="detail-base-cards">
        <el-descriptions :column="3" size="large">
          <template #title>
            <div class="id-wrapper">
              <span>裸机节点ID：{{ state.detailData.ironic_hyper_id }}</span>

              <el-link :underline="false" type="primary" :icon="DocumentCopy"
                @click="copyText(state.detailData.ironic_hyper_id)"></el-link>
              <span style="padding-left: 20px; padding-right:20px;">|</span>
              <el-link type="primary" @click="router.go(-1)">返回</el-link>
            </div>
          </template>
          <template #extra>
            <div class="toolbar-container">
              <el-link :underline="false" type="primary"  target="_blank" :href="`http://${state.detailData.name}`" :icon="Monitor">&nbsp;BMC控制台</el-link>
              <el-dropdown>
                <span class="el-dropdown-link">
                  操作
                  <el-icon class="el-icon--right">
                    <arrow-down />
                  </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :disabled="true">
                      关闭电源
                    </el-dropdown-item>
                    <el-dropdown-item :disabled="true">
                      软关机
                    </el-dropdown-item>
                    <el-dropdown-item :disabled="true">
                      移动 acvtive
                    </el-dropdown-item>
                    <el-dropdown-item :disabled="true">
                      移动 manageable
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
          </template>
          <el-descriptions-item label="名称" label-class-name="desc-customer-label" class-name="desc-customer-content">{{
            state.detailData.name }}</el-descriptions-item>
          <el-descriptions-item label="网络接口" label-class-name="desc-customer-label"
            class-name="desc-customer-content">{{
              state.detailData.network_interface }}</el-descriptions-item>

          <el-descriptions-item label="区域" label-class-name="desc-customer-label" class-name="desc-customer-content">
            <el-tag type="primary">{{ state.detailData.node }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="管理接口" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.management_interface }}
          </el-descriptions-item>
          <el-descriptions-item label="驱动" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.driver }}
          </el-descriptions-item>
          <el-descriptions-item label="资源类" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.resource_class }}
          </el-descriptions-item>
          <el-descriptions-item label="调度节点" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.conductor }}
          </el-descriptions-item>
          <el-descriptions-item label="创建于" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.created_at }}
          </el-descriptions-item>
          <el-descriptions-item label="更新于" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.updated_at }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="container-content">
      <div class="detail-base-cards">
        <tiny-layout :cols="24">
          <tin-row :gutter="40">
            <!--- 左侧信息区---->
            <tiny-col :span="12">
              <tiny-card title="配置状态" class="detial-sub-cards" :auto-width="true">
                <tiny-layout :cols="24">
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      云主机/裸金属ID
                    </tiny-col>
                    <tiny-col :span="16">
                      <el-link type="primary" :underline="false" @click="toOPServerDetail(state.detailData.instance_id)" :disabled="state.detailData.instance_id ? false : true">&nbsp;{{ state.detailData.instance_id || '-' }}</el-link>
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      电源状态
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.power_state || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      配置状态
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.provision_state || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      维护
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.is_maintenance ? '是' : '否' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      维护原因
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.maintenance_reason || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      预留
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.is_protected || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      预留原因
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.protected_reason || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      允许控制台
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.is_console_enabled || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      最近的一次错误
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.last_error || '-' }}
                    </tiny-col>
                  </tiny-row>

                </tiny-layout>
              </tiny-card>

            </tiny-col>
            <!--- 右侧信息区 ---->
            <tin-col :span="12">
              <tiny-card title="驱动信息" class="detial-sub-cards" :auto-width="true">
                <tiny-layout :cols="24">
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      IMPI_用户
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.driver_info).ipmi_username }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      IMPI_密码
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.driver_info).ipmi_password }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      IMPI_地址
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.driver_info).ipmi_address }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      IMPI_协议
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.driver_info).ipmi_protocol_version }}
                    </tiny-col>
                  </tiny-row>

                </tiny-layout>
              </tiny-card>
              <tiny-card title="属性" class="detial-sub-cards" :auto-width="true">
                <tiny-layout :cols="24">
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      cpus
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.properties).cpus }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      memory_mb
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.properties).memory_mb }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      local_gb
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.properties).local_gb }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      cpu_arch
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.properties).cpu_arch }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      capabilities
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.properties).capabilities }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      vendor
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ toStringJSON(state.detailData.properties).vendor }}
                    </tiny-col>
                  </tiny-row>

                </tiny-layout>
              </tiny-card>
            </tin-col>
          </tin-row>
        </tiny-layout>
      </div>
    </div>
  </fs-page>
</template>
<script lang="ts" setup>
import { onMounted, reactive, } from 'vue';
import { GetObj } from '/@/api/tenant/opIronicHypervisor';
import { TinyCard, TinyLayout, TinyRow, TinyCol, } from '@opentiny/vue';
import { useRouter } from 'vue-router';
import { Monitor, DocumentCopy, ArrowDown, View } from '@element-plus/icons-vue';
import { copyText } from '/@/utils/copyText';
import { toStringJSON } from 'xe-utils';
import { toOPServerDetail } from '/@/router/intervalRouterTo/tenant';

const router = useRouter();


defineProps({
  id: {
    type: String,
    required: false,
    default: '',
  },
});

const router_ironicicHypervisor_id = router.currentRoute.value.params.id || '';

const state = reactive({
  detailData: {},
  loading: true,
  success: false,
})


const getDetailData = async () => {
  try {
    const response = await GetObj(router_ironicicHypervisor_id);
    state.detailData = response.data;
    state.loading = false;
    state.success = true;
  }
  catch (e) {
    console.error('获取详情数据失败', e);
    state.loading = false;
    state.success = false;
    return;
  }
}

onMounted(async () => {
  await Promise.all([
    getDetailData()
  ])
});
</script>
<style lang="less" scoped>
.container-content {
  display: grid;
  grid-template-columns: auto;
  gap: 8px;
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
}

.toolbar-container {
  display: flex;
  align-items: center;
  gap: 12px; /* 元素间隔 */
  padding: 8px 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

/* 卡片内容限制 */
.detail-base-cards {
  margin: 16px;
  box-sizing: border-box;
  box-shadow: none;
}

.id-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  max-width: 100%;
}

.detial-sub-cards {
  line-height: 2.4;
  margin-bottom: 16px;
}


:deep(.desc-customer-label) {
  color: #909399;
  font-weight: bolder;
}

:deep(.desc-customer-content) {
  color: #000;
}

</style>