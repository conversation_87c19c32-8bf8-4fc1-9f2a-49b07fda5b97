import { request,downloadFile } from '/@/utils/service';
import { PageQuery, AddReq, DelReq, EditReq, InfoReq, UserPageQuery } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/system/user/';

export function GetDept(query: PageQuery) {
    return request({
        url: "/api/system/dept/dept_lazy_tree/",
        method: 'get',
        params: query,
    });
}

export function GetList(query: PageQuery) {
    return request({
        url: apiPrefix,
        method: 'get',
        params: query,
    });
}
export function GetObj(id: InfoReq) {
    return request({
        url: apiPrefix + id,
        method: 'get',
    });
}

export function AddObj(obj: AddReq) {
    return request({
        url: apiPrefix,
        method: 'post',
        data: obj,
    });
}

export function UpdateObj(obj: EditReq) {
    return request({
        url: apiPrefix + obj.id + '/',
        method: 'put',
        data: obj,
    });
}

export function DelObj(id: DelReq) {
    return request({
        url: apiPrefix + id + '/',
        method: 'delete',
        data: { id },
    });
}


export function exportData(params:any){
    return downloadFile({
        url: apiPrefix + 'export_data/',
        params: params,
        method: 'get'
    })
}


export function resetToDefaultPassword(id:any){
  return request({
      url: apiPrefix  + id + '/reset_to_default_password/',
      method: 'put'
  })
}


export function getUserPermission(query: UserPageQuery) {
  return request({
    url: "/api/system/user/get_user_menu_button_permission/",
    method: 'get',
    params: query,
  });
}


export function getUserRowPermission(query: UserPageQuery) {
  return request({
    url: "/api/system/user/get_user_row_permission/",
    method: 'get',
    params: query,
  });
}


export function getRoleUsersInfo(query: UserPageQuery) {
  return request({
    url: "/api/system/user/get_role_users_info/",
    method: 'get',
    params: query,
  });
}

