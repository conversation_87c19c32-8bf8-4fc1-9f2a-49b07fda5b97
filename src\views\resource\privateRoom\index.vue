<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
        <importExcel api="api/resource/private_room/" v-if="isShowImportBtn">导入</importExcel>
      </template>
    </fs-crud>
	</fs-page>
</template>

<script lang="ts" setup name="privateRoom">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { auth } from "/@/utils/authFunction";
import importExcel from '/@/components/importExcel/index.vue'

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });


// 导入按钮显示权限
const isShowImportBtn: boolean = auth("resource:privateRoom:Import")


// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>
