interface PickerShortcut {
  text: string;
  onClick(picker: { $emit: (event: string, date: Date) => void }): void;
}

export function adjustDateKeepingTime(date: Date, modifier: (date: Date) => void): void {
  // 保存原始的时间部分
  const originalHours = date.getHours();
  const originalMinutes = date.getMinutes();
  const originalSeconds = date.getSeconds();
  const originalMilliseconds = date.getMilliseconds();

  // 应用日期修改器
  modifier(date);

  // 恢复原始的时间部分
  date.setHours(originalHours, originalMinutes, originalSeconds, originalMilliseconds);
}

export const setEndTimeOfDay = (date: Date) => {
  date.setHours(23, 59, 59, 999); // 设置时间为 23:59:59.999
}

// 判断是否为闰年
function isLeapYear(year: number): boolean {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
}

// 获取某个月的最后一天
function getLastDayOfMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate();
}

export const pickerOptions: { shortcuts: PickerShortcut[] } = {
  shortcuts: [
    {
      text: '一周后',
      onClick(picker) {
        const date = new Date();
        setEndTimeOfDay(date);
        date.setTime(date.getTime() + 3600 * 1000 * 24 * 7);
        picker.$emit('pick', date);
      }
    },
    {
      text: '一个月后',
      onClick(picker) {
        const date = new Date();
        setEndTimeOfDay(date);
        adjustDateKeepingTime(date, d => {
          const currentMonth = d.getMonth();
          const currentYear = d.getFullYear();
          const currentDay = d.getDate();

          // 先增加一个月
          d.setMonth(currentMonth + 1);

          // 检查日期是否溢出
          if (currentDay > getLastDayOfMonth(d.getFullYear(), d.getMonth())) {
            d.setDate(getLastDayOfMonth(d.getFullYear(), d.getMonth()));
          }
        });
        picker.$emit('pick', date);
      }
    },
    {
      text: '三个月后',
      onClick(picker) {
        const date = new Date();
        setEndTimeOfDay(date);
        adjustDateKeepingTime(date, d => {
          const currentMonth = d.getMonth();
          const currentYear = d.getFullYear();
          const currentDay = d.getDate();

          // 先增加三个月
          d.setMonth(currentMonth + 3);

          // 检查日期是否溢出
          if (currentDay > getLastDayOfMonth(d.getFullYear(), d.getMonth())) {
            d.setDate(getLastDayOfMonth(d.getFullYear(), d.getMonth()));
          }
        });
        picker.$emit('pick', date);
      }
    },
    {
      text: '六个月后',
      onClick(picker) {
        const date = new Date();
        setEndTimeOfDay(date);
        adjustDateKeepingTime(date, d => {
          const currentMonth = d.getMonth();
          const currentYear = d.getFullYear();
          const currentDay = d.getDate();

          // 先增加六个月
          d.setMonth(currentMonth + 6);

          // 检查日期是否溢出
          if (currentDay > getLastDayOfMonth(d.getFullYear(), d.getMonth())) {
            d.setDate(getLastDayOfMonth(d.getFullYear(), d.getMonth()));
          }
        });
        picker.$emit('pick', date);
      }
    },
    {
      text: '一年后',
      onClick(picker) {
        const date = new Date();
        setEndTimeOfDay(date);
        adjustDateKeepingTime(date, d => {
          const currentYear = d.getFullYear();
          const currentMonth = d.getMonth();
          const currentDay = d.getDate();

          // 先增加一年
          d.setFullYear(currentYear + 1);

          // 特别处理2月29日的情况
          if (currentMonth === 1 && currentDay === 29 && !isLeapYear(d.getFullYear())) {
            d.setDate(28); // 如果目标年份不是闰年，设置为2月28日
          }

          // 检查其他月份的日期是否溢出
          if (currentDay > getLastDayOfMonth(d.getFullYear(), d.getMonth())) {
            d.setDate(getLastDayOfMonth(d.getFullYear(), d.getMonth()));
          }
        });
        picker.$emit('pick', date);
      }
    },
    {
      text: '三年后',
      onClick(picker) {
        const date = new Date();
        setEndTimeOfDay(date);
        adjustDateKeepingTime(date, d => {
          const currentYear = d.getFullYear();
          const currentMonth = d.getMonth();
          const currentDay = d.getDate();

          // 先增加三年
          d.setFullYear(currentYear + 3);

          // 特别处理2月29日的情况
          if (currentMonth === 1 && currentDay === 29 && !isLeapYear(d.getFullYear())) {
            d.setDate(28); // 如果目标年份不是闰年，设置为2月28日
          }

          // 检查其他月份的日期是否溢出
          if (currentDay > getLastDayOfMonth(d.getFullYear(), d.getMonth())) {
            d.setDate(getLastDayOfMonth(d.getFullYear(), d.getMonth()));
          }
        });
        picker.$emit('pick', date);
      }
    }
  ]
};