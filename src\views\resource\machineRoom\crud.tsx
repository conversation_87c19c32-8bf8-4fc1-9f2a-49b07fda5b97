import * as api from './api';
import { GetLevelAllAreasList } from '/@/views/system/areas/api';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict
} from '@fast-crud/fast-crud';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import {verifyPhone} from '/@/utils/toolsValidate';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  const searchLevelAllAreasList = async (query: UserPageQuery) => {
    return await GetLevelAllAreasList(query)
  };

  const validatePhone = async (rule: object, value: string) => {
    if (!verifyPhone(value)) {
      throw new Error('手机号有误')
    }
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('resource:machineRoom:Create'),
            plain: true,
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 280,
        buttons: {
          view: {
            type: 'primary',
            link: true,
            show: auth('resource:machineRoom:Retrieve')
          },
          edit: {
            type: 'primary',
            link: true,
            show: auth('resource:machineRoom:Update')
          },
          remove: {
            type: 'danger',
            link: true,
            show: auth('resource:machineRoom:Delete')
          },
          viewLog: {
            type: 'primary',
            text: '查看日志',
            link: true,
            show: auth('system:auditLog:GetResourceLogs'),
            click(context) {
              router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
            }
          },
        },
      },
      pagination: {
        show: true,
        'default-page-size': 10,
        'default-current': 1,
      },
      table: {
        rowKey: 'id',
      },
      form: {
        labelWidth: 100,
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        name: {
          title: '机房名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '名称必填项' },
              { max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' }
            ],
            component: {
              placeholder: '请输入机房名称',
            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'textarea',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入机房描述',
            }
          }
        },
        province: { // [Importent] 多级选择、实时加载
          title: '省份',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/system/area/search_level_all_areas/?level=1',
            value: 'code',
            label: 'name',
            cache: true,
          }),
          form: {
            valueChange({form, value, getComponentRef}) {
              form.city = ""  // 将 province 的值置空
              form.district = ""  // 将 city 的值置空
              if (value) {
                getComponentRef("city").reloadDict();  // 执行 province 的 select 组件的 reloadDict() 方法，触发 province 重新加载字典
              }
            },
            rules: [
              // 表单校验规则
              {required: true, message: '省份必填项'},
            ],
            component: {
              placeholder: '请选择省份',
            }
          }
        },
        city: {
          title: '城市',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            cache: true,
            prototype: true,
            value: "code",
            label: 'name',
            async getData({form}) {
              const params = {
                level: 2,
                pcode: form.province
              }
              const apiRes = await searchLevelAllAreasList(params);
              return apiRes.data;
            }
          }),
          form: {
            // 注释同上
            valueChange({ value, form, getComponentRef }) {
              if (value) {
                form.distinct = ""; // 将 city 的 value 置空
                const districtSelect = getComponentRef("district");
                if (form && form.province && form.city) {
                  districtSelect.reloadDict(); // 重新加载字典项
                } else {
                  districtSelect.clearDict(); // 清空选项
                }
              }
            },
            rules: [
              // 表单校验规则
              {required: true, message: '城市必填项'},
            ],
            component: {
              placeholder: '请选择城市',
            }
          }
        },
        district: {
          title: '区县',
          search: {
            show: true,
          },
          column: {
            minWidth: 90,
          },
          type: 'dict-select',
          dict: dict({
            value: "code",
            label: "name",
            cache: true,
            prototype: true,
            async getData({form}) {
              const params = {
                level: 3,
                pcode: form.city
              }
              const apiRes = await searchLevelAllAreasList(params);
              return apiRes.data;
            }
          }),
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '区县必填项'},
            ],
            component: {
              placeholder: '请选择区县',
            }
          }
        },
        address: {
          title: '地址',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '机房地址必填项'},
            ],
            component: {
              placeholder: '请输机房地址',
            }
          }
        }, 
        manager_name: {
          title: '责任人名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '责任人必填项'},
              {max: 63, min: 2, message: '输入有误', trigger: 'blur'},
            ],
            component: {
              placeholder: '请输入责任人名称',
            }
          }
        },
        manager_phone: {
          title: '责任人电话',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '责任人电话必填项'},
              {validator: validatePhone, message: '手机号有误', trigger: 'blur'},
            ],
            component: {
              placeholder: '请输入责任人电话',
            }
          }
        },
        manager_email: {
          title: '责任人邮箱',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '责任人邮箱非必填项'},
              {type: 'email', message: '请准确输入邮箱', trigger: 'blur'},
            ],
            component: {
              placeholder: '请输入邮箱',
            }
          }
        },
      },
    },
  };
};
