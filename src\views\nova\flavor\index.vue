<!-- 分类规格 -->
<template>
  <div class="app-container">
    
      <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="queryParams.search"
            placeholder="输入规格ID、名称进行搜索"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetClick()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>


    <el-card shadow="never">
      <div class="mb-[10px]">
        <el-button type="success" @click="handleAddClick()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button
          type="danger"
          :disabled="ids.length === 0"
          @click="handleDelete()"
        >
          <i-ep-delete />
          删除
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        highlight-current-row
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <!-- <el-table-column type="expand" label="" width="100">
          <template #default="props">
            <el-table :data="props.row.dictItems">
              <el-table-column label="规格项键" prop="name" width="200" />
              <el-table-column label="规格项值" prop="value" align="center" />
              <el-table-column label="排序" prop="sort" align="center" />
            </el-table>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="ID" prop="flavor_id" /> -->
        <el-table-column label="名称" prop="name" />
        <el-table-column label="项目" prop="project_name" />
        <el-table-column label="类型" prop="flavor_type" />
        <el-table-column label="内存" prop="ram" />
        <el-table-column label="根磁盘" prop="disk" />
        <!-- <el-table-column label="临时存储" prop="ephemeral" /> -->
        <el-table-column label="公开" prop="is_public">
          <template #default="scope">
            <el-tag :type="scope.row.is_public === true ? 'success' : 'info'">
              {{ scope.row.is_public === true ? "公开" : scope.row.vm_state }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="Metadata" prop="extra_specs" :formatter="formatObjectToString"/>
        
        <!-- <el-table-column fixed="right" label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="handleEditClick(scope.row.id, scope.row.name)"
            >
              <i-ep-edit />
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click.stop="handleDelete(scope.row.id)"
            >
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!--规格弹窗-->

    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="80%"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="computedRules"
        label-width="100px"
      >
        <el-card shadow="never">
          <el-row>
            <el-col :span="12">
              <el-form-item label="规格名" prop="name">
                <el-input v-model="formData.name" placeholder="请输入规格名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="cpu" prop="key_name">
                <el-select v-model="formData.vcpus" multiple class="full-width-input" clearable>
                  <el-option v-for="(item, index) in sgOptions" :key="index" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </el-card>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitClick">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import "/@/styles/index.scss";   // 基础样式
import { reactive, ref, computed, onMounted, defineOptions } from "vue";
import { ElForm, ElInput, ElDrawer, ElButton, ElTag, ElMessage, ElMessageBox } from "element-plus";
import  Pagination from  "/@/components/Pagination/index.vue";

defineOptions({
  name: "Flavor",
  inherititems: false,
});

import FlavorAPI, { FlavorPageQuery, FlavorPageVO, FlavorForm } from "/@/api/cmdb/flavor";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<FlavorPageQuery>({
  page: 1,
  limit: 10,
});

const tableData = ref<FlavorPageVO[]>();

// 规格弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

const formData = reactive<FlavorForm>({});

const computedRules = computed(() => {
  const rules: Partial<Record<string, any>> = {
    name: [{ required: true, message: "请输入规格名称", trigger: "blur" }],
    ram: [{ required: true, message: "请输入cpu数量", trigger: "blur" }],
  };
  return rules;
});

const formatObjectToString = (row, column, cellValue, index) => {
  // 将对象转换为字符串
  return JSON.stringify(cellValue);
};

// 查询
function handleQuery() {
  loading.value = true;
  FlavorAPI.getPage(queryParams)
    .then((data) => {
      tableData.value = data.data;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetClick() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

// 行选择
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 新增规格
function handleAddClick() {
  dialog.visible = true;
  dialog.title = "新增规格";
}

/**
 * 编辑规格
 *
 * @param id 规格ID
 */
function handleEditClick(id: number, name: string) {
  dialog.visible = true;
  dialog.title = "【" + name + "】规格修改";
  FlavorAPI.getFormData(id).then((data) => {
    Object.assign(formData, data);
  });
}

// 提交规格表单
function handleSubmitClick() {
  dataFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      loading.value = true;
      const id = formData.insid;
      if (id) {
        FlavorAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        FlavorAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭规格弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.insid = undefined;
  formData.volumeItems = [];
}
/**
 * 删除规格
 *
 * @param id 规格ID
 */
function handleDelete(id?: number) {
  const attrGroupIds = [id || ids.value].join(",");
  if (!attrGroupIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      FlavorAPI.deleteByIds(attrGroupIds).then(() => {
        ElMessage.success("删除成功");
        handleResetClick();
      });
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

/** 新增Volume项 */
function handleAddAttrClick() {
  formData.volumeItems = formData.volumeItems ?? [];
  formData.volumeItems.push({ sort: 1, status: 1 });
}

/** 删除规格项 */
function handleDeleteAttrClick(index: number) {
  if (formData.volumeItems && formData.volumeItems.length > 0) {
    formData.volumeItems.splice(index, 1);
  }
}

onMounted(() => {
  handleQuery();
});
</script>
