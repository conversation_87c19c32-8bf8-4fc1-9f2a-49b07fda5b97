import * as api from '/@/views/system/api/user';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    compute,
    CrudExpose,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import {dictionary} from '/@/utils/dictionary';
import {successMessage} from '/@/utils/message';
import {auth} from '/@/utils/authFunction';
import {SystemConfigStore} from "/@/stores/systemConfig";
import {storeToRefs} from "pinia";
import {computed} from "vue";
import { Md5 } from 'ts-md5';
import { resetToDefaultPassword } from '/@/views/system/api/user';
import { tr } from 'element-plus/es/locale';
export const createCrudOptions = function ({
  crudExpose,
  handleDrawerOpen,
  handlePerm
}: {
    crudExpose: CrudExpose;
    handleDrawerOpen: Function;
    handlePerm: Function;}
  ): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.GetList(query);
    };
    const editRequest = async ({form, row}: EditReq) => {
        form.id = row.id;
        return await api.UpdateObj(form);
    };
    const delRequest = async ({row}: DelReq) => {
        return await api.DelObj(row.id);
    };
    const addRequest = async ({form}: AddReq) => {
        return await api.AddObj(form);
    };

    const exportRequest = async (query: UserPageQuery) => {
        return await api.exportData(query)
    }
    
    const resetToDefaultPasswordRequest = async (row:EditReq)=>{
      await resetToDefaultPassword(row.id)
      successMessage("重置密码成功")
    }

    const systemConfigStore = SystemConfigStore()
    const {systemConfig} = storeToRefs(systemConfigStore)


    return {
        crudOptions: {
            table: {
                remove: {
                    confirmMessage: '是否删除该用户？',
                },
            },
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            form: {
                initialForm: {
                    password: computed(() => {
                        return systemConfig.value['base.default_password']
                    }),
                },
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('user:Create')
                    },
                    export: {
                        text: "导出",//按钮文字
                        title: "导出",//鼠标停留显示的信息
                        click() {
                            return exportRequest(crudExpose!.getSearchFormData())
                        }
                    }
                }
            },
            rowHandle: {
                //固定右侧
                fixed: 'right',
                width: 280,
                buttons: {
                    view: {
                        show: auth('user:Retrieve'),
                        text: '查看',
                        type: 'primary',
                        link: true,
                    },
                    edit: {
                        text: '编辑',
                        type: 'primary',
                        link: true,
                        show: auth('user:Update'),
                    },
                    remove: {
                        text: '删除',
                        type: 'danger',
                        link: true,
                        show: auth('user:Delete'),
                    },
                    custom: {
                        text: '重设密码',
                        type: 'primary',
                        link: true,
                        dropdown: true, //---------》给想要折叠的按钮配置dropdown为true，就会放入dropdown中《---------
                        show: auth('user:ResetPassword'),
                        //@ts-ignore
                        click: (ctx: any) => {
                            const {row} = ctx;
                            resetToDefaultPasswordRequest(row)
                        },
                    },
                    permission: {
                      type: 'primary',
                      text: '预览菜单权限',
                      link: true,
                      show: auth('role:Permission'),
                      dropdown: true, //---------》给想要折叠的按钮配置dropdown为true，就会放入dropdown中《---------
                      click: (context: any): void => {
                          const {row} = context;
                          handleDrawerOpen(row);
                      },
                    },
                    permUserRow: {
                      type: 'primary',
                      text: '预览数据权限',
                      link: true,
                      dropdown: true, //---------》给想要折叠的按钮配置dropdown为true，就会放入dropdown中《---------
                      show: auth('role:Permission'),
                      click: (context: any): void => {
                          const {row} = context;
                          handlePerm(row);
                      },
                    }
                  },
                  dropdown: {
                    // 操作列折叠，dropdown参数配置
                    // 至少几个以上的按钮才会被折叠
                    // atLeast: 2, //TODO 注意 [atLeast]参数即将废弃，请给button配置dropdown即可放入折叠
                    more: {
                      //更多按钮配置
                      text: "操作",
                      link: true,
                      // @ts-ignore
                      icon: null
                    },
                  },
            },
            columns: {
                _index: {
                    title: '序号',
                    form: {show: false},
                    column: {
                        type: 'index',
                        align: 'center',
                        width: '70px',
                        columnSetDisabled: true, //禁止在列设置中选择
                    },
                },
                username: {
                    title: '账号',
                    search: {
                        show: true,
                    },
                    type: 'input',
                    column: {
                        minWidth: 100, //最小列宽
                    },
                    form: {
                        rules: [
                            // 表单校验规则
                            {
                                required: true,
                                message: '账号必填项',
                            },
                        ],
                        component: {
                            placeholder: '请输入账号',
                        },
                    },
                },
                password: {
                    title: '密码',
                    type: 'password',
                    column: {
                        show: false,
                    },
                    editForm: {
                        show: false,
                    },
                    form: {
                        rules: [
                            // 表单校验规则
                            {
                                required: true,
                                message: '密码必填项',
                            },
                        ],
                        component: {

                            span: 12,
                            showPassword: true,
                            placeholder: '请输入密码',
                        },
                    },
                    valueResolve({form}) {
                        if (form.password) {
                            form.password = Md5.hashStr(form.password)
                        }
                    }
                },
                name: {
                    title: '姓名',
                    search: {
                        show: true,
                    },
                    type: 'input',
                    column: {
                        minWidth: 100, //最小列宽
                    },
                    form: {
                        rules: [
                            // 表单校验规则
                            {
                                required: true,
                                message: '姓名必填项',
                            },
                        ],
                        component: {
                            span: 12,
                            placeholder: '请输入姓名',
                        },
                    },
                },
                dept: {
                    title: '部门',
                    search: {
                        disabled: true,
                    },
                    type: 'dict-tree',
                    dict: dict({
                        isTree: true,
                        url: '/api/system/dept/all_dept/',
                        value: 'id',
                        label: 'name'
                    }),
                    column: {
                        minWidth: 150, //最小列宽
                    },
                    form: {
                        rules: [
                            // 表单校验规则
                            {
                                required: true,
                                message: '必填项',
                            },
                        ],
                        component: {
                            filterable: true,
                            placeholder: '请选择',
                            props: {
                                checkStrictly:true,
                                props: {
                                    value: 'id',
                                    label: 'name',
                                },
                            },
                        },
                    },
                },
                role: {
                    title: '角色',
                    search: {
                        disabled: true,
                    },
                    type: 'dict-select',
                    dict: dict({
                        url: '/api/system/role/?type=0&limit=9999',
                        value: 'id',
                        label: 'name',
                    }),
                    column: {
                        minWidth: 100, //最小列宽
                    },
                    form: {
                      valueBuilder({ form }) {
                        // [Important] 通过valueBuilder 可以修改从后台获取到的数据，适配组件所需要的value值
                        // http://fast-crud.docmirror.cn/api/crud-options/columns.html#valuebuilder与valueresolve
                        if (form.role) {
                          form.tempRole = form.role;
                        }
                      },
                      show: false,
                        rules: [
                            // 表单校验规则
                            {
                                required: true,
                                message: '必填项',
                            },
                        ],
                        component: {
                            multiple: true,
                            filterable: true,
                            placeholder: '请选择角色',
                        },
                    },
                },
                tempRole: {
                  // 临时使用，不添加此列表的值
                  title: '角色',
                  search: {
                      disabled: true,
                  },
                  type: 'dict-select',
                  dict: dict({
                      url: '/api/system/role/?type=0&limit=9999',
                      value: 'id',
                      label: 'name',
                  }),
                  column: {
                      minWidth: 100, //最小列宽
                      show: false,
                  },
                  form: {
                    // eslint-disable-next-line no-unused-vars
                    valueChange({form, value}) {
                      if (form.tempRole instanceof Array && form.data_permission_group instanceof Array) {
                        form.role = [...form.tempRole, ...form.data_permission_group]
                      }
                    },
                      rules: [
                          // 表单校验规则
                          {
                              required: true,
                              message: '必填项',
                          },
                      ],
                      component: {
                          multiple: true,
                          filterable: true,
                          placeholder: '请选择角色',
                      },
                  },
              },
                data_permission_group: {
                  title: '数据组',
                  search: {
                      disabled: true,
                  },
                  type: 'dict-select',
                  dict: dict({
                      url: '/api/system/role/?type=1&limit=9999',
                      value: 'id',
                      label: 'name',
                  }),
                  column: {
                      minWidth: 100, //最小列宽
                  },
                  form: {
                    valueChange({form, value}) {
                      if (form.tempRole instanceof Array && form.data_permission_group instanceof Array) {
                        form.role = [...form.tempRole, ...form.data_permission_group]
                      }
                    },
                    show: true,
                      rules: [
                          // 表单校验规则
                          {
                              required: false,
                              message: '必填项',
                          },
                      ],
                      component: {
                          multiple: true,
                          filterable: true,
                          placeholder: '请选择数据组',
                      },
                  },
              },
                mobile: {
                    title: '手机号码',
                    search: {
                        show: true,
                    },
                    type: 'input',
                    column: {
                        minWidth: 120, //最小列宽
                    },
                    form: {
                        rules: [
                            {
                                max: 20,
                                message: '请输入正确的手机号码',
                                trigger: 'blur',
                            },
                            {
                                pattern: /^1[3-9]\d{9}$/,
                                message: '请输入正确的手机号码',
                            },
                        ],
                        component: {
                            placeholder: '请输入手机号码',
                        },
                    },
                },
                email: {
                    title: '邮箱',
                    column: {
                        width: 260,
                    },
                    form: {
                        rules: [
                            {
                                type: 'email',
                                message: '请输入正确的邮箱地址',
                                trigger: ['blur', 'change'],
                            },
                        ],
                        component: {
                            placeholder: '请输入邮箱',
                        },
                    },
                },
                gender: {
                    title: '性别',
                    type: 'dict-select',
                    dict: dict({
                        data: dictionary('gender', undefined),
                    }),
                    form: {
                        value: 1,
                        component: {
                            span: 12,
                        },
                    },
                    component: {props: {color: 'auto'}}, // 自动染色
                },
                user_type: {
                    title: '用户类型',
                    search: {
                        show: true,
                    },
                    type: 'dict-select',
                    dict: dict({
                        data: dictionary('user_type', undefined),
                    }),
                    column: {
                        minWidth: 100, //最小列宽
                    },
                    form: {
                        show: true,
                        value: 0,
                        component: {
                            span: 12,
                        },
                    },
                },
                is_active: {
                    title: '锁定',
                    search: {
                        show: true,
                    },
                    type: 'dict-radio',
                    column: {
                        component: {
                            name: 'fs-dict-switch',
                            activeText: '',
                            inactiveText: '',
                            style: '--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6',
                            onChange: compute((context) => {
                                return () => {
                                    api.UpdateObj(context.row).then((res: APIResponseData) => {
                                        successMessage(res.msg as string);
                                    });
                                };
                            }),
                        },
                    },
                    dict: dict({
                        data: dictionary('button_status_bool', undefined),
                    }),
                },
                post: {
                  title: '岗位',
                    search: {
                        disabled: false,
                    },
                    type: 'dict-select',
                    dict: dict({
                        url: '/api/system/post/?limit=99999',
                        value: 'id',
                        label: 'name',
                    }),
                    column: {
                        minWidth: 100, //最小列宽
                    },
                    form: {
                        rules: [
                            // 表单校验规则
                            {
                                required: true,
                                message: '必填项',
                            },
                        ],
                        component: {
                            multiple: true,
                            filterable: true,
                            placeholder: '请选择岗位',
                        },
                    },
                },
                post_status: {
                  title: '状态',
                  type: 'dict-select',
                  dict: dict({
                      data: dictionary('system:post:status', undefined),
                  }),
                  form: {
                      value: 1,
                      component: {
                          span: 12,
                      },
                  },
                  component: {props: {color: 'auto'}}, // 自动染色
                },
                avatar: {
                    title: '头像',
                    type: 'avatar-cropper',
                    form: {
                        show: false,
                    },
                    column: {
                        minWidth: 400, //最小列宽
                    },
                },
            },
        },
    };
};
