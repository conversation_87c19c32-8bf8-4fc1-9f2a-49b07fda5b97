import { CrudOptions, AddReq, DelReq, EditReq, dict, CrudExpose, UserPageQuery, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import _ from 'lodash-es';
import * as api from './api';
import { request } from '/@/utils/service';
import { auth } from "/@/utils/authFunction";

//此处为crudOptions配置
export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
  const pageRequest = async (query: any) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    if (row.id) {
      form.id = row.id;
    }
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  const exportRequest = async (query: UserPageQuery) => {
    return await api.exportData(query)
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('JobModelViewSet:Create'),
          },
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            text: '查看',
            type: 'text',
            show: auth('JobDemoModelViewSet:Retrieve')
          },
          edit: {
            text: '编辑',
            type: 'text',
            show: auth('JobDemoModelViewSet:Update')
          },
          copy: {
            text: '复制',
            type: 'text',
            show: auth('JobDemoModelViewSet:Copy')
          },
          remove: {
            text: '删除',
            type: 'text',
            show: auth('JobDemoModelViewSet:Delete')
          },
        },
      },
      columns: {
        name: {
          title: '作业名称',
          type: 'input',
          search: { show: true },
          column: {
            minWidth: 120,
          },
          form: {
            rules: [{ required: true, message: '作业名称必填' }],
            component: {
              placeholder: '请输入作业名称',
            },
          },
        },
        template: {
          title: '模板',
          type: 'dict-select',
          search: { show: true },
          column: { minWidth: 100 },
          dict: dict({
            url: '/api/job/TemplateModelViewSet/',
            value: 'id',
            label: 'name'
          }),
          form: {
            rules: [{ required: true, message: '必填项', },],
            component: {
              filterable: true,
              placeholder: '请选择模板',
              props: {
                checkStrictly: true,
                props: {
                  value: 'id',
                  label: 'name',
                },
              },
            },
          },
        },
        create_datetime: {
          title: '创建时间',
          type: 'datetime',
          search: { show: false },
          form: {
            show: false,
            component: {
              //显示格式化
              //输入输出值格式化
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              editable: {
                //该列是否禁用编辑, boolean | TableColumnEditableDisabledFunc;
                //比table.editable.isEditable优先级更高
                disabled: true,
              }
            }
          },
          column: {
            align: "center",
            minWidth: 120,
            component: { name: "fs-date-format", format: "YYYY-MM-DD HH:mm:ss" },

          }
        },

      },
    },
  };
}