import * as api from '/@/api/report_datav/report';
import { useRouter } from 'vue-router';
import {
	UserPageQuery,
	AddReq,
	DelReq,
	EditReq,
	CreateCrudOptionsProps,
	CreateCrudOptionsRet,
	dict,
	// ValueBuilderContext,
	// ValueResolveContext,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { verifyKey } from '/@/utils/toolsValidate';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};
	const validateKey = async (rule: object, value: string) => {
		if (!verifyKey(value)) {
			throw new Error('仅允许英文及—_');
		}
	};
	const router = useRouter();

	return {
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			actionbar: {
				buttons: {
					add: {
						show: false,
						plain: true,
						type: 'primary',
					},
				},
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 180,
				buttons: {
					view: {
						show: auth('report_datav:report:Retrieve'),
						type: 'primary',
						link: true,
						click(context) {
							router.push({ name: context.row.path_name, params: { reportId: context.row.id } });
						},
					},
					edit: {
						link: true,
						type: 'primary',
						show: false,
					},
					remove: {
						link: true,
						type: 'danger',
						show: auth('report_datav:report:Delete'),
					},
				},
			},
			pagination: {
				show: true,
				'default-page-size': 10,
				'default-current': 1,
			},
			table: {
				rowKey: 'id',
			},
			columns: {
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
						// columnSetDisabled: true, //禁止在列设置中选择
					},
				},
				name: {
					title: '报表名称',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'input',
					column: {
						minWidth: 100,
						showOverflowTooltip: true,
						sortable: 'custom',
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '报表名称必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入报表名称',
						},
					},
				},
				code: {
					title: '报表编码',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 100,
						showOverflowTooltip: true,
						sortable: 'custom',
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '报表编码必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
							{ validator: validateKey, message: '仅允许英文及_-', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入报表编码',
						},
					},
				},
				description: {
					title: '描述',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 100,
						showOverflowTooltip: true,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '描述必填项' },
							{ max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入描述',
						},
					},
				},
				category: {
					title: '报表分类',
					search: {
						show: true,
					},
					column: {
						minWidth: 90,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('report_datav:report:category', undefined),
					}),
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '报表分类必填项' },
						],
						component: {
							placeholder: '请输入报表分类',
						},
					},
				},
				path_name: {
					title: '报表前端路径名称',
					search: {
						show: false,
					},
					column: {
						minWidth: 90,
					},
					type: 'input',
					form: {
						show: false,
						rules: [
							// 表单校验规则
							{ required: true, message: '报表前端路径名称必填项' },
						],
						component: {
							placeholder: '请输入报表前端路径名称',
						},
					},
				},
				// source_data: {
				// 	title: '元数据',
				// 	type: 'json',
				// 	form: {
				//     component: {
				//       name: 'fs-json-editor',
				//       showBtns: false,
				//       mode: 'code',
				//       rules: [
				//         // 表单校验规则
				//         // {required: false, message: '参数配置必填项'},
				//       ],
				//     },
				//     valueBuilder({ form }: ValueBuilderContext) {
				//       if (form.options == null) {
				//         return;
				//       }
				//       form.options = JSON.parse(form.options);
				//     },
				// 		valueResolve({ form }: ValueResolveContext) {
				// 			if (form.options == null) {
				// 				return;
				// 			}
				// 			form.options = JSON.stringify(form.options);
				// 		},
				// 	},
				// },
			},
		},
	};
};
