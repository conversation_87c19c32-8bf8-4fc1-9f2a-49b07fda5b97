<template>
	<div>
		<slot name="header"></slot>
		<el-form :model="editForm[props.resourceType]" ref="editFormRef">
			<component
				:is="resourceTypeComp[props.resourceType]"
				:item="editForm[props.resourceType]"
				:method="props.method"
			></component>
			<el-descriptions size="default" border>
				<template #title>
					<div style="font-size: 18px;margin:10px 0 0 15px">相关附件</div>
				</template>
				<el-descriptions-item width="160">
					<template #label>
						<div class="upload-section">
							<el-upload
								action
								:http-request="uploadToBackend"
								multiple
								v-model:file-list="editFileList"
								:show-file-list="false"
							>
								<el-button :disabled="isDetail" type="success" plain size="large" icon="Upload">上传附件</el-button>
							</el-upload>
						</div>
					</template>
					<AttachmentList
						:resultList="editForm.accessory"
						:method="props.method"
						@delete="handleDelete"
					/>
				</el-descriptions-item>
				<!-- <el-descriptions size="large" title="备注信息" border>
					<el-form-item label>
						<el-input type="textarea" v-model="editForm.description" placeholder="填写任务描述(必填)"></el-input>
					</el-form-item>
				</el-descriptions>-->
			</el-descriptions>
			<span class="span-button">
				<el-button
					@click="submit()"
					v-if="['Update','Create'].includes(props.method)"
					type="primary"
					plain
					style="margin:10px 0 40px 0;width:100px"
					size="large"
				>{{ props.method=="Update"?"更新":"创建" }}</el-button>

				<!-- 详情页的逻辑 -->
				<template v-if="props.resourceType === 'contract'">
					<template v-if="isDetail">
						<!-- 待提交或审批驳回时显示提交按钮 -->
						<el-button
							v-if="['待提交', '审批驳回'].includes(editForm.contract.status) && auth('contract:contract:Submit')"
							@click="resubmit('待审批')"
							type="primary"
							plain
							style="margin:10px 10px 40px 0;width:100px"
							size="large"
						>提交审批</el-button>

						<!-- 审批通过显示提前完结按钮 -->
						<el-button
							v-if="['审批通过'].includes(editForm.contract.status) && auth('contract:contract:FinishAhead')"
							@click="resubmit('提前完结')"
							type="primary"
							plain
							style="margin:10px 10px 40px 0;width:100px"
							size="large"
						>提前完结</el-button>

						<!-- 审批通过显示提前完结按钮 -->
						<el-button
							v-if="['提前完结'].includes(editForm.contract.status) && auth('contract:contract:FinishRecall')"
							@click="resubmit('审批通过')"
							type="primary"
							plain
							style="margin:10px 10px 40px 0;width:100px"
							size="large"
						>撤回提前完结</el-button>

						<el-button
							v-if="['审批通过','提前完结','已归档'].includes(editForm.contract.status) && auth('contract:payment:Search') && editForm.contract.is_payment === true"
							@click="viewCurPayment"
							type="primary"
							plain
							style="margin:10px 10px 40px 0;width:100px"
							size="large"
						>查看回款</el-button>
					</template>
					<template v-if="props.method === 'Approval'">
						<el-button
							@click="resubmit('审批通过')"
							type="success"
							plain
							style="margin:10px 10px 40px 0;width:100px"
							size="large"
						>通过</el-button>
						<el-button
							@click="resubmit('审批驳回')"
							type="danger"
							plain
							style="margin:10px 0 40px 0;width:100px"
							size="large"
						>驳回</el-button>
					</template>
				</template>
			</span>
		</el-form>
	</div>
</template>
<script setup>
import { ref,onBeforeMount,reactive,toRefs,markRaw,computed } from 'vue'
import { useRouter } from 'vue-router'
import * as api from '/@/api/contract/accessory';
import Contract from './CreateOrEditFrame/Contract.vue';
import Payment from './CreateOrEditFrame/Payment.vue';
import AttachmentList from './AttachmentList.vue';
import { ElMessage } from 'element-plus'
import { auth } from '/@/utils/authFunction';
const router = useRouter()
// 接收参数
const props = defineProps({
    item: {
        type: Object,
    },
    method: {
        type: String,
        default: "Create"
    },
    resourceType: {
        type: String,
        default: "contract"
    },
})

const data = reactive({
  editForm: {
    accessory:[],
    delete_accessory_list:[]
  },
  oldEditForm: {},
  resourceTypeComp: {
    'contract':markRaw(Contract),
    'payment':markRaw(Payment)
  },
  editFileList: [],
})

let editFormRef = ref(null);
const emits = defineEmits(['submitHandler'])
const isDetail = computed(() => props.method === 'Detail')

const submit = () => {
	editFormRef.value.validate((valid) => {
		if (valid) {
      emits('submitHandler',data.editForm)
		} else {
			ElMessage.warning('请完善表单数据');
		}
	});
};

const resubmit = (status) => {
    if (status === "审批驳回") {
        data.editForm = data.oldEditForm
    }
    data.editForm.contract.status = status
    submit()
}

const viewCurPayment = () => {
  router.replace({
    path: '/purchaseSalesManage/payment',
    query: {
      code: data.editForm.contract.code
    }
  })
}

const uploadToBackend = () => {
  	api.AddObj(data.editFileList.map((item) => item.raw)).then((response) => {
		if (response.code === 2000) {
      data.editForm.accessory = [...data.editForm.accessory, response.data]
			ElMessage.success(response.msg);
		} else {
			ElMessage.error(response.msg);
		}
    });
    data.editFileList = []
}

const handleDelete = (id) => {
    data.editForm.accessory = data.editForm.accessory.filter(item => item.id !== id)
    data.editForm.delete_accessory_list.push(id)
}

const initPaymentForm = () => {
    data.editForm.payment = {}
    data.editForm.payment.agent_info = {}
    data.editForm.payment.invoice_type = "企业"
    data.editForm.payment.payment_list = []
    data.editForm.payment.payee = "杭州星哉科技有限公司"
}

const initContractForm = () => {
    data.editForm.accessory = []
    data.editForm.delete_accessory_list = []
    data.editForm.contract = {}
    data.editForm.contract.sku_list = []
    data.editForm.contract.customer = {}
    data.editForm.contract.pay_type = "后付"
    data.editForm.contract.code = generate16DigitRandom()
    data.editForm.contract.second_party = "杭州星哉科技有限公司"
    data.editForm.contract.type = "新签"
}

function generate16DigitRandom() {
  const now = new Date();
  const pad = (num, size = 2) => String(num).padStart(size, '0');
  const timestamp =
    now.getFullYear().toString() +
    pad(now.getMonth() + 1) +
    pad(now.getDate()) +
    pad(now.getHours()) +
    pad(now.getMinutes()) +
    pad(now.getSeconds()) +
    String(now.getMilliseconds()).padStart(3, '0'); // 毫秒填充到3位
  return timestamp;
}

onBeforeMount(()=>{
    if (props.method != "Create"){
        data.editForm = JSON.parse(JSON.stringify(props.item));
        data.editForm.delete_accessory_list = []
        if (props.resourceType == "contract"){
            if (data.editForm.contract.customer == null || data.editForm.contract.customer == undefined){data.editForm.contract.customer={}}
            for(var i=0;i<data.editForm.contract.sku_list.length;i++){
              if(data.editForm.contract.sku_list[i].duration == null || data.editForm.contract.sku_list[i].duration == undefined){
                data.editForm.contract.sku_list[i].duration = {}
                data.editForm.contract.sku_list[i].duration.amount = 1
                data.editForm.contract.sku_list[i].duration.unit = "month"
              }
            }
        }
        else {
            if (data.editForm.payment.agent_info == null || data.editForm.payment.agent_info == undefined){data.editForm.payment.agent_info={}}
            data.editForm.payment.contract_id = data.editForm.payment.contract_code
        }
        data.oldEditForm = JSON.parse(JSON.stringify(data.editForm));
    }
    else{
      if (props.resourceType == "contract"){
        initContractForm()
      }
      if (props.resourceType == "payment"){
        initPaymentForm()
      }
    }
})
const { editForm,resourceTypeComp,editFileList } = toRefs(data);
</script>

<style scoped>
.span-button {
	margin: 10px 0 0 13px;
	display: block;
}
</style>
