<template>
	<div>
		<el-drawer v-model="dialogVisible" :show-close="true" size="60%">
			<template #header="{ close, titleId, titleClass }">
				<h4 :id="titleId" :class="titleClass">文件预览</h4>
			</template>
			<DocumentEditor
				id="docEditor"
				documentServerUrl="http://***********/"
				:config="config"
				:events_onDocumentReady="onDocumentReady"
				:onLoadComponentError="onLoadComponentError"
			/>
		</el-drawer>
	</div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, watch } from 'vue';
import { DocumentEditor } from '@onlyoffice/document-editor-vue';

// 接收参数
const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	documentDict: {
		type: Object,
		default: () => ({}),
	},
});

const fileTypeToDocumentType = {
	// Word 文档类型
	doc: 'word',
	docx: 'word',
	txt: 'word',
	html: 'word',
	pdf: 'word',

	// 表格类型
	xls: 'cell',
	xlsx: 'cell',
	csv: 'cell',

	// 演示文稿类型
	ppt: 'slide',
	pptx: 'slide',
};

// 定义 emit
const emit = defineEmits(['update:modelValue']);

// 本地 computed 双向绑定
const dialogVisible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value),
});

const config = ref({
	document: {
		fileType: 'docx',
		key: '',
		title: 'new.docx',
		url: '',
	},
	documentType: 'word',
	editorConfig: {
		user: {
			id: 'onlyoffice',
			name: 'onlyoffice',
		},
		callbackUrl: ``, // 处理保存回调
		mode: 'view', // 允许编辑和保存
	},
});

// 监听 props.documentDict，动态赋值 config
watch(
	() => props.documentDict,
	(newDocumentDict) => {
		config.value.document.url = newDocumentDict.url;
		config.value.document.key = newDocumentDict.key;
		config.value.document.title = newDocumentDict.title;
		config.value.document.fileType = newDocumentDict.fileType;
		config.value.documentType = fileTypeToDocumentType[newDocumentDict.fileType];
	},
	{ deep: true }
);

const onDocumentReady = () => {
	console.log('Document is loaded');
};

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
	switch (errorCode) {
		case -1:
			console.log('Unknown error:', errorDescription);
			break;
		case -2:
			console.log('Error loading DocsAPI:', errorDescription);
			break;
		case -3:
			console.log('DocsAPI is not defined:', errorDescription);
			break;
	}
};
</script>
