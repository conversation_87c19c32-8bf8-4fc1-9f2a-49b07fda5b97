import * as api from '/@/api/tenant/opIronicHypervisorRegister';
import { UserPageQuery, AddReq, DelReq, EditReq, CreateCrudOptionsProps, CreateCrudOptionsRet, dict } from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { ref, computed, h } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { toOPIronHypervisiorDetail } from '/@/router/intervalRouterTo/tenant';
import { fa } from 'element-plus/es/locale';

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const router = useRouter();
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};

	let selectedIds = ref([]);

	const onSelectionChange = (changed: any) => {
		selectedIds.value = changed.map((item: any) => item.id);
	};
	const selectionRegisterButtonShowComputed = computed(() => {
		const isShow = auth('tenant:tenantIronicHypervisiorRegister:startRegister') && selectedIds.value.length > 0;
		return isShow;
	});

	return {
		selectedIds,
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			actionbar: {
				buttons: {
					add: {
						show: auth('tenant:tenantIronicHypervisiorRegister:Create'),
						plain: true,
						order: 2,
					},
					selectionsDeleted: {
						text: '批量注册',
						type: 'danger',
						order: 1,
						plain: true,
						show: selectionRegisterButtonShowComputed,
						click: (): void => {
							if (selectedIds.value.length === 0) {
								ElMessage.warning('请先勾选');
								return;
							}
							ElMessageBox.confirm(
								h('p', null, [
									h('span', null, '确定开始注册裸机节点任务 '),
									h('i', { style: 'color: red' }, selectedIds.value.length),
									h('span', null, ' 个记录吗？'),
								]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.ManageStartRegister({ register_ids: selectedIds.value }).then((response: any) => {
										if (response.code === 2000) {
											console.error(response);
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消注册裸机任务',
									});
								});
						},
					},
				},
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 280,
				buttons: {
					view: {
						type: 'primary',
						link: true,
						show: auth('tenant:tenantIronicHypervisiorRegister:Retrieve'),
					},
					edit: {
						type: 'primary',
						link: true,
						show: auth('tenant:tenantIronicHypervisiorRegister:Update'),
					},
					remove: {
						type: 'danger',
						link: true,
						show: auth('tenant:tenantIronicHypervisiorRegister:Delete'),
					},
					// viewLog: {
					// 	type: 'primary',
					// 	text: '查看日志',
					// 	link: true,
					// 	show: auth('system:auditLog:GetResourceLogs'),
					// 	click(context) {
					// 		router.push('/system/resourceChangeRecords/detail?resource_id=' + context.row.id);
					// 	},
					// },
				},
			},
			pagination: {
				show: true,
			},
			table: {
				rowKey: 'id',
				border: false,
				onSelectionChange,
			},
			form: {
				labelWidth: 100,
			},
			columns: {
				$checked: {
					title: '选择',
					form: { show: false },
					column: {
						type: 'selection',
						align: 'left',
						width: '55px',
						// selectable(row, index) {
						//   return row.id !== 1; //设置第一行不允许选择
						// }
					},
				},
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
						columnSetDisabled: true, //禁止在列设置中选择
					},
				},
				name: {
					title: '裸机节点名称',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '裸机节点名称为必填项' },
							{ min: 1, max: 255, message: '最小: 1, 最大: 255', trigger: 'blur' },
						],
						component: {
							placeholder: '请输入裸机节点名称',
						},
					},
				},
				description: {
					title: '描述',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
						],
						component: {
							placeholder: '请输入描述',
						},
					},
				},
        sn: {
					title: '序列号',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '序列号为必填项' },
						],
						component: {
							placeholder: '请输入序列号',
						},
					},
				},
				node_resource_class: {
					title: '资源类',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '资源类为必填项' },
						],
						component: {
							placeholder: '请输入资源类',
						},
					},
				},
				node_network_interface: {
					title: '网络接口',
					search: {
						show: false,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('cloud_resource_pool:basical_config:baremetral_node:network_interface', undefined),
					}),
					column: {
						minWidth: 90,
						show: false,
					},
					form: {
						value: 'neutron',
						rules: [
							// 表单校验规则
							{ required: true, message: '网络接口为必填项' },
						],
						component: {
							placeholder: '请选择网络接口',
							disabled: true,
						},
					},
				},
				node_driver: {
					title: '节点驱动',
					search: {
						show: true,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('cloud_resource_pool:basical_config:baremetral_node:driver', undefined),
					}),
					column: {
						minWidth: 90,
					},
					form: {
						value: 'ipmi',
						helper: {
							render() {
								return (
									<div>
										默认为"ipmi",<span style="color:red">非特殊需要请勿修改！</span>
									</div>
								);
							},
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '节点驱动为必填项' },
						],
						component: {
							placeholder: '请选择节点驱动',
						},
					},
				},
        node_driver_ipmi_username: {
					title: 'IPMI用户名',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: 'IPMI用户名为必填项' },
						],
						component: {
							placeholder: '请输入IPMI用户名',
						},
					},
				},
        node_driver_ipmi_password: {
					title: 'IPMI密码',
					search: {
						show: false,
					},
					type: 'password',
					column: {
						minWidth: 90,
            show: false,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: 'IPMI密码为必填项' },
						],
						component: {
							placeholder: '请输入IPMI密码',
						},
					},
				},
        node_driver_ipmi_address: {
					title: 'IPMI-IP地址',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
            show: false,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: 'IPMI-IP地址为必填项' },
						],
						component: {
							placeholder: '请输入IPMI-IP地址',
						},
					},
				},
				node_properties: {
					title: '裸机属性',
					search: {
						show: false,
					},
					type: 'input',
					column: {
						minWidth: 90,
						show: false,
					},
					form: {
						value: '{"cpus": "144","memory_mb": "515072","local_gb": "400","cpu_arch": "x86_64","capabilities": "boot_option:local,disk_label:gpt"}',
						helper: {
							render() {
								return <div style="color:red">属性需配置为json格式的字符</div>;
							},
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '裸机属性为必填项' },
						],
						component: {
							placeholder: '请输入裸机属性',
						},
					},
				},
				port_group_mode: {
					title: '端口组模式',
					search: {
						show: false,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('cloud_resource_pool:basical_config:baremetral_node:portgroup_mode', undefined),
					}),
					column: {
						minWidth: 90,
						show: false,
					},
					form: {
						value: '802.3ad',
						helper: {
							render() {
								return (
									<div>
										默认为"802.3ad",<span style="color:red">非特殊需要请勿修改！</span>
									</div>
								);
							},
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '端口组模式为必填项' },
						],
						component: {
							placeholder: '请选择端口组模式',
						},
					},
				},
				port_group_properties: {
					title: '端口组属性',
					search: {
						show: false,
					},
					type: 'input',
					column: {
						minWidth: 90,
						show: false,
					},
					form: {
						value: '{"miimon": "100","xmit_hash_policy": "layer2+3"}',
						helper: {
							render() {
								return <div style="color:red">属性需配置为json格式的字符</div>;
							},
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '端口组属性为必填项' },
						],
						component: {
							placeholder: '请输入端口组属性',
						},
					},
				},
				port_group_standalone_ports_supported: {
					title: '端口组SAPorts',
					search: {
						show: false,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('button_whether_bool', undefined),
					}),
					column: {
						minWidth: 90,
						show: false,
					},
					form: {
						value: true,
						helper: {
							render() {
								return <div style="color:red">指定在该端口组中的端口是否可以用作独立端口</div>;
							},
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '端口组SAPorts为必填项' },
						],
						component: {
							placeholder: '请选择是否可以作为独立端口',
						},
					},
				},
				port_switch_id: {
					title: '交换机ID',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '交换机ID为必填项' },
						],
						component: {
							placeholder: '请输入交换机ID',
						},
					},
				},
				port_switch_info: {
					title: '交换机信息',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '交换机信息为必填项' },
						],
						component: {
							placeholder: '请输入交换机信息',
						},
					},
				},
				port_port_id: {
					title: '端口ID',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '端口ID为必填项' },
						],
						component: {
							placeholder: '请输入端口ID',
						},
					},
				},
				port_hardware_address: {
					title: '裸机网络mac1',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '裸机网络mac1为必填项' },
						],
						component: {
							placeholder: '请输入裸机网络mac1',
						},
					},
				},
				port_network_address: {
					title: '裸机网络mac2',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '裸机网络mac2为必填项' },
						],
						component: {
							placeholder: '请输入裸机网络mac2',
						},
					},
				},
				register_status: {
					title: '注册任务状态',
					search: {
						show: false,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('cloud_resource_pool:basical_config:baremetral_node:register_status', undefined),
					}),
					column: {
						minWidth: 90,
					},
					form: {
						show: false,
						rules: [
							// 表单校验规则
							{ required: true, message: '注册任务状态为必填项' },
						],
						component: {
							placeholder: '请输入注册任务状态',
						},
					},
				},
				// is_success: {
				// 	title: '是否成功',
				// 	search: {
				// 		show: true,
				// 	},
				//   type: 'dict-select',
				//   dict: dict({
				//     data: dictionary('button_whether_bool', undefined)
				//   }),
				// 	column: {
				// 		minWidth: 90,
				// 	},
				// 	form: {
				//     show: false,
				// 		rules: [
				// 			// 表单校验规则
				// 			{ required: true, message: '是否成功为必填项' },
				// 		],
				// 		component: {
				// 			placeholder: '请选择是否成功',
				// 		},
				// 	},
				// },
				created_logger_info: {
					title: '注册任务日志',
					search: {
						show: false,
					},
					type: 'input',
					column: {
						minWidth: 90,
						show: false,
					},
					form: {
						show: false,
						rules: [
							// 表单校验规则
							{ required: true, message: '注册任务日志为必填项' },
						],
						component: {
							placeholder: '请输入注册任务日志',
						},
					},
				},
				created_hyp_ironic_node: {
					title: '裸机节点',
					search: {
						show: true,
					},
					type: 'link',
					column: {
						minWidth: 90,
						showTitle: '点击查看详情',
						component: {
							on: {
								// 注意：必须要on前缀
								onClick({ row }) {
									if (row.created_hyp_ironic_node) {
										toOPIronHypervisiorDetail(row.created_hyp_ironic_node);
									} else {
										ElMessage.error('裸机节点ID不存在，请联系管理员确认原因！');
									}
								},
							},
						},
					},
					form: {
						show: false,
						rules: [
							// 表单校验规则
							{ required: true, message: '裸机节点为必填项' },
						],
						component: {
							placeholder: '请输入裸机节点',
						},
					},
				},
				node: {
					title: '节点区域',
					search: {
						show: true,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/tenants/tenant-op-setting/get_op_all_nodes/',
						immediate: false,
					}),
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: '节点区域为必填项' },
						],
						component: {
							placeholder: '请输入节点区域',
							disabled: false,
						},
					},
				},
			},
		},
	};
};
