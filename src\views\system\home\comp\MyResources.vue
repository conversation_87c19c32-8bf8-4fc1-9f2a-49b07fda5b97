<template>
  <tiny-card custom-class="stat-cards" type="text">
    <h1 class="dashboard-sub-title">我的资源</h1>
    <div>
      <tiny-layout>
        <tiny-row :flex="true">
          <tiny-col :span="4">
            <tiny-statistic
              :value="props.stats.total"
              :title="{ position: 'top' }"
            >
            <template #title>
              <span style="font-weight: bolder;">裸金属</span>
            </template>
            </tiny-statistic>
          </tiny-col>
          <tiny-col :span="4">
            <tiny-statistic
              :value="props.stats.running"
              :title="{ position: 'top' }"
            >
            <template #title>
              <span style="font-weight: bolder;">运行中</span>
            </template>
            </tiny-statistic>
          </tiny-col>
          <tiny-col :span="4">
          <tiny-statistic
              :value="props.stats.gpu_counts"
              :title="{ position: 'top' }"
            >
            <template #title>
              <span style="font-weight: bolder;">GPU数量</span>
            </template>
            </tiny-statistic>
          </tiny-col>
          <tiny-col :span="4">
            <tiny-statistic
              :value="props.stats.coming_expire"
              :value-style="[{ 'color': '#eb1212' }]"
              :title="{ position: 'top' }"
            >
            <template #title>
              <span style="font-weight:bolder;">即将过期&nbsp;</span>
              
              <tiny-tooltip content="未来15天内即将过期的裸金属" placement="top" effect="light">
                <tiny-icon-icon-unknow></tiny-icon-icon-unknow>
              </tiny-tooltip>
            </template>
            </tiny-statistic>
          </tiny-col>
          <tiny-col :span="4">
            <tiny-statistic
              :value="props.stats.had_expired"
              :value-style="[{ 'color': '#eb1212' }]"
              :title="{ position: 'top' }"
            >
            <template #title>
              <span style="font-weight:bolder;">已过期&nbsp;</span>
              
              <tiny-tooltip content="近15天内已过期的裸金属" placement="top" effect="light">
                <tiny-icon-icon-unknow></tiny-icon-icon-unknow>
              </tiny-tooltip>
            </template>
            </tiny-statistic>
          </tiny-col>
          <tiny-col :span="4">
            <tiny-statistic
              :value="props.stats.coming_created"
              :title="{ position: 'top' }"
            >
            <template #title>
              <span style="font-weight:bolder;">近期创建&nbsp;</span>   
              <tiny-tooltip content="近7天创建的裸金属" placement="top" effect="light">
                <tiny-icon-icon-unknow></tiny-icon-icon-unknow>
              </tiny-tooltip></template>
            </tiny-statistic>
          </tiny-col>
        </tiny-row>
      </tiny-layout>
    </div>
  </tiny-card>
</template>
 
<script setup lang="ts">
import { PropType } from 'vue';
import { TinyCard, TinyStatistic, TinyRow, TinyCol, TinyLayout, TinyTooltip } from '@opentiny/vue';
import { iconUnknow } from '@opentiny/vue-icon';


const props = defineProps({
  stats: {
    type: Object as PropType<{
      total: number 
      running: number 
      coming_expire: number 
      coming_created: number
      had_expired: number
      gpu_counts: number
    }>,
    required: true 
  }
});
 
const TinyIconIconUnknow = iconUnknow()
 
</script>

<style scoped>
.dashboard-sub-title {
  font-size: 16px;
  margin-bottom: 20px;
}
/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px); /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important; /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>