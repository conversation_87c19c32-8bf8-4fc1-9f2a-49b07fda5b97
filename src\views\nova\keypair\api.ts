
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/keypair";

class KeypairAPI {
  /**
   * 获取秘钥对分页列表
   *
   * @param queryParams 查询参数
   * @returns 秘钥对分页结果
   */
  static getPage(queryParams: KeypairPageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取秘钥对表单数据
   *
   * @param id 秘钥对ID
   * @returns 秘钥对表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 获取秘钥对列表
   *
   * @returns 秘钥对列表
   */
  static getList(project_name?: string, user_id?: string) {
    let url = `${BASE_URL}/list/`
    if (project_name && user_id) {
      url += `?project_name=${project_name}&user_id=${user_id}`;
    }
    return request({
      url: url,
      method: "get",
    });
  }

}

export default KeypairAPI;

/**
 * 秘钥对查询参数
 */
export interface KeypairPageQuery extends PageQuery {
  search?: string;    // keywords
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}

/**
 * 秘钥对分页数组中的单个对象
 */
export interface KeypairPageVO {
  keypair_id: string;
  name: string;
  public_key: string;
  fingerprint: string;
  type: string;
  project_id: string;
  project_name: string;
  user_id: string;
  user_name: string;
}

/**
 * 秘钥对表单
 */
export interface KeypairForm {
  keypair_id?: string;
  name?: string;
  public_key?: string;
  fingerprint?: string;
  type?: string;
  project_id?: string;
  project_name?: string;
  user_id?: string;
  user_name?: string;
}
