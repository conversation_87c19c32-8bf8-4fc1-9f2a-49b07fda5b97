<!-- XTermLogViewer.vue -->
<template>
  <div ref="xtermContainer" class="terminal"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount, computed, PropType, } from 'vue';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import 'xterm/css/xterm.css';

// 定义 props 类型
interface LogEntry {
  timestamp: string;
  level: string;
  msg: string;
}

// 定义 props 并设置默认值
const props = defineProps({
  logs: {
    type: Array as PropType<LogEntry[]>,
    default: () => [
      { timestamp: new Date().toISOString(), level: 'INFO', msg: 'No log entries available.' },
    ],
  },
  fontSize: {
    type: Number,
    default: 14,
  },
  theme: {
    // @ts-ignore
    type: Object as PropType<Terminal.ITheme>,
    default: () => ({
      foreground: '#ffffff', // 字体
      background: '#000000', // 背景色
      cursor: 'help' // 设置光标
    }),
  },
});

// 确保传递给终端的 logs 总是一个数组
const safeLogs = computed(() => {
  if (Array.isArray(props.logs)) {
    return props.logs;
  } else {
    return [
      { timestamp: new Date().toISOString(), level: 'ERROR', msg: 'Invalid log data format.' },
    ];
  }
});

// 定义一个引用，用于指向DOM元素
const xtermContainer = ref<HTMLElement | null>(null);

let terminal: Terminal | null = null;
let fitAddon: FitAddon | null = null;

// 初始化终端并加载日志数据
onMounted(() => {
  if (!xtermContainer.value) return;

  // 创建一个新的终端实例
  terminal = new Terminal({
    cursorBlink: true,
    fontSize: props.fontSize,
    theme: props.theme,
  });

  // 添加fit插件以自动调整大小
  fitAddon = new FitAddon();
  terminal.loadAddon(fitAddon);

  // 将终端挂载到DOM元素
  terminal.open(xtermContainer.value);
  fitAddon.fit();

  // 加载并显示传入的日志数据
  displayLogs(terminal, safeLogs.value);
});

// 当 props.logs 发生变化时，更新终端内容
watch(safeLogs, (newLogs) => {
  if (terminal) {
    terminal.clear();
    displayLogs(terminal, newLogs);
  }
}, { deep: true });

// 显示日志数据的函数
const displayLogs = (term: Terminal, logs: LogEntry[]) => {
  logs.forEach(log => {
    const logLine = formatLogEntry(log);
    term.write(logLine);
  });
};

// 格式化日志条目
const formatLogEntry = (log: LogEntry): string => {
  let formattedMessage = `[${log.timestamp}] ${log.level}: ${log.msg}\n`;
  switch (log.level.toLowerCase()) {
    case 'info':
      formattedMessage = `\x1b[34m${formattedMessage}\x1b[0m`; // 蓝色
      break;
    case 'error':
      formattedMessage = `\x1b[31m${formattedMessage}\x1b[0m`; // 红色
      break;
    case 'warn':
      formattedMessage = `\x1b[33m${formattedMessage}\x1b[0m`; // 黄色
      break;
    case 'debug':
      formattedMessage = `\x1b[36m${formattedMessage}\x1b[0m`; // 青色
      break;
    default:
      formattedMessage = `\x1b[37m${formattedMessage}\x1b[0m`; // 白色
  }
  return formattedMessage;
};

// 清理资源
onBeforeUnmount(() => {
  if (terminal) {
    terminal.dispose();
    terminal = null;
  }
  if (fitAddon) {
    fitAddon.dispose();
    fitAddon = null;
  }
});
</script>

<style scoped>
.terminal {
  width: 100%;
  height: 400px;
  border: 1px solid #ccc;
}
</style>