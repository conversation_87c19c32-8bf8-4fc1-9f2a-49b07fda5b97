/*
* 判断对象类型
* 判断对象是否为可迭代对象
*/
export function isIterable(obj: any): boolean {
  return obj != null && typeof obj[Symbol.iterator] === 'function';
}


/*
* 判断对象
* 判断对象是否为 Object
* // 测试
* console.log(isObject({})); // true
* console.log(isObject([])); // true
* console.log(isObject(function() {})); // true
* console.log(isObject(null)); // false
* console.log(isObject(42)); // false
* 
* console.log(isPlainObject({})); // true
* console.log(isPlainObject([])); // false，因为数组不是一个普通的对象
* console.log(isPlainObject(new Date())); // false，因为Date是一个特殊的对象
* console.log(isPlainObject(null)); // false
* console.log(isPlainObject(42)); // false
*/
export function isPlainObject(value: any): boolean {
  return Object.prototype.toString.call(value) === '[object Object]';
}
