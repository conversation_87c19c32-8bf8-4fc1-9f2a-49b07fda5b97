<!-- 子网 -->
<template>
  <div class="app-container">
    
      <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="queryParams.search"
            placeholder="输入子网ID、名称进行搜索"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetClick()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>


    <el-card shadow="never">
      <div class="mb-[10px]">
        <el-button type="success" @click="handleAddClick()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button
          type="danger"
          :disabled="ids.length === 0"
          @click="handleDelete()"
        >
          <i-ep-delete />
          删除
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        highlight-current-row
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="子网名称" prop="name" />
        <el-table-column label="子网ID" prop="subnet_id" />
        <el-table-column label="项目" prop="project_name" />

        <el-table-column label="网络" >
          <template #default="scope">
            <div v-if="scope.row.network_id">
              {{ netObject[scope.row.network_id] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="IP版本" prop="ip_version" width="60"/>
        <el-table-column label="网关" prop="gateway_ip" />
        <el-table-column label="CIDR" prop="cidr" />
        <!-- <el-table-column label="地址池" prop="allocation_pools" /> -->
        <el-table-column label="DNS" prop="dns_nameservers" />



        <el-table-column label="开启DHCP" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_dhcp_enabled === true ? 'success' : 'info'">
              {{ scope.row.is_dhcp_enabled === true ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!--子网弹窗-->

    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="80%"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="computedRules"
        label-width="100px"
      >
        <el-card shadow="never">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入子网名称" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitClick">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import "/@/styles/index.scss";   // 基础样式
import { reactive, ref, computed, onMounted, defineOptions } from "vue";
import { ElForm, ElInput, ElDrawer, ElButton, ElTag, ElMessage, ElMessageBox } from "element-plus";
import  Pagination from  "/@/components/Pagination/index.vue";

defineOptions({
  name: "Subnet",
  inherititems: false,
});

import SubnetAPI, { SubnetPageQuery, SubnetPageVO, SubnetForm } from "./api";
import NetworkAPI from "../network/api";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<SubnetPageQuery>({
  page: 1,
  limit: 10,
});

const tableData = ref<SubnetPageVO[]>();

// 子网弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

const formData = reactive<SubnetForm>({});

const computedRules = computed(() => {
  const rules: Partial<Record<string, any>> = {
    name: [{ required: true, message: "请输入子网名称", trigger: "blur" }],
  };
  return rules;
});


const netOptions = ref<any[]>([]);
// 将网络数组netOptions 改造成一个map，使用compute在元数据变化时自动更新
const netObject = computed(() => {
  return netOptions.value.reduce((obj, item) => {
    obj[item.net_id] = item.name;
    return obj;
  }, {});
});


// 查询网络组列表
function getNetworkList() { 
  NetworkAPI.getList()
    .then((data: any) => {
      netOptions.value = data.results;
    });
}

// 查询
function handleQuery() {
  loading.value = true;
  SubnetAPI.getPage(queryParams)
    .then((data: any) => {
      tableData.value = data.data;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetClick() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

// 行选择函数
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 新增子网
function handleAddClick() {
  dialog.visible = true;
  dialog.title = "新增子网";
}

// /**
//  * 编辑子网
//  *
//  * @param id 子网ID
//  */
// function handleEditClick(id: number, name: string) {
//   dialog.visible = true;
//   dialog.title = "【" + name + "】子网修改";
//   SubnetAPI.getFormData(id).then((data) => {
//     Object.assign(formData, data);
//   });
// }

// 提交子网表单
function handleSubmitClick() {
  dataFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      loading.value = true;
      const id = formData.subnet_id;
      ElMessage.info("点击了提交按钮" + id)
      // if (id) {
      //   SubnetAPI.update(id, formData)
      //     .then(() => {
      //       ElMessage.success("修改成功");
      //       handleCloseDialog();
      //       handleQuery();
      //     })
      //     .finally(() => (loading.value = false));
      // } else {
      //   SubnetAPI.add(formData)
      //     .then(() => {
      //       ElMessage.success("新增成功");
      //       handleCloseDialog();
      //       handleQuery();
      //     })
      //     .finally(() => (loading.value = false));
      // }
    }
  });
}

/** 关闭子网弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.subnet_id = '';
}
/**
 * 删除子网
 *
 * @param id 子网ID
 */
function handleDelete(id?: number) {
  console.log("handleDelet" + id);
  // const attrGroupIds = [id || ids.value].join(",");
  // if (!attrGroupIds) {
  //   ElMessage.warning("请勾选删除项");
  //   return;
  // }
  // ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
  //   confirmButtonText: "确定",
  //   cancelButtonText: "取消",
  //   type: "warning",
  // }).then(
  //   () => {
  //     SubnetAPI.deleteByIds(attrGroupIds).then(() => {
  //       ElMessage.success("删除成功");
  //       handleResetClick();
  //     });
  //   },
  //   () => {
  //     ElMessage.info("已取消删除");
  //   }
  // );
}


onMounted(() => {
  handleQuery();
  getNetworkList();
});
</script>
