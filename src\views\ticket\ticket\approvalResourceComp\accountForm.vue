<template>
  <div>
    <div style="margin-bottom: 12px;">
      <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image :src="accountImg" alt="租户图标" style="width: 24px; height: 16px;"></tiny-image>当前租户:</span>
      <tiny-tag type="success" size="medium"> {{ currentSelectAccount }} </tiny-tag>
      </div>
    <tiny-grid ref="selectAccountGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @radio-change="handleRadioChange" row-id="id" :radio-config="radioConfigData">
      <tiny-grid-column type="radio" width="40"></tiny-grid-column>
      <tiny-grid-column field="account_name" title="ID/名称" align="left" width="180" :sortable="true" :filter="nameFilter">
        <template #default="{row}">
          <div class="id-cell">
              <!-- <span class="id-text">{{ row.ironicHyperisor_id.slice(0, 16) }}</span> -->
              <tiny-link :underline="false" type="info">{{ row.id.slice(0, 8) }}</tiny-link>
              <tiny-link :underline="false" type="info" :icon="TinyIconCopy" @click="copyText(row.id)"></tiny-link>
          </div>
          <p>{{ row.account_name }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="account_nick_name" title="账号别名" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="company" title="公司名称" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.company }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="account_type" title="账号类型" align="center" :sortable="true" :filter="accountTypeFilter">
        <template #default="{row}">
          {{ dictionary(tenantAccountType, row.account_type) || row.account_type }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectAccountForm">
import { ref, reactive, toRefs, watch } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyInput,
  TinyPager,
  TinyTag,
} from '@opentiny/vue';
import { GetList, DelObj } from '/@/api/tenant/account';
import { iconCopy } from '@opentiny/vue-icon';
import { copyText } from '/@/utils/copyText';
import accountImg from '/@/assets/img/portal-account.svg';
import { dictionary } from '/@/utils/dictionary';

const TinyIconCopy = iconCopy();

// 定义 props
const props = defineProps({
  currentSelectAccountId: {
    type: String,
    required: false,
    default: '',
  },
});

// selectedKey
const tenantAccountType = 'tenant:account:account_type'
// const props = defineProps({
//   projectId: {
//     type: String,
//     required: false,
//     default: ''
//   }
// });



// 当前选中值
let currentSelectAccount = ref<string>('--');
const emit = defineEmits(['update:currentSelectAccountId']);

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})

const accountTypeFilter = ref({
  multi: false,
  enumable: true,
  defaultFilter: false,
  inputFilter: false,
  values: dictionary(tenantAccountType, undefined)
})
const radioConfigData = ref({
  checkRowKey: props.currentSelectAccountId
})

// 初始化请求数据
interface FilterOptions {
  account_name: string;
  account_nick_name: string;
  description: string;
  company: string;
  account_type: string;
  id: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    account_name: '',
    account_nick_name: '',
    description: '',
    company: '',
    account_type: '',
    id: props.currentSelectAccountId,
  },
});
let tableData = ref([]);

const selectAccountGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectAccountGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    account_name: '',
    account_nick_name: '',
    description: '',
    company: '',
    account_type: '',
    id: props.currentSelectAccountId,
  };
  // reloadGrid();
}

const handleRadioChange = () => {
  let selectedRow = selectAccountGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectAccount.value = `${selectedRow.account_name}`
    emit('update:currentSelectAccountId', selectedRow.id)
  }
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.account_name && filters.filters.account_name.type === 'input') {
      filterOptions.value.account_name = filters.filters.account_name.value.text;
    }
  if (filters.filters.account_type && filters.filters.account_type.type === 'enum') {
    filterOptions.value.account_type = filters.filters.account_type.value.text;
    }
  reloadGrid();
}
</script>
<style lang="less" scoped>
.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>
