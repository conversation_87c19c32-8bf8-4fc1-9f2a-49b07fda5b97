import * as api from './api';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsRet,
  CreateCrudOptionsProps,
  dict,
} from '@fast-crud/fast-crud';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';
import { useRouter } from 'vue-router';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    if (!form.id){
      form.id = row.id;
    }
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('resource:idcRackMachine:Create'),
            plain: true,
            click() {
              router.push("/resource/idcRackMachine/add")
            }
          },
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 280,
        buttons: {
          view: {
            type: 'primary',
            link: true,
            show: auth("resource:idcRackMachine:Retrieve")
          },
          edit: {
            type: 'primary',
            link: true,
            show: auth('resource:idcRackMachine:Update'),
            // click(context) {
            //   router.push("/resource/idcRackMachine/add?id=" + context.row.id)
            // }
          },
          remove: {
            type: 'danger',
            link: true,
            show: auth('resource:idcRackMachine:Delete')
          },
          viewLog: {
            type: 'primary',
            text: '查看日志',
            link: true,
            show: auth('system:auditLog:GetResourceLogs'),
            click(context) {
              router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
            }
          },
        },
      },
      pagination: {
        show: true
      },
      table: {
        rowKey: 'id',
    },
      form: {
        labelWidth: 120,
        row: { gutter: 20},
        group: {
          type: "collspase",
          accordion: false,
          groups: {
            base: {
              slots: {
                // 自定义 header
                title: () => {
                  return (
                    <span style={{'align-items':'center','font-size': 'larger', 'font-weight': 'bolder'}}>&nbsp;基础信息</span>
                  );
                }
              },
              columns: ['machine_room', 'machine_room_alias', 'private_room', 'private_room_alias',
                        'rack_sn', 'name', 'description', 'rack_type', 'rack_physics_type',
                        'rack_status', 'billing_unit', 'rack_height', 'rack_u_height', 'rack_width', 'rack_deep',
                        'load_bearing', 'construction_status', 'construction_finished_time', 'accept_delivery_time', 'construction_density',
                        'to_shelve_density',
                      ]
            },
            network: {
              slots: {
                // 自定义 header
                title: () => {
                  return (
                    <span style={{'align-items':'center','font-size': 'larger', 'font-weight': 'bolder'}}>&nbsp;网络信息</span>
                  );
                }
              },
              columns: [
                'net_operator_business', 'network_type', 'network_cluster',
                'bmc_pod_sn', 'asw_pod_sn', 'network_arhitecture', 'security_domain', 'logical_zone',
              ]
            },
            power: {
              slots: {
                // 自定义 header
                title: () => {
                  return (
                    <span style={{'align-items':'center','font-size': 'larger', 'font-weight': 'bolder'}}>&nbsp;电源信息</span>
                  );
                }
              },
              columns: ['power_status', 'power_type', 'power_count', 'normal_power', 'power_max']
            },
          }
        }
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        // machine_room: {
        //   title: '机房',
        //   search: {
        //     show: true,
        //   },
        //   width: 130,
        //   type: 'table-select',
        //   dict: dict({
        //     value: "id",
        //     label: "name",
        //     getNodesByValues: async (values: any[]) => {
        //       const res = await machineRoomGetListByIds({'ids': values});
        //       return res.data
        //     }
        //   }),
        //   form: {
        //     component: {
        //       multiple: false,
        //       rowKey: "id", //element-plus 必传
        //       createCrudOptions: machineRoomOptions,
        //       crudOptionsOverride: {
        //         rowHandle: {
        //           fixed: "right"
        //         }
        //       }
        //     },
        //     rules: [
        //       // 表单校验规则
        //       {
        //         required: false,
        //         message: '必填项',
        //       },
        //     ],
        //   },
        // },
        machine_room: {
          title: '机房',
          search: {
            show: true,
          },
          column: {
            minWidth: 180,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/machine_room/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            // cache: true,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择机房',
            }
          },
        },
        machine_room_alias: {
          title: '机房缩写',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 100,
            show: true,
          },
          form: {
            show: true,
            component: {
              placeholder: '请输入机房缩写',
            },
            rules: [
              // 表单校验规则
              {
                required: true,
                message: '必填项',
              },
              {
                min: 1,
                max: 255,
                message: '最小 1, 最大 255',
                trigger: 'blur'
              },
            ],
          }
        },
        // private_room: {
        //   title: '包间',
        //   search: {
        //     show: true,
        //   },
        //   width: 130,
        //   type: 'table-select',
        //   dict: dict({
        //     value: "id",
        //     label: "name",
        //     getNodesByValues: async (values: any[]) => {
        //       const res = await privateRoomGetListByIds({'ids': values});
        //       return res.data
        //     }
        //   }),
        //   form: {
        //     component: {
        //       multiple: false,
        //       rowKey: "id", //element-plus 必传
        //       createCrudOptions: privateRoomOptions,
        //       crudOptionsOverride: {
        //         rowHandle: {
        //           fixed: "right"
        //         }
        //       }
        //     },
        //     rules: [
        //       // 表单校验规则
        //       {
        //         required: false,
        //         message: '必填项',
        //       },
        //     ],
        //   },
        //   column: {
        //     show: true,
        //   },
        // },
        private_room: {
          title: '包间',
          search: {
            show: true,
          },
          column: {
            minWidth: 180,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/private_room/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            // cache: true,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择包间',
            }
          },
        },
        private_room_alias: {
          title: '包间缩写',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 100,
            show: true,
          },
          form: {
            show: true,
            component: {
              placeholder: '请输入包间缩写',
            },
            rules: [
              // 表单校验规则
              {
                required: true,
                message: '必填项',
              },
              {
                min: 1,
                max: 255,
                message: '最小 1, 最大 255',
                trigger: 'blur'
              },
            ],
          }
        },
        rack_sn: {
          title: '机柜编号',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 180,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入机柜编号'},
              {min: 1, max: 255, message: '最小: 1, 最大: 255', trigger: 'blur'}
            ],
            component: {
              placeholder: '请输入机柜编号',
            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
            ],
            component: {
              placeholder: '请输入描述',
            }
          }
        },
        net_operator_business: {
          title: '网络运营商',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:net_operator_business', undefined)
          }),
          column: {
            minWidth: 100,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请选择网络运营商'},
            ],
            component: {
              placeholder: '请选择',
            }
          }
        },
        rack_type: {
          title: '机柜类型',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:rack_type', undefined)
          }),
          column: {
            minWidth: 120,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请选择机柜类型'},
            ],
            component: {
              placeholder: '请选择',
            }
          }
        },
        rack_physics_type: {
          title: '机柜物理形态',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:rack_physics_type', undefined)
          }),
          column: {
            minWidth:100,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请选择机柜物理形态'},
            ],
            component: {
              placeholder: '请选择',
            }
          }
        },
        network_type: {
          title: '网络类型',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:network_type', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请选择网络类型'},
            ],
            component: {
              placeholder: '请选择',
            }
          }
        },
        network_cluster: {
          title: '网络集群',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请选择网络集群'},
            ],
            component: {
              placeholder: '请选择',
            }
          }
        },
        bmc_pod_sn: {
          title: 'BMC_POD编号',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入BMC_POD编号'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        asw_pod_sn: {
          title: 'ASW_POD编号',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入ASW_POD编号'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        network_arhitecture: {
          title: '网络架构',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入网络架构'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        security_domain: {
          title: '安全域',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:security_domain', undefined)
          }),
          column: {
            minWidth: 120,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入安全域'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        logical_zone: {
          title: '逻辑区域',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:logical_zone', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入逻辑区域'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        construction_status: {
          title: '模建状态',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:construction_status', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请选择模建状态'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        accept_delivery_time: {
          title: '验收交付时间',
          search: {
            show: true,
          },
          type: 'datetime',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请选择验收交付时间'},
            ],
            component: {
              placeholder: '请输入',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
            }
          }
        },
        construction_finished_time: {
          title: '模建完成时间',
          search: {
            show: true,
          },
          type: 'datetime',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请选择模建完成时间'},
            ],
            component: {
              placeholder: '请输入',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
            }
          }
        },
        rack_status: {
          title: '柜位状态',
          search: {
            show: true,
          },
          type: 'dict-radio',
          dict: dict({
            data: dictionary('idc_rack_machine:rack_status', undefined),
            value: 'value',
            label: 'label',
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请选择柜位状态'},
            ],
            component: {
              placeholder: '请选择',
            }
          }
        },
        power_status: {
          title: '上电状态',
          search: {
            show: true,
          },
          type: 'dict-radio',
          dict: dict({
            data: dictionary('idc_rack_machine:power_status', undefined),
            value: 'value',
            label: 'label',
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入上电状态'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        billing_unit: {
          title: '计费单元',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入计费单元'},
            ],
            component: {
              placeholder: '请输入',
            }
          }
        },
        construction_density: {
          title: '建设密度',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入建设密度'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
            }
          }
        },
        to_shelve_density: {
          title: '上架密度',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '请输入上架密度'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
            }
          }
        },
        rack_height: {
          title: '高度(mm)',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入高度(mm)'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        rack_u_height: {
          title: '高度U',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入高度U/机柜U位'},
            ],
            helper: {
              position: "label",
              tooltip: {
                placement: "top-start"
              },
              // text: "同机柜U位"
              render() {
                return <span>同&nbsp;<span style={"color:red"}>机柜U位</span></span>;
              }
            },
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        rack_width: {
          title: '宽度(mm)',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入宽度(mm)'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        rack_deep: {
          title: '长度/深度(mm)',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入长度/深度(mm)'},
            ],
            helper: {
              position: "label",
              tooltip: {
                placement: "top-start"
              },
              // text: "此为共用字段，液冷机柜时可定义为机柜长度，风冷机时可定义为机柜深度"
              render() {
                return <span>此为共用字段，液冷机柜时可定义为<span style={"color:red"}>机柜长度</span>，风冷机时可定义为<span style={"color:red"}>机柜深度</span></span>;
              }
            },
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        load_bearing: {
          title: '承重(KG)',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入承重(KG)'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        power_type: {
          title: '电源类型',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('idc_rack_machine:power_type', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请选择电源类型'},
            ],
            component: {
              placeholder: '请选择',
            }
          }
        },
        power_count: {
          title: '电源路数',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入电源路数'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        normal_power: {
          title: '额定功率',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入额定功率'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        power_max: {
          title: '功率上限',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '请输入功率上限'},
            ],
            component: {
              placeholder: '请输入',
              step: 1,
              min: 1,
            }
          }
        },
        
      },
    },
  };
};
