<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
      <template #cell_$expand="scope">
        <el-descriptions
          class="margin-top"
          :border="true"
          align="center"
          size="default"
          style="width: 500px"
          :column="3"
        >
          <el-descriptions-item label="实例ID" label-class-name="my-label-class">{{scope.row.instance_id}}</el-descriptions-item>
          <el-descriptions-item label="主机名称" label-class-name="my-label-class">{{scope.row.name}}</el-descriptions-item>
          <el-descriptions-item label="主机类型" label-class-name="my-label-class">
            <el-tag type="primary">{{scope.row.host_type || "无"}}</el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="Buffer机" label-class-name="my-label-class">
            <el-switch
                v-model="scope.row.is_buffer"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                :disabled="true"
                :active-value="true"
                :inactive-value="false"
              />
          </el-descriptions-item>
          <el-descriptions-item label="IPMI监控" label-class-name="my-label-class">
              <el-switch
                v-model="scope.row.is_ipmi_mon"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                :disabled="true"
                :active-value="true"
                :inactive-value="false"
              />
          </el-descriptions-item>
          <el-descriptions-item label="系统监控" label-class-name="my-label-class">
            <el-switch
                v-model="scope.row.is_system_mon"
                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
                :disabled="true"
                :active-value="true"
                :inactive-value="false"
              />
          </el-descriptions-item>

          <el-descriptions-item label="主机状态" label-class-name="my-label-class">
            <el-tag type="danger">{{scope.row.host_status || "未知"}}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作系统" label-class-name="my-label-class">
            <el-tag type="danger">{{scope.row.os || "未知"}}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="内网IP" label-class-name="my-label-class">{{scope.row.ip_private}}</el-descriptions-item>
          <el-descriptions-item label="公网IP" label-class-name="my-label-class">{{scope.row.ip_public}}</el-descriptions-item>
          <el-descriptions-item label="宿主机IP" label-class-name="my-label-class">{{scope.row.kvm_ip_host}}</el-descriptions-item>
          <el-descriptions-item label="BMC-IP" label-class-name="my-label-class">{{scope.row.ip_bmc}}</el-descriptions-item>
          <el-descriptions-item label="BMC账号" label-class-name="my-label-class">{{scope.row.bmc_user}}</el-descriptions-item>
          
          <el-descriptions-item label="CPU(核)" label-class-name="my-label-class">{{scope.row.cpu}}</el-descriptions-item>
          <el-descriptions-item label="内存(G)" label-class-name="my-label-class">{{scope.row.mem}}</el-descriptions-item>
          <el-descriptions-item label="系统盘(G)" label-class-name="my-label-class">{{scope.row.sys_disk}}</el-descriptions-item>
          <el-descriptions-item label="数据盘(G)" label-class-name="my-label-class">{{scope.row.data_disk}}</el-descriptions-item>
          <el-descriptions-item label="GPU模型" label-class-name="my-label-class">
            <el-tag type="primary">{{scope.row.gpu_model || "无"}}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="GPU数量" label-class-name="my-label-class">{{scope.row.gpu_count}}</el-descriptions-item>
          <el-descriptions-item label="SSH用户" label-class-name="my-label-class">{{scope.row.ssh_user}}</el-descriptions-item>
          <el-descriptions-item label="SSH端口" label-class-name="my-label-class">{{scope.row.ssh_port}}</el-descriptions-item>
        </el-descriptions>
      </template>
      <template #actionbar-right>
        <importExcel api="api/operatorcmdb/host/" v-if="isShowImportBtn">导入</importExcel>
        <!-- <dropdownmenuRunner ref="runnerRef" :selected-ids="selectedIds" :selected-hosts="selectedHosts"></dropdownmenuRunner> -->
        <el-dropdown>
          <el-button type="primary">
            执行作业<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu :split-button="true">
              <el-dropdown-item @click="handleExecJob('ping')">测试连接性</el-dropdown-item>
              <el-dropdown-item @click="handleExecJob('check_network')">测试公网</el-dropdown-item>
              <el-dropdown-item @click="handleExecJob('ipmi_power_on')" :divided="true">开机</el-dropdown-item>
              <el-dropdown-item @click="handleExecJob('ipmi_power_shutdown')">关机</el-dropdown-item>
              <el-dropdown-item @click="handleExecJob('ipmi_power_off')">强制关机</el-dropdown-item>
              <el-dropdown-item @click="handleExecJob('ipmi_power_reset')">重启</el-dropdown-item>
              <el-dropdown-item @click="handleExecJob('install_node_exporter')" :divided="true">安装监控</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </fs-crud>
  <!-- 执行操作日志弹窗 -->
  <runnerLog v-model:visible="logDialogVisible" :executeId="execute_id"></runnerLog>
	</fs-page>
</template>

<script lang="ts" setup name="host">
import { onMounted, ref } from 'vue';
import { useExpose, useCrud } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { ElMessage } from 'element-plus';
import runnerLog from './component/runnerLog.vue';
import * as api from '/@/api/operatorCMDB/host';
import { auth } from "/@/utils/authFunction";
// import dropdownmenuRunner from './component/dropdownmenuRunner.vue'

// 导入按钮显示权限
const isShowImportBtn: boolean = auth("operatorcmdb:host:Import")

// const { crudBinding, crudRef, crudExpose, } = useFs({ createCrudOptions });

// crud组件的ref
const crudRef = ref();
// crud 配置的ref
const crudBinding = ref();
// 暴露的方法
const {crudExpose} = useExpose({crudRef, crudBinding});
// 你的crud配置
// @ts-ignore
const {crudOptions, selectedIds, selectedHosts} = createCrudOptions({ crudExpose });
// 初始化crud配置
// eslint-disable-next-line no-unused-vars
const {resetCrudOptions} = useCrud({crudExpose, crudOptions});


const execute_id = ref("")

const handleExecJob = (code: string) => {
  if (selectedIds.value.length <= 0) {
    ElMessage.error('未选中主机')
  } else {
    const data = {
        "job_code": code,
        "hosts": selectedHosts.value,
        "params": ""
    }
    api.RemoteExecJob(data).then(
      (response: any) => {
        if (response.code === 200 && response.msg === '成功') {
          ElMessage.success('发起作业成功')
          // TODO 刷新列表
          execute_id.value = response.data.execute_id
          console.log(execute_id.value)
          handleOpenLogDialog()
        } else {
          ElMessage.success('发起作业失败')
        }
      }
    )
  }
}


// 执行日志弹窗状态
const logDialogVisible = ref(false);

// 打开执行日志弹窗
function handleOpenLogDialog() { 
  logDialogVisible.value = true
}

// 获取子组件暴露的值，先为子组件绑定一个ref
// const runnerRef = ref()

// 通过方法获取子组件传来的值
// const getSonMethod = () => {
//   console.log(runnerRef.value.execute_id)
// }

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>

<style scoped>
:deep(.my-label-class) {
  text-align: center;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
  font-weight: bold;
  color: gray;
  opacity: 75%;
}

</style>