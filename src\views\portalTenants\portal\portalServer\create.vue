<template>
  <fs-page>
    <template #header>
      <div class="new-title">
        <tiny-steps vertical advanced size="large" :data="steps" :active="active" flex @click="stepsClick"></tiny-steps>
      </div>
    </template>
  <div id="container-list" class="container-list">

    <div class="contain">
      <div id="basical_config" class="create-step-container">
        <div class="container-step-head">
          <span>基础配置</span>
        </div>
        <tiny-form ref="createServerFormBasicalValid" :model="createServerFormData" :inline="false" label-position="left" label-width="150px" :label-align="true" style="border-radius: 0px" :rules="createServerFormRules">
          <tiny-form-item label="区域" prop="node">
            <tiny-button-group v-model="createServerFormData
            .node" style="border-radius: 0px; margin-right: 10px"
              :data="allNodes" :text-field="nodeValueField" :value-field="nodeValueField"></tiny-button-group>
            <!-- <span style="background-color: [object Event]; color: #8a8e99; font-size: 12px"
          >温馨提示：页面左上角切换区域</span> -->
            <span
              style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">不同区域的云服务产品之间内网互不相通；请就近选择靠近您业务的区域，可减少网络时延，提高访问速度。</span>
          </tiny-form-item>
          <tiny-form-item label="主机类型" prop="instance_type">
            <tiny-button-group v-model="createServerFormData.instance_type" style="border-radius: 0px; margin-right: 10px"
              :data="[{ text: '裸金属', value: '裸金属' },{ text: '云主机', value: '云主机' }]"></tiny-button-group>
            <!-- <span style="background-color: [object Event]; color: #8a8e99; font-size: 12px"
          >温馨提示：页面左上角切换区域</span> -->
            <!-- <span
              style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">不同区域的云服务产品之间内网互不相通；请就近选择靠近您业务的区域，可减少网络时延，提高访问速度。</span> -->
          </tiny-form-item>
          <tiny-form-item label="主机名称" prop="name">
            <tiny-input placeholder="请输入" v-model="createServerFormData.name" style="width: 40%; margin-right: 10px" :maxlength="63" show-word-limit></tiny-input>
            <!-- <span style="color: #575d6c; font-size: 12px; margin-right: 10px">GiB IOPS上限600，IOPS突发上限5,000</span> -->
          </tiny-form-item>
          <tiny-form-item label="主机描述" prop="description">
            <tiny-input placeholder="请输入" type="textarea" v-model="createServerFormData.description" style="width: 40%; margin-right: 10px" :maxlength="255" show-word-limit></tiny-input>
            <!-- <span style="color: #575d6c; font-size: 12px; margin-right: 10px">GiB IOPS上限600，IOPS突发上限5,000</span> -->
          </tiny-form-item>
          <tiny-form-item label="过期时间" prop="expire_at">
            <tiny-date-picker v-model="createServerFormData.expire_at" :picker-options="pickerOptions" type="datetime" placeholder="请选择过期日期"  style="width: 40%; margin-right: 10px" default-time="23:59:59" value-format="yyyy-MM-dd HH:mm:ss"></tiny-date-picker>
          </tiny-form-item>
        </tiny-form>
      </div>
      <tiny-divider></tiny-divider>
      <div id="project_config" class="create-step-container">
        <div class="container-step-head">
          <span>项目配置</span>
        </div>
        <tiny-form ref="createServerFormProjectIdValid" :inline="false" label-position="left" label-width="150px" style="border-radius: 0px" :model="createServerFormData" :rules="createServerFormRules">
          <tiny-form-item label="选择项目" prop="project_id">
            <SelectProjectForm v-model:current-select-project-id="createServerFormData.project_id" :node="createServerFormData.node"/>
          </tiny-form-item>
        </tiny-form>
      </div>
      <tiny-divider></tiny-divider>
      <div id="host_config" class="create-step-container">
        <div class="container-step-head">
          <span>主机配置</span>
        </div>
        <tiny-form ref="createServerFormHostValid" :model="createServerFormData" :inline="false" label-position="left" label-width="150px" style="border-radius: 0px" :rules="createServerFormRules">
          <tiny-form-item label="选择规格" prop="flavor_id">
            <SelectFlavorForm v-model:current-select-flavor-id="createServerFormData.flavor_id" :flavor_type="createServerFormData.instance_type" :node="createServerFormData.node" />
          </tiny-form-item>
          <tiny-form-item label="选择镜像" prop="image_id">
            <SelectImageForm v-model:current-select-image-id="createServerFormData.image_id" :image_type="createServerFormData.instance_type" :node="createServerFormData.node" />
          </tiny-form-item>
          <!-- <tiny-form-item label="选择裸机节点" prop="ironic_hypervisor_id" v-if="createServerFormData.instance_type === '裸金属'">
            <SelectIronicHypervisorForm v-model:currentSelectIronicHypervisorId="createServerFormData.ironic_hypervisor_id"></SelectIronicHypervisorForm>
          </tiny-form-item> -->
        </tiny-form>
      </div>
      <tiny-divider></tiny-divider>
      <div class="create-step-container">
        <span style="font-weight:bolder;">高级配置 &nbsp;</span>
        <tiny-tooltip content="指定选择网络或安全组，若选定的租户仅有一个网络时，自动选择无需手动操作" placement="top" effect="light">
          <tiny-icon-icon-unknow></tiny-icon-icon-unknow>
        </tiny-tooltip>
        <div style="display: inline-flex; padding-left: 16px; justify-items: center;">
          <tiny-switch v-model="isShowNetworkConfig" show-text>
            <template #open>
              <span>显示</span>
            </template>
            <template #close>
              <span>隐藏</span>
            </template>
          </tiny-switch>
        </div>
      </div>
     
      <div id="network_config" class="create-step-container" v-show="isShowNetworkConfig">
        <div class="container-step-head">
          <span>网络配置</span>
        </div>
        <tiny-form ref="createServerFormNetworkValid" :model="createServerFormData" :inline="false" label-position="left" label-width="150px" style="border-radius: 0px" :rules="createServerFormRules">
          <tiny-form-item label="选择网络" prop="network_id">
            <SelectNetworkForm  :project-id="createServerFormData.project_id" v-model:current-select-network-id="createServerFormData.network_id" :node="createServerFormData.node" />
          </tiny-form-item>
          <tiny-form-item label="选择安全组" prop="security_group_names">
            <SelectSecurityGroupForm :project-id="createServerFormData.project_id" v-model:current-select-security-group-id="createServerFormData.security_group_names" :node="createServerFormData.node" />
          </tiny-form-item>
        </tiny-form>
      </div>
      <tiny-divider></tiny-divider>
      <div>
        <tiny-row style="border-radius: 0px; height: 100%; margin-bottom:10px;">
          <tiny-col :span="8">
            <tiny-row style="border-radius: 0px">
              <tiny-col :span="5" style="display: flex">
                <span style="margin-right: 10px">购买量</span>
                <tiny-input type="number" placeholder="请输入" v-model="createServerFormData.count" style="width: 120px; margin-right: 10px" :step="1" :min="1" :max="10"></tiny-input>
                <span>台</span></tiny-col>
              <tiny-col :span="7">
                <!-- <div>
                  <span style="font-size: 12px">配置费用</span>
                  <span style="padding-left: 10px; padding-right: 10px; color: #de504e">¥1.5776</span>
                  <span style="font-size: 12px">/小时</span>
                </div>
                <div>
                  <span style="font-size: 12px; border-radius: 0px">参考价格，具体扣费请以账单为准。</span>
                  <span style="font-size: 12px; color: #344899">了解计费详情</span>
                </div> -->
              </tiny-col></tiny-row></tiny-col>
          <tiny-col :span="4" style="
            display: flex;
            flex-direction: row-reverse;
            border-radius: 0px;
            height: 100%;
            justify-content: flex-start;
            align-items: center;
          ">
            <tiny-button text="创建" type="danger" style="max-width: unset" @click="clickCreate" :reset-time="5000"></tiny-button></tiny-col></tiny-row>
      </div>
    </div>
  </div>
</fs-page>
</template>

<script setup name="portalServerCreate" lang="ts">
import { reactive, ref, watch, } from 'vue';
import {
  Divider as TinyDivider,
  Modal as TinyModal,
  DatePicker as TinyDatePicker,
  TinySwitch,
} from '@opentiny/vue';
import { iconUnknow } from '@opentiny/vue-icon';

import SelectFlavorForm from '../portalServer/createComp/form/selectFlavorForm.vue';
import SelectProjectForm from '../portalServer/createComp/form/selectProjectForm.vue';
import SelectImageForm from '../portalServer/createComp/form/selectImageForm.vue';
import SelectNetworkForm from '../portalServer/createComp/form/selectNetworkForm.vue';
import SelectSecurityGroupForm from '../portalServer/createComp/form/selectSecurityGroupForm.vue';
// import SelectIronicHypervisorForm from '../portalServer/createComp/form/selectIronicHypervisorForm.vue';
import {createBaremetalServerObj} from '/@/api/tenant/opServer';
import { useRouter } from 'vue-router';
import { pickerOptions } from '/@/utils/pickDatetimeOptions';
import { dictionary } from '/@/utils/dictionary';


const TinyIconIconUnknow = iconUnknow();

const active = ref(0);

// 路由
const router = useRouter();
const nodeValueField = ref('value');
const allNodes = dictionary('operator_cmdb:host:area_node', undefined);
let createServerFormData = reactive({
  'project_id': '',
  'network_id': '',
  'security_group_names': '',
  'flavor_id': '',
  'image_id': '',
  'ironic_hypervisor_id': '',
  'count': 1,
  'name': '',
  'description': '',
  'instance_type': '裸金属',
  'node': '',
  'expire_at': ''
});

// 点击创建按钮
const clickToServerList = () => {
  router.push(`/tenant/producter_and_services/elasti-computes/baremetal`)
}

// 项目数据选择验证
const createServerFormProjectIdValid = ref<any>({ validate: () => Promise.resolve(true) });
// 基础数据选择验证
const createServerFormBasicalValid = ref<any>({ validate: () => Promise.resolve(true) });
// 裸金属数据选择验证
const createServerFormHostValid = ref<any>({ validate: () => Promise.resolve(true) });
// 网络数据选择验证
const createServerFormNetworkValid = ref<any>({ validate: () => Promise.resolve(true) });

const createServerFormRules = reactive({
  project_id: [
    {required: true, message:'请选择项目', trigger: 'blur'}
  ],
  network_id: [
    {required: true, message:'请选择网络', trigger: 'blur'}
  ],
  security_group_names: [
    {required: false, message:'请选择安全组', trigger: 'blur'}
  ],
  flavor_id: [
    {required: true, message:'请选择规格', trigger: 'blur'}
  ],
  image_id: [
    {required: true, message:'请选择镜像', trigger: 'blur'}
  ],
  name: [
    {required: true, message:'请输入主机名称', trigger: 'blur'},
    {min:3, max: 63, message:'最小3个字符，最大63个字符', trigger: 'blur'},
  ],
  count: [
    {required: true, message:'请选择镜像', trigger: 'blur'},
    {type: 'number', min:1, max: 10, message:'最小为 1，最大为 10', trigger: 'blur'},
  ],
  description: [
    {max: 255, message: '最大255个字符', trigger: 'blur'}
  ],
  node: [
    {required: true, message:'请选择区域', trigger: 'blur'}
  ],
  instance_type: [
    {required: true, message:'请选择主机类型', trigger: 'blur'}
  ],
  expire_at: [
    {required: false, message:'请选择过期时间', trigger: 'blur'}
  ],
});

const steps = ref([
  {
    name: '项目配置',
    key: 'project_config',
    status: 'doing'
  },
  { 
    name: '基础配置',
    key: 'basical_config',
    status: 'doing'
  },
  {
    name: '主机配置',
    key: 'host_config',
    status: 'doing'
  },
  {
    name: '网络配置',
    key: 'network_config',
    status: 'doing'
  },
]);

const scrollToSection = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

// eslint-disable-next-line no-unused-vars
const stepsClick = (index:number, node: any) => {
  scrollToSection(steps.value[index]?.key || 'project_config')
  
}

// 是否展示高级配置
const isShowNetworkConfig = ref(true);
watch(() => createServerFormData.network_id, (newNetworkId, oldNetworkId) => {
  if (newNetworkId !== '' && newNetworkId !== oldNetworkId) {
    isShowNetworkConfig.value = false;
  } else {
    isShowNetworkConfig.value = true;
  }
}, {
    immediate: true, // 捕获初始值 
    deep: true,      // 处理嵌套对象 
    flush: 'post'    // 兼容异步场景
  });

const clickCreate = async () => {
  const validations = [
    { form: createServerFormProjectIdValid, section: 'project_config' },
    { form: createServerFormBasicalValid, section: 'basical_config' },
    { form: createServerFormHostValid, section: 'host_config' },
    { form: createServerFormNetworkValid, section: 'network_config' }
  ];

  for (const { form, section } of validations) {
    try {
      const valid = await form.value.validate();
      if (!valid) {
        scrollToSection(section);
        return; // 停止后续验证
      }
    } catch (error) {
      // 处理验证失败的情况
      scrollToSection(section);
      return; // 停止后续验证
    }
  }
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    const response = await createBaremetalServerObj(createServerFormData);
    TinyModal.message({
        message: response.msg,
        status: 'success',
      });
      clickToServerList();
  } catch (error) {
    TinyModal.message({
        message: '创建失败',
        status: 'error',
      });
  }
  
};
</script>
<style scoped lang="less">
.new-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
  padding: 8px;
}
.fixed-header {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-server-steps {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-step-container {
  margin: 20px;
}

.container-step-head {
  font-size: 24px;
  font-weight: bolder;
  margin-bottom: 24px;
}

.content {
  margin-top: 150px;
  /* 确保内容不会被步骤条覆盖 */
  overflow-y: auto;
  /* 允许内容区域垂直滚动 */
}

.section {
  height: 100vh;
  /* 每个部分的高度为视口高度 */
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}
.container-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 20px);
  overflow-x: hidden;
  overflow-y: auto;
}

.tiny-steps, .tiny-timeline {
  --tv-Steps-node-bg-color-active: #1476ff; /* 修改为您想要的颜色，例如红色 */
  --tv-Steps-node-bg-color-primary: #1476ff;
}

/* Tiny Vue 组件搜索栏修改圆角 */
:deep(.tiny-input__inner) {
  border-radius: 6px; /* 设置圆角大小 */
}
:deep(.tiny-button) {
  border-radius: 16px; /* 设置圆角大小 */
}

.line {
  height: 1px;
  color: rgb(213, 213, 213);
  background-color: rgb(213, 213, 213);
  border: 0;
}

.contain {
  flex: 1 1 auto;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);

  .contain-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 10px 0 10px;

    hr {
      .line();

      width: 86%;
      margin: 0 20px;
    }

    span {
      color: #1a1818;
      font-size: 16px;
    }
  }

  .contain-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 52px;
  }

  .contain-img {
    position: relative;
    display: flex;
    cursor: pointer;

    img:hover {
      background: whitesmoke;
    }
  }

  .contain-text {
    padding-left: 10px;
    color: #4e5969;
    font-size: 14px;
    cursor: pointer;
  }
}

.bottom-line {
  padding-bottom: 8px;

  hr {
    .line();

    width: 96%;
    margin: 0 auto;
  }
}

.filter-form {
  margin: 10px 0;
}

.col {
  width: 96%;
}

:deep(.tiny-grid) {
  &-header__column,
  &-body__column {
    &.col__selection,
    &.col__radio {
      padding: 0 8px 0 8px;
      & + th,
      + td {
        padding-left: 0;
      }
    }
  }
}

:deep(.tiny-pager) {
  float: right;
}

:deep(.tiny-grid-button__wrapper) {
  width: 100%;
}

:deep(.tiny-form-item__label) {
  color: #494747;
  font-weight: normal;
}

:deep(.tiny-grid-header__column) {
  height: 35px;
  color: rgb(139, 137, 137);
  background-color: #f5f6f7;
}

.operation {
  color: #5e7ce0;
}

.btn {
  display: flex;
  width: 100%;

  .screen {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    text-align: center;
    cursor: pointer;

    span {
      margin-left: 10px;
      color: #4e5969;
      font-size: 14px;
    }
  }
}

.btn > div:last-child {
    margin-left: auto;
}

.tiny-fullscreen {
  background: #fff;
}

.tiny-fullscreen-wrapper {
  min-height: 710px;
  padding: 0 30px;
}

.tiny-fullscreen-scroll {
  overflow-y: auto;
}

.search-btn {
  display: flex;
  button {
    height: 34px;
  }
}

.id-cell {
  display: flex;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}

.status-success {
  fill: #52c41a;
  margin-right: 8px;
}
.status-danger {
  fill: #C7000B;
  margin-right: 8px;
}

.status-warning {
  fill: #FA9841;
  margin-right: 8px;
}
</style>