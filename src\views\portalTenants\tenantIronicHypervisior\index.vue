<template>
  <fs-page>
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <template #cell_createDate="scope">
        创建时间：{{ dateFormat(scope.row.createDate) }}<br />
        修改时间：{{ dateFormat(scope.row.updateDate) }}
      </template>
      <!-- <template #actionbar-right>
        <importExcel api="api/tenants/tenant-op-ironic-hypervisor/">导入</importExcel>
      </template> -->
    </fs-crud>
  </fs-page>	
</template>

<script lang="ts" setup name="tenantOpenstackIronicHypervisior">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import importExcel from '/@/components/importExcel/index.vue';
import { auth } from "/@/utils/authFunction";

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

// 导入按钮显示权限
const isShowImportBtn: boolean = auth("tenant:adminIronicHypervisior:Import");

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>
