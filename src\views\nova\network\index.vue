<!-- 网络 -->
<template>
  <div class="app-container">
    
      <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="queryParams.search"
            placeholder="输入网络ID、名称进行搜索"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetClick()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>


    <el-card shadow="never">
      <div class="mb-[10px]">
        <el-button type="success" @click="handleAddClick()">
          <i-ep-plus />
          新增
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        highlight-current-row
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="名称" prop="name" />
        <el-table-column label="项目" prop="project_name" />
        <el-table-column label="ID" prop="net_id" />
        <el-table-column label="网络类型" prop="provider_network_type" />
        <el-table-column label="子网">
          <template #default="scope">
            <span v-for="subnetId in scope.row.subnet_ids" :key="subnetId">
              {{ subnetObject[subnetId] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="可用区" prop="availability_zones" />
        <el-table-column label="状态">
          <template #default="scope">
            <el-tag effect="plain" v-if="scope.row.status === 'ACTIVE'" type="success">运行中</el-tag>
            <el-tag effect="plain" v-else type='info'> {{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="MTU" prop="mtu" />
        <el-table-column label="管理状态" prop="is_admin_state_up"  width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_admin_state_up === true ? 'success' : 'danger'">
              {{ scope.row.is_admin_state_up === true ? "UP" : "DOWN" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="外部路由" prop="is_router_external"  width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_router_external === true ? 'success' : 'info'">
              {{ scope.row.is_router_external === true ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="共享的" prop="is_shared"  width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_shared === true ? 'success' : 'info'">
              {{ scope.row.is_shared === true ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>

      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!--网络弹窗-->

    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="80%"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="computedRules"
        label-width="100px"
      >
        <el-card shadow="never">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入网络名称" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitClick">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import "/@/styles/index.scss";   // 基础样式
import { reactive, ref, computed, onMounted, defineOptions } from "vue";
import { ElForm, ElInput, ElDrawer, ElButton, ElTag, ElMessage, ElMessageBox } from "element-plus";
import  Pagination from  "/@/components/Pagination/index.vue";
import NetworkAPI, { NetworkPageQuery, NetworkPageVO, NetworkForm } from "./api";
import SubnetAPI from "../subnet/api";

defineOptions({
  name: "Network",
  inherititems: false,
});


const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<NetworkPageQuery>({
  page: 1,
  limit: 10,
});

const tableData = ref<NetworkPageVO[]>();

// 网络弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

const formData = reactive<NetworkForm>({});

const computedRules = computed(() => {
  const rules: Partial<Record<string, any>> = {
    name: [{ required: true, message: "请输入网络名称", trigger: "blur" }],
  };
  return rules;
});


const subnetOptions = ref<any[]>([]);
// 将subnetOptions数组改造成一个map，使用compute在元数据变化时自动更新
const subnetObject = computed(() => {
  return subnetOptions.value.reduce((obj, item) => {
    obj[item.subnet_id] = item.name;
    return obj;
  }, {});
});
// 查询子网列表  
function fetchSubnetList() { 
  SubnetAPI.getList()
    .then((data: any) => {
      subnetOptions.value = data.results;
    });
}

// 查询
function handleQuery() {
  loading.value = true;
  NetworkAPI.getPage(queryParams)
    .then((data: any) => {
      tableData.value = data.data;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetClick() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

// 行选择函数
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 新增网络
function handleAddClick() {
  dialog.visible = true;
  dialog.title = "新增网络";
}

// /**
//  * 编辑网络
//  *
//  * @param id 网络ID
//  */
// function handleEditClick(id: number, name: string) {
//   dialog.visible = true;
//   dialog.title = "【" + name + "】网络修改";
//   NetworkAPI.getFormData(id).then((data) => {
//     Object.assign(formData, data);
//   });
// }

// 提交网络表单
function handleSubmitClick() {
  ElMessage.info("submit");
//   dataFormRef.value.validate((isValid: boolean) => {
//     if (isValid) {
//       loading.value = true;
//       const id = formData.insid;
//       if (id) {
//         NetworkAPI.update(id, formData)
//           .then(() => {
//             ElMessage.success("修改成功");
//             handleCloseDialog();
//             handleQuery();
//           })
//           .finally(() => (loading.value = false));
//       } else {
//         NetworkAPI.add(formData)
//           .then(() => {
//             ElMessage.success("新增成功");
//             handleCloseDialog();
//             handleQuery();
//           })
//           .finally(() => (loading.value = false));
//       }
//     }
//   });
}

/** 关闭网络弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.net_id = '';
}
/**
 * 删除网络
 *
 * @param id 网络ID
 */
// function handleDelete(id?: number) {
//   const attrGroupIds = [id || ids.value].join(",");
//   if (!attrGroupIds) {
//     ElMessage.warning("请勾选删除项");
//     return;
//   }
//   ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
//     confirmButtonText: "确定",
//     cancelButtonText: "取消",
//     type: "warning",
//   }).then(
//     () => {
//       NetworkAPI.deleteByIds(attrGroupIds).then(() => {
//         ElMessage.success("删除成功");
//         handleResetClick();
//       });
//     },
//     () => {
//       ElMessage.info("已取消删除");
//     }
//   );
// }


onMounted(() => {
  handleQuery();
});

onMounted(() => {
  // 获取子网列表
  fetchSubnetList();
});


</script>
