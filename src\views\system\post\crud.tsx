import * as api from './api';
import {
  dict,
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet
} from '@fast-crud/fast-crud';
import {dictionary} from '/@/utils/dictionary';
import { auth } from "/@/utils/authFunction";

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('system:post:Create'),
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            iconRight: 'View',
            type: 'success',
            show: auth('system:post:Retrieve')
          },
          edit: {
            iconRight: 'Edit',
            type: 'primary',
            show: auth('system:post:Update')
          },
          remove: {
            iconRight: 'Delete',
            type: 'danger',
            show: auth('system:post:Delete')
          },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        code: {
          title: '岗位编码',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '岗位编码必填项' },
            ],
            component: {
              placeholder: '请输入岗位编码',
            }
          }
        },
        name: {
          title: '岗位名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '名称必填项' },
            ],
            component: {
              placeholder: '请输入名称',
            }
          }
        },
        description: {
          title: '岗位描述',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入岗位描述',
            }
          }
        },
        sort: {
          title: '排序',
          search: {
            show: true,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请选择排序',
            }
          }
        },
      }
    }
  };
};
