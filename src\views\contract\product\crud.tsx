import * as api from '../../../api/contract/product';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict
} from '@fast-crud/fast-crud';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  let selectedIds = ref([]);
  let selectProducts = ref([])
  const onSelectionChange = (changed: any) => {
    selectedIds.value = changed.map((item: any) => item.id);
    selectProducts.value = changed.map((item: any) => {
      return {
        "name": item.name,
        "attributes": item.attributes,
        "label": item.label,
        "unit": item.unit,
        "price": item.price,
        "description": item.description,
        "source": item.source
      }
    });
  };
  return {
    selectedIds,
    selectProducts,
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: true,
            plain: auth('contract:product:Create'),
          }
        }
      }, 
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            link: true,
            type: 'primary',
            show: auth('contract:product:Retrieve'),
          },
          edit: {
            link: true,
            type: 'primary',
            show: auth('contract:product:Update'),
          },
          remove: {
            link: true,
            type: 'danger',
            show: auth('contract:product:Delete'),
          },
          viewLog: {
            type: 'primary',
            text: '查看日志',
            link: true,
            show: auth('system:auditLog:GetResourceLogs'),
            click(context) {
              router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
            }
          },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
        onSelectionChange,
      },
      columns: {
        $checked: {
          title: "选择",
          form: { show: false },
          column: {
            type: "selection",
            align: "left",
            width: "40px",
            columnSetDisabled: true, //禁止在列设置中选择
          }
        },
        name: {
          title: '产品名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
            fixed: 'left',
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '名称必填项' },
            ],
            component: {
              placeholder: '请输入名称',
            }
          }
        },
        attributes: {
          title: '产品规格',
          search: {
            show: true,
          },
          type: 'textarea',
          column: {
            minWidth: 150,
          },
          form: {
            component: {
              placeholder: '请输入规格',
            }
          }
        },
        type: {
          title: '产品类型',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('oamanage:product:type', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '产品类型必填项' },
            ],
            component: {
              placeholder: '请选择产品类型',
            }
          }
        },
        label: {
          title: '产品标签',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('oamanage:product:label', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '标签必选项' },
            ],
            component: {
              placeholder: '请选择产品标签',
            }
          }
        },
        source: {
          title: '产品来源',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('oamanage:product:source', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            value: '自行开发',
          },
        },
        unit: {
          title: '产品单位',
          type: 'dict-select',
          search: {
            show: true,
          },
          dict: dict({
            data: dictionary('oamanage:product:unit', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            value: '元/月',
          },
        },
        price: {
          title: '产品价格',
          type: 'number',
          search: {
            show: true,
          },
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '价格必填项' },
            ],
            component: {
              placeholder: '请输入价格',
              controls: true, // 是否显示加减按钮（可选）
            }
          }
        },
        description: {
          title: '产品描述',
          type: 'textarea',
          search: {
            show: true,
          },
          column: {
            minWidth: 150,
          },
          form: {
            component: {
              placeholder: '请输入描述',
            }
          }
        },
      },
    },
  };
};
