import { request,downloadFile } from '/@/utils/service';
import { UserPageQuery, AddReq,InfoReq,EditReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/contract/contract/';

export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}
export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + "detail_info" + '/',
    method: 'get',
    params: {"id":id},
	});
}

export function SaveObj(obj: AddReq) {
	return request({
		url: apiPrefix + "save" + "/",
		method: 'post',
		data: obj,
	});
}

export function UpdateObj(obj: EditReq) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export function exportData(params:any){
  return downloadFile({
      url: apiPrefix + 'export_all_contracts/',
      params: params,
      method: 'get',
  })
}
