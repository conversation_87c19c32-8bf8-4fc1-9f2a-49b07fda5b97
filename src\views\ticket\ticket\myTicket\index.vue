<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
      </template>
    </fs-crud>
	</fs-page>
</template>

<script lang="ts" setup name="myTicket">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
// import { auth } from "/@/utils/authFunction";
// import importExcel from '/@/components/importExcel/index.vue'

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>
