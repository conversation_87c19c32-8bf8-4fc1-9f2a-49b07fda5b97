import * as api from '/@/api/tenant/hisecEnginePublicIp';
import { dict, UserPageQuery, AddReq, DelReq, EditReq, useCompute, CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { dictionary } from '/@/utils/dictionary';
import { auth } from '/@/utils/authFunction';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, h, computed } from 'vue';

const { compute } = useCompute();
// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};

	let selectedIds = ref([]);

	const onSelectionChange = (changed: any) => {
		selectedIds.value = changed.map((item: any) => item.id);
	};
	const delButtonShowComputed = computed(() => {
		const isShow = auth('tenant:adminTenantPublicIP:MultipleDelete') && selectedIds.value.length > 0;
		return isShow;
	});
	return {
		selectedIds,
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			form: {
				labelWidth: '120px', //标签宽度
			},
			actionbar: {
				buttons: {
					add: {
						show: auth('tenant:adminTenantPublicIP:Create'),
						plain: true,
					},
					selectionsDeleted: {
						text: '删除',
						type: 'danger',
						plain: true,
						show: delButtonShowComputed,
						click: (): void => {
							if (selectedIds.value.length === 0) {
								ElMessage.warning('请先勾选');
								return;
							}
							ElMessageBox.confirm(
								h('p', null, [
									h('span', null, '确定删除 '),
									h('i', { style: 'color: red' }, selectedIds.value.length),
									h('span', null, ' 个记录吗？'),
								]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.DelObjs({ keys: selectedIds.value }).then((response: any) => {
										if (response.code === 2000 && response.msg === '删除成功') {
											ElMessage.success('删除成功');
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error('删除失败');
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消删除',
									});
								});
						},
					},
				},
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 200,
				buttons: {
					view: {
						link: true,
						type: 'primary',
						show: auth('tenant:adminTenantPublicIP:Retrieve'),
					},
					edit: {
						link: true,
						type: 'primary',
						show: auth('tenant:adminTenantPublicIP:Update'),
					},
					remove: {
						link: true,
						type: 'danger',
						show: auth('tenant:adminTenantPublicIP:Delete'),
					},
				},
			},
			pagination: {
				show: true,
			},
			table: {
				rowKey: 'id',
				border: false,
				onSelectionChange,
			},
			columns: {
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
						show: false,
					},
				},
				$checked: {
					title: '选择',
					form: { show: false },
					column: {
						type: 'selection',
						align: 'left',
						width: '55px',
						// selectable(row, index) {
						//   return row.id !== 1; //设置第一行不允许选择
						// }
					},
				},
				public_ip: {
					title: '公网IP',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: true, message: 'vlan名称必填项' },
						],
						component: {
							placeholder: '请输入',
						},
					},
				},
				node: {
					title: '区域',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'dict-select',
					dict: dict({
						url: '/api/tenants/tenant-op-setting/get_op_all_nodes/',
						immediate: false,
					}),
					editForm: {
						show: false,
					},
					column: {
						minWidth: 90,
					},
					form: {
						show: true,
						rules: [
							// 表单校验规则
							{ required: true, message: 'openstack项目区域必填项' },
						],
						component: {
							placeholder: '请输入openstack项目区域',
						},
					},
				},
				project_id: {
					title: '项目',
					search: {
						show: true,
					},
					column: {
						minWidth: 60,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/tenants/tenant-project/get_list_by_ids/?is_all=true',
						value: 'project_id',
						label: 'name',
						// cache: true,
					}),
					form: {
						show: false,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							rules: [
								// 表单校验规则
								// {required: false, message: '机房必填项'},
							],
							placeholder: '请选择所属项目',
						},
					},
				},
				used: {
					title: '使用中',
					search: {
						show: true,
					},
					column: {
						minWidth: 60,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('button_whether_bool', undefined),
					}),
					form: {
						show: false,
						rules: [
							// 表单校验规则
							{ required: false, message: '使用中必填项' },
						],
						component: {
							placeholder: '请选择是否使用中',
						},
					},
				},
				description: {
					title: '描述',
					search: {
						show: true,
					},
					type: 'textarea',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							// {required: true, message: '描述非必填项'},
						],
						component: {
							placeholder: '请输入描述',
						},
					},
				},
			},
		},
	};
};
