<template>
  <tiny-card class="stat-cards">
    <h2 class="dashboard-sub-title">
      <tiny-icon-info class="desc-icon-info" />
      {{ currentConfig.name  }}
    </h2>
    <ul class="description-list">
      <li 
        v-for="(description, index) in currentData" 
        :key="index"
        class="dashboard-sub-title-desc"
      >
       {{ description }}
      </li>
    </ul>
  </tiny-card>
</template>
 
<script setup lang="ts">
import { IconInfo } from '@opentiny/vue-icon'
import { TinyCard } from '@opentiny/vue'
import { PropType, ref, watchEffect } from 'vue'
 
const TinyIconInfo = IconInfo()
 
interface CardConfig {
  name: string 
}
 
const props = defineProps({
  data: {
    type: Array as PropType<string[]>,
    required: true,
    default: () => []
  },
  config: {
    type: Object as PropType<CardConfig>,
    required: false,
    default: () => ({ name: '说明' })
  }
})
 
// 使用更高效的响应式更新方式 
const currentData = ref<string[]>(props.data) 
const currentConfig = ref<CardConfig>(props.config) 
 
// 合并watch监听，减少重复触发 
watchEffect(() => {
  currentData.value  = props.data  
  currentConfig.value  = props.config  
})
</script>
 
<style scoped>
.desc-icon-info {
  fill: #1890ff;
  margin-right: 8px;
  vertical-align: middle;
}
 
.dashboard-sub-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px;
  display: flex;
  align-items: center;
  color: var(--text-primary);
}
 
.description-list {
  margin: 0;
  padding-left: 24px;
}
 
.dashboard-sub-title-desc {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  color: var(--text-regular);
  list-style-type: none;
  position: relative;
}
 
.dashboard-sub-title-desc::before {
  content: "•";
  color: var(--color-primary);
  position: absolute;
  left: -16px;
}
 
.stat-cards {
  --card-padding: 16px;
  --card-margin: 16px;
  
  width: calc(100% - var(--card-margin) * 2);
  margin: var(--card-margin);
  padding: var(--card-padding) !important;
  background-color: #e6f4ff;;
  box-shadow: none;
  box-sizing: border-box;
}
</style>