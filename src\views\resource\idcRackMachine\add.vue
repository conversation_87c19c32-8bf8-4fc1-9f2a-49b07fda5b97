<template>
  <fs-page>
    <template #header>
      <div class="new-title">
        <el-button @click="goBack" :icon="ArrowLeft" class="go-back">返回</el-button>
        <!-- <div class="title">创建机柜</div> -->
         <span class="sub-title">机柜</span>
      </div>
      
    </template>
    <div class="custom-card-content">
      <fs-form ref="formRef" v-bind="formOptions" />
    </div>
    <template #footer>
      <div class="custom-card-footer">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-button type="primary" v-if="formRef" @click="formRef.submit">保存</el-button>
          </el-col>
          <el-col :span="4" :offset="14">
            <el-button :icon="ArrowLeft" class="go-back" @click="goBack">返回</el-button>
          </el-col>
        </el-row>
      </div>
    </template>
  </fs-page>
</template>


<script setup name="FormNewPageEdit">
import { useRouter, useRoute } from "vue-router";
import { ref, onMounted } from "vue";
import { useCrud, useExpose, } from "@fast-crud/fast-crud";
import { createCrudOptions } from "./crud";
import { ElMessage } from "element-plus";
import { ArrowLeft } from "@element-plus/icons-vue";
import * as api from "./api";


// crud组件的ref
const crudRef = ref();
// crud 配置的ref
const crudBinding = ref();
// 暴露的方法
const { expose } = useExpose({ crudRef, crudBinding });
// 你的crud配置
const { crudOptions } = createCrudOptions({ expose });
// 初始化crud配置
// eslint-disable-next-line no-unused-vars
const { resetCrudOptions } = useCrud({ expose, crudOptions });

const formRef = ref();
const formOptions = ref();

const router = useRouter();
const route = useRoute();
const id = route.query.id;
const goBack = () => {
  // 返回上一页
  router.go(-1);
}

if (id) {
  //编辑表单
  formOptions.value = crudBinding.value.editForm;

} else {
  formOptions.value = crudBinding.value.addForm;
}
const doSubmit = formOptions.value.doSubmit;

formOptions.value.doSubmit = (context) => {
  if (context.form?.id !== undefined) {
    context.mode = 'edit'
  }
  doSubmit(context);
  //提交成功后，关闭本页面
  ElMessage.success("保存成功");
  goBack()
  
};


const getDetail = async (id) => {
      return await api.GetObj(id);
    };


onMounted(async () => {
  if (id) {
    //远程获取记录详情
    const response = await getDetail(id);
    formRef.value.setFormData(response.data);
  }
});
</script>

<style>
.go-back {
  height: 100%;
  background-color: #ffffff;
  border-color: #ffffff;
}
.go-back:hover {
  background-color: #ffffff; /* hover状态下也保持背景色透明 */
  border-color: #ffffff; /* hover状态下也保持边框颜色透明 */
  color: #66b1ff; /* hover状态下可以更改文字颜色 */
}
.new-title {
  border: 8px solid #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: 0px 2px 2px 2px rgba(0, 0, 0, .04), 0px 2px 2px rgba(0, 0, 0, .08);
  background-color: #ffffff;
  width: 100%;
  height: 100%;
  display: flex;
}
.sub-title {
  padding-top: 2px;
  align-items: center;
  font-weight: bolder;
}

.custom-card-content {
  border: 8px solid #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: 0px 2px 2px 2px rgba(0, 0, 0, .04), 0px 2px 2px rgba(0, 0, 0, .08);
  background-color: #ffffff;
  padding: 10px;
}
.custom-card-footer {
  border: 8px solid #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, .04), 0px 2px 2px rgba(0, 0, 0, .08);
  background-color: #ffffff;
}
</style>