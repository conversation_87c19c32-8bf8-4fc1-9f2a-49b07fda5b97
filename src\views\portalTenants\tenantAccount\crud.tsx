import * as api from '../../../api/tenant/account';
import {
  dict,
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  // compute,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import { dictionary } from '/@/utils/dictionary';
import { auth } from "/@/utils/authFunction";
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };


  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('tenant:TenantAccount:Create'),
            plain: true,
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            link: true,
            type: 'primary',
            show: auth('tenant:TenantAccount:Retrieve'),
          },
          edit: {
            link: true,
            type: 'primary',
            show: auth('tenant:TenantAccount:Update'),
          },
          remove: {
            link: true,
            type: 'danger',
            show: auth('tenant:TenantAccount:Delete'),
          },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        account_name: {
          title: '账号名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '名称必填项' },
            ],
            component: {
              placeholder: '请输入名称',
            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'textarea',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '描述非必填项'},
            ],
            component: {
              placeholder: '请输入描述',
            }
          }
        },
        account_nick_name: {
          title: '账号别称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '账号别称必填项'},
            ],
            component: {
              placeholder: '请输入客户联系人',
            }
          }
        },
        company: {
          title: '公司名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '公司名称必填项' },
            ],
            component: {
              placeholder: '请输入公司名称',
            }
          }
        },
        tenant_id: {
          title: '租户ID',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          editForm: {
            show: false,
          },
          column: {
            minWidth: 90,
          },
          form: {
            show: false,
            rules: [
              // 表单校验规则
              { required: false, message: '租户ID必填项' },
            ],
            component: {
              placeholder: '请输入租户ID',
            }
          }
        },
        password: {
          title: '密码',
          type: 'password',
          column: {
              show: false,
          },
          editForm: {
              show: false,
          },
          form: {
              rules: [
                  // 表单校验规则
                  {
                      required: true,
                      message: '密码必填项',
                  },
              ],
              component: {

                  span: 12,
                  showPassword: true,
                  placeholder: '请输入密码',
              },
          },
      },
        is_master: {
          title: '主账号',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('button_whether_bool', undefined)
          }),
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '是否主账号必填项' },
            ],
            component: {
              placeholder: '请选择是否主账号',
            }
          }
        },
        account_type: {
          title: '账号类型',
          search: {
            show: true,
          },
          column: {
            minWidth: 100,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('tenant:account:account_type', undefined)
          }),
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '账号类型必填项' },
            ],
            component: {
              placeholder: '请输入账号类型',
            }
          }
        },
        mobile: {
          title: '联系人电话',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入联系人电话',
            }
          }
        },
        customer_id: {
          title: '客户',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/customer/customer/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            // cache: true,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择所属客户',
            }
          },
        },
        email: {
          title: '联系人邮箱',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              // {required: true, message: '公司描述必填项'},
            ],
            component: {
              placeholder: '请输入联系人邮箱',
            }
          },
          email: {
            title: '联系人邮箱',
            search: {
              show: true,
            },
            type: 'input',
            column: {
              minWidth: 90,
            },
            form: {
              rules: [
                // 表单校验规则
                // {required: true, message: '公司描述必填项'},
              ],
              component: {
                placeholder: '请输入联系人邮箱',
              }
            }
        },
      },
      },
    },
  };
};
