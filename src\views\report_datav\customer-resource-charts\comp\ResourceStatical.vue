<template>
  <tiny-card class="asset-overview-card">
    <header class="card-header">
      <h2 class="card-title">资产概况</h2>
    </header>

    <div class="stats-container">
      <!-- 使用CSS Grid布局替代TinyRow/TinyCol -->
      <div class="stats-grid">
        <div v-for="(item, index) in statsItems" :key="index" class="stat-item">
          <ChartStatistics :data="currentData[item.key]" :config="item.config" />
        </div>

        <!-- 添加空div保持最后一行对齐（当总数不是4的倍数时） -->
        <div v-for="i in placeholderCount" :key="'placeholder-' + i" class="stat-item placeholder"></div>
      </div>
    </div>
  </tiny-card>
</template>

<script setup lang="ts">
import { TinyCard } from '@opentiny/vue'
import ChartStatistics from '/@/components/chaosCharts/chartStatistics.vue'
import { PropType, ref, watchEffect, computed } from 'vue'

interface AssetStatistics {
  total: number
  baremetal_total: number
  virtual_host_total: number
  running: number
  coming_expire: number
  had_expired: number
  coming_created: number
  gpu_counts: number
}
interface StatItem {
  key: keyof AssetStatistics  // 确保 key 只能是 AssetStatistics 的属性名 
  config: {
    name: string
    unit: string 
    tooltip?: string 
  }
}

const props = defineProps({
  data: {
    type: Object as PropType<AssetStatistics>,
    required: true,
    default: () => ({
      total: 0,
      baremetal_total: 0,
      virtual_host_total: 0,
      running: 0,
      coming_expire: 0,
      had_expired: 0,
      coming_created: 0,
      gpu_counts: 0,
    })
  }
})

const currentData = ref<AssetStatistics>(props.data)

const statsItems = computed<StatItem[]>(() => [
  { key: 'total', config: { name: '主机', unit: '台', tooltip: '云主机 + 裸金属' } },
  { key: 'baremetal_total', config: { name: '裸金属', unit: '台' } },
  { key: 'virtual_host_total', config: { name: '云主机', unit: '台' } },
  { key: 'running', config: { name: '运行中', unit: '台' } },
  { key: 'gpu_counts', config: { name: 'GPU数量', unit: '张' } },
  { key: 'coming_expire', config: { name: '即将过期', unit: '台', tooltip: '未来15天内即将过期的裸金属' } },
  { key: 'had_expired', config: { name: '已过期', unit: '台', tooltip: '近15天内已过期的裸金属' } },
  { key: 'coming_created', config: { name: '近期创建', unit: '台', tooltip: '近7天创建的裸金属' } },
])

// 计算需要多少个占位元素（确保总数为4的倍数）
const placeholderCount = computed(() => {
  const remainder = statsItems.value.length % 4
  return remainder > 0 ? 4 - remainder : 0
})

watchEffect(() => {
  currentData.value = props.data
})
</script>

<style scoped>
.asset-overview-card {
  --card-padding: 20px;
  --card-margin: 16px;

  width: calc(100% - var(--card-margin) * 2);
  margin: var(--card-margin);
  padding: var(--card-padding);
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.card-header {
  margin-bottom: 24px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.stats-container {
  width: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.stat-item {
  min-width: 0;
  /* 防止内容溢出 */
}

.placeholder {
  visibility: hidden;
  /* 隐藏占位元素 */
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .asset-overview-card {
    --card-padding: 16px;
    --card-margin: 8px;
  }

  .card-header {
    margin-bottom: 16px;
  }
}
</style>