import { AddReq, DelReq, EditReq, dict, CrudExpose, UserPageQuery, CreateCrudOptionsRet } from '@fast-crud/fast-crud';

import * as api from './api';
import { auth } from "/@/utils/authFunction";

//此处为crudOptions配置
export default function ({ crudExpose }: { crudExpose: CrudExpose }): CreateCrudOptionsRet {
  const pageRequest = async (query: any) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    if (row.id) {
      form.id = row.id;
    }
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  const exportRequest = async (query: UserPageQuery) => {
    return await api.exportData(query)
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('TemplateModelViewSet:Create'),
          },
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            type: 'text',
            order: 1,
            show: auth('TemplateModelViewSet:Retrieve')
          },
          edit: {
            type: 'text',
            order: 2,
            show: auth('TemplateModelViewSet:Update')
          },
          copy: {
            type: 'text',
            order: 3,
            show: auth('TemplateModelViewSet:Copy')
          },
          remove: {
            type: 'text',
            order: 4,
            show: auth('TemplateModelViewSet:Delete')
          },
        },
      },
      columns: {
        name: {
          title: '模板名称',
          type: 'input',
          search: { show: true },
          column: {
            minWidth: 120,
          },
          form: {
            rules: [{ required: true, message: '模板名称必填' }],
            component: {
              placeholder: '请输入模板名称',
            },
          },
        },
        type: {
          title: '类型',
          type: 'dict-select',
          search: { show: true },
          column: { minWidth: 100 },
          dict: dict({
            value: "id",
            label: "text",
            data: [
              { id: "P", text: "Playbook", color: "success" },
              { id: "M", text: "Module", color: "primary" },
            ]
          }),
          form: {
            rules: [
              {
                required: true,
                message: '必填项',
              },],
            component: {
              placeholder: '请选择模板',
              props: {
                checkStrictly: true,
                props: {
                  value: 'id',
                  label: 'text',
                },
              },
            },
          },
        },
        folder: {
          title: '目录',
          type: 'input',
          search: { show: true },
          column: {
            minWidth: 120,
          },
          form: {
            rules: [{ required: true, message: '必填项' }],
            component: {
              placeholder: '请输入目录',
            },
          },
        },
        yml_path: {
          title: 'yaml路径',
          type: 'input',
          search: { show: true },
          column: {
            minWidth: 120,
          },
          form: {
            rules: [{ required: true, message: '模板名称必填' }],
            component: {
              placeholder: '请输入yaml文件路径',
            },
          },
        },
        desc: {
          title: '备注',
          type: ['textarea', 'colspan'],
          search: { show: false },
          column: {
            minWidth: 150,
          },
        },
        create_datetime: {
          title: '创建时间',
          type: 'datetime',
          search: { show: false },
          form: {
            show: false,
            component: {
              //输入输出值格式化
              valueFormat: "YYYY-MM-DD HH:mm:ss"
            }
          },
          column: {
            align: "center",
            minWidth: 200,
            component: { name: "fs-date-format", format: "YYYY-MM-DD HH:mm:ss" },
            editable: {
              //该列是否禁用编辑, boolean | TableColumnEditableDisabledFunc;
              //比table.editable.isEditable优先级更高
              disabled: true,
            }
          }
        },

      },
    },
  };
}