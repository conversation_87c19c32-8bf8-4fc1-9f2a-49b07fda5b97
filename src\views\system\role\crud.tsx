import {CrudOptions, AddReq, DelReq, EditReq, dict, CrudExpose, compute} from '@fast-crud/fast-crud';
import * as api from '/@/views/system/api/role';
import {dictionary} from '/@/utils/dictionary';

// eslint-disable-next-line no-unused-vars
import {columnPermission} from '../../../utils/columnPermission';
import {successMessage} from '../../../utils/message';
import {auth} from '/@/utils/authFunction'


interface CreateCrudOptionsTypes {
    output: any;
    crudOptions: CrudOptions;
}

//此处为crudOptions配置
export const createCrudOptions = function ({
                                               // eslint-disable-next-line no-unused-vars
                                               crudExpose,
                                               // eslint-disable-next-line no-unused-vars
                                               rolePermission,
                                               // eslint-disable-next-line no-unused-vars
                                               handlePerm,
                                               handleDrawerOpen,
                                               handleUsersInfo,
                                           }: {
    crudExpose: CrudExpose;
    rolePermission: any;
    handleDrawerOpen: Function;
    handlePerm: Function;
    handleUsersInfo: Function;
}): CreateCrudOptionsTypes {
    const pageRequest = async (query: any) => {
        // 仅查询默认的【菜单、按钮权限】类角色
        query['type'] = 0
        return await api.GetList(query);
    };
    const editRequest = async ({form, row}: EditReq) => {
        form.id = row.id;
        return await api.UpdateObj(form);
    };
    const delRequest = async ({row}: DelReq) => {
        return await api.DelObj(row.id);
    };
    const addRequest = async ({form}: AddReq) => {
        return await api.AddObj(form);
    };

    //权限判定

    // @ts-ignore
    return {
        crudOptions: {
          container: {
            is: 'CardCustomLayout', //可以将自定义布局组件全局注册，这里只需要配置name即可
          },
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            pagination: {
                show: true
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('role:Create'),
                        type: 'primary',
                        plain: true,
                    }
                }
            },
            rowHandle: {
                //固定右侧
                fixed: 'right',
                width: 300,
                buttons: {
                    view: {
                        show: true,
                        type: 'success',
                        link: true,
                    },
                    edit: {
                        show: auth('role:Update'),
                        type: 'primary',
                        link: true,
                    },
                    remove: {
                        show: auth('role:Delete'),
                        type: 'danger',
                        link: true,
                    },
                    permission: {
                        type: 'primary',
                        text: '权限配置',
                        show: auth('role:Permission'),
                        dropdown: true, //---------》给想要折叠的按钮配置dropdown为true，就会放入dropdown中《---------
                        click: (context: any): void => {
                            const {row} = context;
                            handleDrawerOpen(row);
                        },
                        plain: true,
                    },
                    // permRoleRow: {
                    //   type: '',
                    //   text: '行权限',
                    //   plain: true,
                    //   dropdown: true, //---------》给想要折叠的按钮配置dropdown为true，就会放入dropdown中《---------
                    //   show: auth('role:rowPermission'),
                    //   tooltip: {
                    //       placement: 'top',
                    //       content: '配置行权限',
                    //   },
                    //   click: (context: any): void => {
                    //       const {row} = context;
                    //       handlePerm(row);
                    //   },
                    // },
                    roleUsers: {
                      type: 'primary',
                      text: '用户',
                      plain: true,
                      dropdown: true, //---------》给想要折叠的按钮配置dropdown为true，就会放入dropdown中《---------
                      show: auth('role:Permission'),
                      click: (context: any): void => {
                        const {row} = context;
                        handleUsersInfo(row);
                      },
                    }
                  },
                  dropdown: {
                    // 操作列折叠，dropdown参数配置
                    // 至少几个以上的按钮才会被折叠
                    // atLeast: 2, //TODO 注意 [atLeast]参数即将废弃，请给button配置dropdown即可放入折叠
                    more: {
                      //更多按钮配置
                      text: "操作",
                      link: true,
                      // @ts-ignore
                      icon: null
                    },
                  },
            },
            form: {
                col: {span: 24},
                labelWidth: '100px',
                wrapper: {
                    is: 'el-dialog',
                    width: '600px',
                },
            },
            columns: {
                _index: {
                    title: '序号',
                    form: {show: false},
                    column: {
                        type: 'index',
                        show: false,
                        align: 'center',
                        width: '70px',
                        columnSetDisabled: true, //禁止在列设置中选择
                    },
                },
                id: {
                    title: 'ID',
                    type: 'text',
                    column: {show: false},
                    search: {show: false},
                    form: {show: false},
                },
                name: {
                    title: '角色名称',
                    type: 'text',
                    search: {show: true},
                    column: {
                        minWidth: 120,
                        sortable: 'custom',
                    },
                    form: {
                        rules: [{required: true, message: '角色名称必填'}],
                        component: {
                            placeholder: '请输入角色名称',
                        },
                    },
                },
                key: {
                    title: '权限标识',
                    type: 'text',
                    search: {show: false},
                    column: {
                        minWidth: 120,
                        sortable: 'custom',
                        columnSetDisabled: true,
                    },
                    form: {
                        rules: [{required: true, message: '权限标识必填'}],
                        component: {
                            placeholder: '输入权限标识',
                        },
                    },
                    valueBuilder(context) {
                        const {row, key} = context
                        return row[key]
                    }
                },
                // sort: {
                //     title: '排序',
                //     search: {show: false},
                //     type: 'number',
                //     column: {
                //         minWidth: 90,
                //         sortable: 'custom',
                //     },
                //     form: {
                //         rules: [{required: true, message: '排序必填'}],
                //         value: 1,
                //     },
                // },
                status: {
                    title: '状态',
                    search: {show: true},
                    type: 'dict-radio',
                    column: {
                        width: 100,
                        component: {
                            name: 'fs-dict-switch',
                            activeText: '',
                            inactiveText: '',
                            style: '--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6',
                            onChange: compute((context) => {
                                return () => {
                                    api.UpdateObj(context.row).then((res: APIResponseData) => {
                                        successMessage(res.msg as string);
                                    });
                                };
                            }),
                        },
                    },
                    dict: dict({
                        data: dictionary('button_status_bool', undefined),
                    }),
                    label: 'label',
                    value: 'value',
                }
            },
        },
    };
};
