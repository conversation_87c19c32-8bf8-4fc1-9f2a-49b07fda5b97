<template>
	<div>
		<el-descriptions size="large" :column="2" border>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="预回款金额(元)" required class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item label prop="amount" required :rules="options.rules.amount">
					<el-input-number
						style="width:100%"
						v-model="props.item.amount"
						placeholder="请输入预回款金额"
						:controls="false"
						:precision="2"
						:disabled="isDetail"
					/>
				</el-form-item>
			</el-descriptions-item>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="收款主体" required class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item label prop="payee" required :rules="options.rules.payee">
					<el-select v-model="props.item.payee" filterable placeholder="请选择收款主体" :disabled="isDetail">
						<el-option v-for="s in secondPartyOptions" :key="s.value" :label="s.label" :value="s.value" />
					</el-select>
				</el-form-item>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="付款主体" required class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-radio-group v-model="props.item.invoice_type" class="ml-4" :disabled="isDetail">
					<el-radio value="企业">企业</el-radio>
					<el-radio value="个人">个人</el-radio>
				</el-radio-group>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="付款方名称" class="form-label-in-descriptions" label-width="130" />
				</template>
				<!-- 可编辑时用选择器 -->
				<el-select
					v-model="props.item.invoice_title"
					filterable
					placeholder="请选择付款方"
					@visible-change="customerVisibleChanged"
					:disabled="true"
				>
					<el-option v-for="s in customerList" :key="s.id" :label="s.name" :value="s.name" />
				</el-select>
				<!-- <el-input v-model="props.item.invoice_title" placeholder="请输入付款方名称" /> -->
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="电话号码" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.invoice_phone" placeholder="请输入电话号码" :disabled="true" />
			</el-descriptions-item>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="地址" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.invoice_address" placeholder="请输入付款方地址" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item v-if="props.item.cash_pledge">
				<template #label>
					<el-form-item label="押金" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.cash_pledge" placeholder="请输入押金" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item v-if="props.item.cash_pledge">
				<template #label>
					<el-form-item label="押金回款日期" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-date-picker
					v-model="props.item.cashple_pay_time"
					type="date"
					placeholder="请选择预押金回款日期"
					value-format="YYYY-MM-DD"
					style="min-width:250px"
					:disabled="isDetail"
				/>
				<!-- <el-tooltip placement="top" style="margin-left:10px">
					<template #content>
						合同分为新签与续签,父级合同理解为续签合同所绑定的上一级合同。
						<br />若不选择父级合同,则创建的合同默认为新签合同。
						<br />若选择了父级合同,则创建的合同为续签合同。
					</template>
					<el-icon size="20">
						<QuestionFilled />
					</el-icon>
				</el-tooltip>-->
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="纳税人识别号" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input
					style="min-width:250px"
					v-model="props.item.invoice_tax"
					placeholder="请输入纳税人识别号"
					:disabled="isDetail"
				/>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="预回款日期" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-date-picker
					v-model="props.item.payment_time"
					type="date"
					placeholder="请选择预回款日期"
					value-format="YYYY-MM-DD"
					style="min-width:250px"
					:disabled="isDetail"
				/>
			</el-descriptions-item>
		</el-descriptions>

		<el-descriptions v-if="props.item.invoice_type !== '个人'" size="large" :column="3" border>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="开户行" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.invoice_bank" placeholder="请输入开户行" :disabled="isDetail" />
			</el-descriptions-item>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="银行账号" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.invoice_account" placeholder="请输入银行账号" :disabled="isDetail" />
			</el-descriptions-item>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="银行营业网点" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.invoice_banking" placeholder="请输入银行营业网点" :disabled="isDetail" />
			</el-descriptions-item>
		</el-descriptions>

		<el-descriptions size="large" :column="1" border>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="合同流程编号" required class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item label required :rules="options.rules.contract_code" prop="contract_code">
					<el-select
						v-model="props.item.contract_code"
						@visible-change="visibleChanged"
						@change="contractChanged"
						filterable
						placeholder="请选择关联的合同"
						clearable
						:disabled="!isCreate"
					>
						<el-option
							v-for="s in contractList"
							:key="s.code"
							:label="s.code + '&nbsp;&nbsp;&nbsp;' + s.name + '&nbsp;&nbsp;&nbsp;' + s.customer_name"
							:value="s.code"
						/>
					</el-select>
				</el-form-item>
			</el-descriptions-item>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="备注信息" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input
					type="textarea"
					v-model="props.item.description"
					placeholder="填写描述"
					:disabled="isDetail"
				></el-input>
			</el-descriptions-item>
		</el-descriptions>

		<!-- 代理信息 -->
		<el-descriptions size="large" :column="3" border>
			<template #title>
				<div style="font-size: 18px;margin:10px 0 0 15px">
					<span>代理信息</span>
				</div>
			</template>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="居间方" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.agent_info.customer" :disabled="isDetail" />
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="居间费" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.agent_info.cost" :disabled="isDetail" />
			</el-descriptions-item>
		</el-descriptions>

		<!-- 回款信息 -->
		<el-descriptions size="large" :column="3" border>
			<template #title>
				<div style="font-size: 18px;margin:10px 0 0 15px">
					<span>回款信息</span>
					<!-- <el-button
						type="success"
						plain
						size="large"
						style="margin-left:10px"
						:disabled="isDetail"
						@click="handleSurplusPay"
					>剩余部分全部回款</el-button>-->
				</div>
			</template>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="回款状态" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.status" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item v-if="!isPayErr">
				<template #label>
					<el-form-item label="未回款金额(元)" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.surplus_payment_amount" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item label-class-name="my-label" v-else>
				<template #label>
					<el-form-item label="异常金额(元)" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.surplus_payment_amount" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="已回款金额(元)" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.pre_payment_amount" :disabled="true" />
			</el-descriptions-item>
		</el-descriptions>
		<el-descriptions title direction="vertical" border size="large">
			<!-- 回款记录 -->
			<el-descriptions-item>
				<template #label>
					<el-form-item label="回款记录" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-select
					v-model="payHistorySelectStrategy"
					filterable
					placeholder="选择账期过滤策略"
					style="width:220px;margin-bottom:10px"
				>
					<el-option
						v-for="s in paymentHistoryFilterOptions"
						:key="s.label"
						:label="s.label"
						:value="s.value"
					/>
				</el-select>
				<el-table border scrollbar-always-on :data="filteredPaymentList">
					<el-table-column
						prop
						label="回款周期"
						width="220"
						:sortable="true"
						:sort-method="(a, b) => {
              const t1 = a.payment_start_time ? new Date(a.payment_start_time).getTime() : 0;
              const t2 = b.payment_start_time ? new Date(b.payment_start_time).getTime() : 0;
              return t1 - t2;
            }"
					>
						<template #default="scope">
							<el-date-picker
								:model-value="[scope.row.payment_start_time, scope.row.payment_end_time]"
								@update:model-value="(val) => onPaymentRangeChange(val, scope.row)"
								type="daterange"
								unlink-panels
								start-placeholder="开始日期"
								end-placeholder="结束日期"
								value-format="YYYY-MM-DD"
								style="width: 100%"
								:disabled="isDetail"
							/>
						</template>
					</el-table-column>

					<el-table-column prop label="周期内回款批次" width="400">
						<template #default="scope">
							<el-table scrollbar-always-on :data="scope.row.payment_data_list" height="100">
								<el-table-column fixed="left" prop align="center" label="到账日期" width>
									<template #default="subScope">
										<el-date-picker
											v-model="subScope.row.payment_time"
											type="date"
											placeholder="请选择到账日期"
											value-format="YYYY-MM-DD"
											style="min-width:150px"
											:disabled="isDetail"
										/>
									</template>
								</el-table-column>
								<el-table-column fixed="left" prop align="center" label="到账金额(元)" width="120">
									<template #default="subScope">
										<el-input-number
											v-model="subScope.row.payment_amount"
											placeholder="请输入到账金额"
											style="width: 100%"
											:controls="false"
											:precision="2"
											:disabled="isDetail"
										/>
									</template>
								</el-table-column>
								<el-table-column align="center" fixed="right" width="80">
									<template #header>
										<el-button v-if="!isDetail" link type="primary" @click="addPayRow(scope.$index)">添加</el-button>
										<el-button v-else link type="primary" :disabled="true">添加</el-button>
									</template>
									<template #default="subScope">
										<el-button
											link
											type="warning"
											@click="deletePayRow(props.item.payment_list[scope.$index],subScope.$index)"
											:disabled="isDetail"
										>删除</el-button>
									</template>
								</el-table-column>
							</el-table>
						</template>
					</el-table-column>
					<el-table-column
						prop
						label="回款状态"
						width="120"
						:sortable="true"
						:sort-method="(a, b) => new Date(a.status) - new Date(b.status)"
					>
						<template #default="scope">
							<el-select v-model="scope.row.status" filterable placeholder="请指定回款状态" :disabled="isDetail">
								<el-option
									v-for="s in paymentStatuOptions"
									:key="s.label"
									:label="s.label"
									:value="s.value"
								/>
							</el-select>
						</template>
					</el-table-column>
					<el-table-column prop label="备注信息">
						<template #default="scope">
							<el-form-item>
								<el-input v-model="scope.row.description" :disabled="isDetail" />
							</el-form-item>
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="80">
						<template #header>
							<el-button link type="primary" @click="addTableRow" :disabled="isDetail">添加</el-button>
						</template>
						<template #default="scope">
							<el-button link type="warning" @click="deleteTableRow(scope.$index)" :disabled="isDetail">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
			</el-descriptions-item>
		</el-descriptions>
	</div>
</template>

<script setup>
import { ref,onBeforeMount,reactive,watch,toRefs,markRaw,defineProps,computed } from 'vue'
import * as api from '/@/api/contract/payment';
import { ElMessage } from 'element-plus'
import * as areaApi from '/@/views/system/areas/api.ts';
import * as customerApi from '/@/views/customer/customer/api.ts';
import * as contractApi from '/@/api/contract/contract';
// 接收参数
const props = defineProps({
    item: {
      type: Object,
    },
    method: {
      type: String
    }
})


const data = reactive({
  secondPartyOptions:[{
    value: '杭州星哉科技有限公司',
    label: '杭州星哉科技有限公司',
  }],
  paymentStatuOptions:[
    {
    value: '未回款',
    label: '未回款',
    },
    {
    value: '部分回款',
    label: '部分回款',
    },
    {
    value: '全部回款',
    label: '全部回款',
  }],
  paymentHistoryFilterOptions:[
    {
    value: 'onlyShowUnpaid',
    label: '仅显示未回款账期',
    },
    {
    value: 'onlyShowRemittance',
    label: '仅显示已回款账期',
    },
    // {
    // value: 'onlyShowPartialPayment',
    // label: '仅显示部分回款账期',
    // },
    {
    value: 'allRemittance',
    label: '显示全部账期',
    },
    {
    value: 'onlyShowCurPayPeriod',
    label: '仅显示当前账期',
    },
  ],
  contractList:[],
  customerList:[],
  availableContractStatusList:['审批通过','提前完结','已归档'],
  options: {
    rules: {
      amount: [
        {required: true, message: '请输入预回款金额', trigger: 'blur'}
      ],
      payee: [
        {required: true, message: '请选择收款主体', trigger: 'blur'}
      ],
      tax: [
        {required: true, message: '请输入纳税人识别号', trigger: 'blur'}
      ],
      contract_code: [
        {required: true, message: '请输入合同编号', trigger: 'blur'}
      ],
      payment_amount: [
        {required: true, message: '请输入到账金额', trigger: 'blur'}
      ],
      payment_time: [
        {required: true, message: '请选择回款日期', trigger: 'blur'}
      ],
    },

  }
})

const payHistorySelectStrategy = ref("onlyShowUnpaid")
const isPayErr = ref(false)
const lastSurplusPaymentAmount = ref(null)
const lastSelectPayAmount = ref(null)
const isDetail = computed(() => props.method === 'Detail')
const isCreate = computed(() => props.method === 'Create')

// 计算是否需要纳入押金（即用户设置了押金回款日期）
const includeCashPledge = computed(() => {
  return !!props.item.cashple_pay_time; // 有日期则说明押金应纳入
});

// 计算实际押金值
const cashPledgeAmount = computed(() => {
  return includeCashPledge.value ? Number(props.item.cash_pledge) || 0 : 0;
});

// 过滤历史回款数据
const filteredPaymentList = computed(() => {
  const list = props.item?.payment_list || []
  const now = new Date()

  switch (payHistorySelectStrategy.value) {
    case 'onlyShowUnpaid':
      return list.filter(item =>
        ['未回款','部分回款'].includes(item.status)
      )

    case 'onlyShowRemittance':
      return list.filter(item => item.status === '全部回款')

    // case 'onlyShowPartialPayment':
    //   return list.filter(item => item.status === '部分回款')

    case 'onlyShowCurPayPeriod':
      return list.filter(item => {
        const startStr = item.payment_start_time
        const endStr = item.payment_end_time

        if (!startStr || !endStr) return false

        const todayStr = new Date().toISOString().split('T')[0] // 取今天的 'YYYY-MM-DD'
        const startDateStr = new Date(startStr).toISOString().split('T')[0]
        const endDateStr = new Date(endStr).toISOString().split('T')[0]

        return todayStr >= startDateStr && todayStr <= endDateStr
      })

    case 'allRemittance':
    default:
      return list
  }
})


// 计算已回款金额（+ 押金）
const prePaymentAmount = computed(() => {
  if (!Array.isArray(props.item.payment_list)) return 0;

  const basePaid = props.item.payment_list.reduce((sum, paymentItem) => {
    if (!Array.isArray(paymentItem.payment_data_list)) return sum;
    const itemSum = paymentItem.payment_data_list.reduce((subSum, payData) => {
      return subSum + (Number(payData.payment_amount) || 0);
    }, 0);
    return sum + itemSum;
  }, 0);

  return basePaid + cashPledgeAmount.value;
});

// 计算未回款金额
const surplusPaymentAmount = computed(() => {
  const total = Number(props.item.amount)  || 0;
  const paid = prePaymentAmount.value; 
  const result = total - paid;
  return result > 0 ? Number(result.toFixed(2))  : 0;
});

// 监听变化并更新 item 字段
watch(
  [prePaymentAmount, () => props.item.amount, cashPledgeAmount],
  ([newPrePaymentAmount]) => {
    props.item.pre_payment_amount = newPrePaymentAmount;
    props.item.surplus_payment_amount = surplusPaymentAmount.value;

    const total = Number(props.item.amount) || 0;
    if (newPrePaymentAmount === 0 && total !== 0) {
      props.item.status = '未回款';
    } else if (newPrePaymentAmount < total) {
      props.item.status = '部分回款';
    } else {
      props.item.status = '全部回款';
    }
  },
  { immediate: true }
);

// 监听 payment_list 变化（深度监听）
watch(
  () => props.item.payment_list,
  () => {
    computePrePaymentStatus();
  },
  { deep: true,}
);

const computePrePaymentStatus = () => {
  const total_amount = Number(props.item.amount) || 0;
  const now = new Date();
  const list = props.item.payment_list || [];

  const relevant = list.filter(item => {
  const start = item.payment_start_time ? new Date(item.payment_start_time) : null;
  const end = item.payment_end_time ? new Date(item.payment_end_time) : null;
    return (
      (start && end && now >= start && now <= end) || // 当前时间在范围内
      (end && now > end) // 或者账期已结束
    );
  });
  if (total_amount <= 0){
    props.item.pre_payment_status = '全部回款';
  } 
  else if (relevant.length === 0) {
    props.item.pre_payment_status = '未回款';
  } 
  else {
    const allPaid = relevant.every(item => item.status === '全部回款');
    props.item.pre_payment_status = allPaid ? '全部回款' : '未回款';
  }
  // console.log(props.item.pre_payment_status)
}

// 当用户修改日期范围时同步回原始字段
const onPaymentRangeChange = (val, row) => {
  if (Array.isArray(val)) {
    row.payment_start_time = val[0];
    row.payment_end_time = val[1];
  } else {
    row.payment_start_time = '';
    row.payment_end_time = '';
  }
};

const handleSurplusPay = () => {
  if (surplusPaymentAmount.value <= 0) {
    ElMessage.warning('已无剩余回款，不需要结清');
    return;
  }

  const today = new Date();
  const formattedDate = today.toISOString().split('T')[0];

  const newRow = {
    payment_amount: surplusPaymentAmount.value,
    payment_time: formattedDate,
    desc: '结清剩余回款',
  };

  props.item.payment_list.push(newRow);
}

const addPayRow = (index) => {
  const newRow = {
    payment_time: '',
    payment_amount: 0
  }
  if (props.item.payment_list[index].payment_data_list == null ||  props.item.payment_list[index].payment_data_list == undefined){
    props.item.payment_list[index].payment_data_list = []
  }
  props.item.payment_list[index].payment_data_list.unshift(newRow)
}

const deletePayRow = (row,index) => {
  row.payment_data_list.splice(index,1)
}

// 添加行的方法
const addTableRow = () => {
	const newRow = {
    payment_start_time: '',
    payment_end_time: '',
		payment_data_list: [],
    desc: '',
    status: '未回款'
	};
	// 在列表后面添加数据
	props.item.payment_list.unshift(newRow);
};

// 删除行的方法
const deleteTableRow = (index) => {
	props.item.payment_list.splice(index, 1);
};

const customerChanged = async (name) => {
  const selected = data.customerList.find(item => item.name === name);
  if (!selected) return;
  props.item.invoice_phone = selected.phone;
  let provinceName = '';
  let cityName = '';
  let districtName = '';
  if (selected.province) {
    const res = await areaApi.GetLevelAllAreasList({ level: 1, code: selected.province });
    provinceName = res?.data?.[0]?.name || '';
  }
  if (selected.city) {
    const res = await areaApi.GetLevelAllAreasList({ level: 2, code: selected.city });
    cityName = res?.data?.[0]?.name || '';
  }
  if (selected.district) {
    const res = await areaApi.GetLevelAllAreasList({ level: 3, code: selected.district });
    districtName = res?.data?.[0]?.name || '';
  }
  props.item.invoice_address = [provinceName, cityName, districtName]
    .filter(Boolean)
    .join('') || null;
};

const customerVisibleChanged = async(visible) => {
  if (visible) {
    const response = await customerApi.GetList({ limit: 10000 });
    data.customerList = response.data
  }
}

const visibleChanged = async(visible) => {
  // 展开的时候去查询items
  if (visible) {
    getContractList()
  }
}

const contractChanged = (code) => {
  isPayErr.value = false
  const selected = data.contractList.find(item => item.code === code)
  props.item.cash_pledge = selected.cash_pledge
  if (isCreate.value){
    props.item.amount = Number(selected.amount)
  }
  props.item.invoice_title = selected.customer_name
  customerChanged(selected.customer_name)
  if (selected.status == "提前完结" && surplusPaymentAmount.value > 0){
    isPayErr.value = true
  }
}

const getContractList  = async() => {
    const response = await contractApi.GetList({ limit: 10000 });
    data.contractList = response.data.filter(item => data.availableContractStatusList.includes(item.status))
}

const { options,secondPartyOptions,contractList,customerList,paymentStatuOptions,paymentHistoryFilterOptions } = toRefs(data);

onBeforeMount(async()=>{
    await customerVisibleChanged(true)
    if(props.method != "Create"){
      computePrePaymentStatus()
      await getContractList().then((res)=>{
        contractChanged(props.item.contract_code)
      })
    }
})

</script>

<style scoped>
.el-form .el-form-item:last-of-type {
	margin-bottom: 2px !important;
}

/* label 样式调整 */
::v-deep(.form-label-in-descriptions .el-form-item__label) {
	font-size: 14px;
}

/* 控制左边 label 的统一宽度 */
::v-deep(.el-descriptions__label) {
	width: 160px !important;
}

:deep(.my-label) {
	background: var(--el-color-danger-light-9) !important;
}

.element-between {
	display: flex;
	flex-direction: row;
	align-items: center;
}
</style>
