<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
        <customELTransfer>授权</customELTransfer>
      </template>
    </fs-crud>
	</fs-page>
</template>

<script lang="ts" setup name="rowPermission">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import customELTransfer from './customELTransfer/index.vue';

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>
