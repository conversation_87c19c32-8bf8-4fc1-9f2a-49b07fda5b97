<template>
  <div class="card-custom-layout">
    <div class="layout-header">
      <!-- ↓↓↓↓↓  关键插槽：查询  ↓↓↓↓ -->
      <slot name="search"></slot>
    </div>
    <div class="layout-top">
      <!-- ↓↓↓↓↓  关键插槽：动作条  ↓↓↓↓ -->
      <slot name="actionbar"></slot>
      <!-- ↓↓↓↓↓  关键插槽：工具条  ↓↓↓↓ -->
      <slot name="toolbar"></slot>
    </div>

    <!-- 高度需要自适应撑开，可以通过flex:1 -->
    <div class="layout-body">
      <!-- 默认插槽 -->
      <slot></slot>
      <slot name="tabs"></slot>
      <!-- <el-divider /> -->
      <!-- ↓↓↓↓↓  关键插槽：表格  ↓↓↓↓ -->
      <slot name="table"></slot>
      
      <!-- ↓↓↓↓↓  关键插槽：表单  ↓↓↓↓ -->
      <slot name="form"></slot>
    </div>
    <div class="layout-footer">
      <!-- ↓↓↓↓↓  关键插槽：分页条  ↓↓↓↓ -->
      <slot name="pagination"></slot>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
/**
 * 自定义布局
 */
export default defineComponent({
  name: "CardCustomLayout"
});
</script>

<style lang="less">
.card-custom-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  // border-demo
  border: 8px solid #ffffff;
  border-radius: 8px 8px 8px 8px;
  box-shadow: 0px 2px 2px 2px rgba(0, 0, 0, .04), 0px 2px 2px rgba(0, 0, 0, .08);

  .layout-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0px 15px 10px 15px;
  }
  .layout-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0px 15px 0px 15px;
  }
  .layout-body {
    flex: 1;
    overflow-y: auto;
    border: 8px solid #ffffff;
    border-radius: 8px 8px 8px 8px;
    margin-top: 10px;
    margin-bottom: 10px;

  }
  .layout-footer {
    padding: 5px 10px 5px 10px;
  }
  .fs-crud-actionbar {
    display: flex;
    align-items: center;
  }
  .fs-crud-footer {
    padding: 0 10px 0 10px;
  }
  .fs-crud-pagination {
    .fs-pagination {
      .el-pagination {
        justify-content: start;
      }
    }
  }
}
</style>
