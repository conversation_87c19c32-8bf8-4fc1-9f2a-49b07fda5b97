<!-- 安全组 -->
<template>
  <div class="app-container">
    
      <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="queryParams.search"
            placeholder="输入安全组ID、名称进行搜索"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetClick()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>


    <el-card shadow="never">
      <div class="mb-[10px]">
        <el-button type="success" @click="handleAddClick()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button
          type="danger"
          :disabled="ids.length === 0"
          @click="handleDelete()"
        >
          <i-ep-delete />
          删除
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        highlight-current-row
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column type="expand" label="" width="50">
          <template #default="props">
            <el-table :data="props.row.security_group_rules" :stripe="false" :border="false" class="no-border-table" :default-sort="{prop: 'standard_attr_id', order: 'ascending'}">
              <!-- <el-table-column label="ID" prop="id"/> -->
              <el-table-column label="序号" prop="standard_attr_id" width="100"/>
              <el-table-column label="方向"  width="100">
                <template #default="scope">
                  <el-tag v-if="scope.row.direction === 'ingress'" type="warning" >
                    入口
                  </el-tag>
                  <el-tag v-if="scope.row.direction === 'egress'" type="success" >
                    出口
                  </el-tag>
                </template>
              </el-table-column>
              
              <el-table-column label="Ether Type" prop="ethertype" width="100" />
              <el-table-column label="IP Protocol" width="150">
                <template #default="scope">
                  <div v-if="scope.row.protocol">
                    {{ scope.row.protocol }}
                  </div>
                  <div v-else>
                    任何
                  </div>
                </template>
              </el-table-column>
              <!-- 自定义列，组合多个值 -->
              <el-table-column label="端口范围" width="100">
                <template #default="scope">
                  <div v-if="scope.row.port_range_min">
                    {{ scope.row.port_range_min }}/{{ scope.row.port_range_max }}
                  </div>
                  <div v-else>
                    任何
                  </div>
                </template>
              </el-table-column>
              <!-- 根据不同条件，组合出-->
              <el-table-column label="Remote IP Prefix" prop="remote_ip_prefix" width="180">
                <template #default="scope">
                  <div v-if="scope.row.remote_ip_prefix">
                    {{ scope.row.remote_ip_prefix }}
                  </div>
                  <div v-else-if="scope.row.remote_group_id">
                    -
                  </div>
                  <div v-else-if="scope.row.ethertype ==='IPv4'">
                    0.0.0.0/0
                  </div>
                  <div v-else-if="scope.row.ethertype ==='IPv6'">
                    ::/0
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="Remote Group" prop="remote_group_id" width="180" >
                <template #default="scope">
                  <div v-if="scope.row.remote_group_id">
                    {{ sgObject[scope.row.remote_group_id] }}
                  </div>
                </template>
              </el-table-column>
              
              <el-table-column label="描述" prop="description"/>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="name"  width="200"/>
        <el-table-column label="项目" prop="project_name" width="200" />
        <el-table-column label="ID" prop="sg_id" />
        <el-table-column label="共享" prop="is_shared"  width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_shared === true ? 'success' : 'info'">
              {{ scope.row.is_shared === true ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="description" />


        <!-- <el-table-column fixed="right" label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="handleEditClick(scope.row.id, scope.row.name)"
            >
              <i-ep-edit />
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click.stop="handleDelete(scope.row.id)"
            >
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!--安全组弹窗-->

    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="80%"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="computedRules"
        label-width="100px"
      >
        <el-card shadow="never">
          <el-row>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入安全组名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="项目" prop="description">
                <el-select v-model="formData.project_name" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in imageOptions" :key="index" :label="item.label" :value="item.value"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="描述" prop="volume_size">
                <el-input v-model="formData.description" placeholder="请输入描述" />
              </el-form-item>
            </el-col>
          </el-row>

        </el-card>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitClick">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import "/@/styles/index.scss";   // 基础样式
import { reactive, ref, computed, onMounted, defineOptions } from "vue";
import { ElForm, ElInput, ElDrawer, ElButton, ElTag, ElMessage, ElMessageBox } from "element-plus";
import  Pagination from  "/@/components/Pagination/index.vue";

defineOptions({
  name: "SecurityGroup",
  inherititems: false,
});

import SecurityGroupAPI, { SecurityGroupPageQuery, SecurityGroupPageVO, SecurityGroupForm } from "./api";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);
const sgOptions = ref<any[]>([]);
// 将sgOptions数组改造成一个map，使用compute在元数据变化时自动更新
const sgObject = computed(() => {
  return sgOptions.value.reduce((obj, item) => {
    obj[item.sg_id] = item.name;
    return obj;
  }, {});
});

const queryParams = reactive<SecurityGroupPageQuery>({
  page: 1,
  limit: 10,
});

const tableData = ref<SecurityGroupPageVO[]>();

// 安全组弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

const formData = reactive<SecurityGroupForm>({});

const computedRules = computed(() => {
  const rules: Partial<Record<string, any>> = {
    name: [{ required: true, message: "请输入安全组名称", trigger: "blur" }],
  };
  return rules;
});


// 查询安全组列表
function getSgList() { 
  SecurityGroupAPI.getList()
    .then((data: any) => {
      sgOptions.value = data.results;
    });
}


// 查询
function handleQuery() {
  loading.value = true;
  SecurityGroupAPI.getPage(queryParams)
    .then((data: any) => {
      tableData.value = data.data;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetClick() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

// 行选择
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 新增安全组
function handleAddClick() {
  dialog.visible = true;
  dialog.title = "新增安全组";
}

/**
 * 编辑安全组
 *
 * @param id 安全组ID
 */
function handleEditClick(id: number, name: string) {
  dialog.visible = true;
  dialog.title = "【" + name + "】安全组修改";
  ImageAPI.getFormData(id).then((data) => {
    Object.assign(formData, data);
  });
}

// 提交安全组表单
function handleSubmitClick() {
  dataFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      loading.value = true;
      const id = formData.insid;
      if (id) {
        ImageAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        ImageAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭安全组弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.insid = undefined;
  formData.volumeItems = [];
}
/**
 * 删除安全组
 *
 * @param id 安全组ID
 */
function handleDelete(id?: number) {
  const attrGroupIds = [id || ids.value].join(",");
  if (!attrGroupIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      ImageAPI.deleteByIds(attrGroupIds).then(() => {
        ElMessage.success("删除成功");
        handleResetClick();
      });
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

/** 新增Volume项 */
function handleAddAttrClick() {
  formData.volumeItems = formData.volumeItems ?? [];
  formData.volumeItems.push({ sort: 1, status: 1 });
}

/** 删除安全组项 */
function handleDeleteAttrClick(index: number) {
  if (formData.volumeItems && formData.volumeItems.length > 0) {
    formData.volumeItems.splice(index, 1);
  }
}

onMounted(() => {
  handleQuery();
  getSgList();
  console.log(sgObject)
});
</script>


<style>
/* 去除表格边框和线框 */
.no-border-table {
  border: none;
}

.no-border-table .el-table__header-wrapper,
.no-border-table .el-table__body-wrapper,
.no-border-table .el-table__footer-wrapper {
  border: none;
}

.no-border-table th,
.no-border-table td {
  border: none;
}

/* 如果需要保留头部和底部的边框，可以仅去除中间的线框 */
.no-border-table .el-table__row {
  border: none;
}
</style>