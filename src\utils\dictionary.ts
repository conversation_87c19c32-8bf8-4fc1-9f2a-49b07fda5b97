import { toRaw } from 'vue';
import { DictionaryStore } from '/@/stores/dictionary';

/**
  * @method 获取指定name字典
  */
export const dictionary = (name: string, key:string|number|undefined): any => {
  const dict = DictionaryStore()
  const dictionary = toRaw(dict.data)
  if(key!=undefined){
    const obj = dictionary[name]?.find((item:any) => item.value === key)
    return obj ? obj.label : ''
  }else{
    return dictionary[name] || {}
  }
}


/**
  * @method 获取对象内指定key的值
  */
type SourceDataType = {
  [key: string]: any;
};

export const getLabelByKeyValue = (key: string, value: any, label: any, data: SourceDataType[]): string | undefined => {
  const item = data.find(item => item[key] === value);
  return item ? item[label] : undefined;
}
