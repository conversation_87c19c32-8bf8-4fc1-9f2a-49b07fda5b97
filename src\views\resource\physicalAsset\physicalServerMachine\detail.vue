<template>
  <fs-page>
    <div class="container-content">
      <div class="detail-base-cards">
        <el-descriptions :column="3" size="large">
          <template #title>
            <div class="id-wrapper">
              <span>物理服务器SN：{{ state.detailData.physical_machine_sn }}</span>

              <el-link :underline="false" type="primary" :icon="DocumentCopy"
                @click="copyText(state.detailData.physical_machine_sn || '')"></el-link>
              <span style="padding-left: 20px; padding-right:20px;">|</span>
              <el-link type="primary" @click="router.go(-1)">返回</el-link>
            </div>
          </template>
          <template #extra>
            <div class="toolbar-container">

              <!-- 无需其他超链接操作 前端 Template 中的注释写法 -->
              <!-- <el-link :underline="false" type="primary"  target="_blank" :href="`http://${state.detailData.name}`" :icon="Monitor">&nbsp;BMC控制台</el-link> -->
              <el-dropdown>
                <span class="el-dropdown-link">
                  操作
                  <el-icon class="el-icon--right">
                    <arrow-down />
                  </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :disabled="true">
                      点击我
                      <!-- 需添加@click事件绑定 -->
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
          </template>

          <el-descriptions-item label="名称" label-class-name="desc-customer-label" class-name="desc-customer-content">{{
            state.detailData.name }}</el-descriptions-item>
          <el-descriptions-item label="设备类型" label-class-name="desc-customer-label"
            class-name="desc-customer-content">{{
              state.detailData.machine_type }}</el-descriptions-item>
          <el-descriptions-item label="型号规格" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.machine_specs }}
          </el-descriptions-item>
          <el-descriptions-item label="GPU类型" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.gpu_model }}
          </el-descriptions-item>
          <el-descriptions-item label="创建于" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.create_datetime }}
          </el-descriptions-item>
          <el-descriptions-item label="更新于" label-class-name="desc-customer-label" class-name="desc-customer-content">
            {{ state.detailData.update_datetime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div class="container-content">
      <div class="detail-base-cards">
        <tiny-layout :cols="24">
          <tin-row :gutter="40">
            <!--- 左侧信息区---->
            <tiny-col :span="12">
              <tiny-card title="详情" class="detial-sub-cards" :auto-width="true">
                <tiny-layout :cols="24">
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      厂商
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.manufacturer || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      资产归属
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.belong || '-' }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      规格
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.specs || '-' }}
                    </tiny-col>
                  </tiny-row>
                </tiny-layout>
              </tiny-card>

            </tiny-col>
            <!--- 右侧信息区 ---->
            <tin-col :span="12">
              <tiny-card title="上架信息" class="detial-sub-cards" :auto-width="true">
                <tiny-layout :cols="24">
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      机房
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.machine_room }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      包间
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.private_room }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      机柜
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.idc_rack_machine }}
                    </tiny-col>
                  </tiny-row>
                  <tiny-row :gutter="20">
                    <tiny-col :span="8">
                      U&nbsp;位
                      <!-- &nbsp;   在前端显示一个空格； -->
                    </tiny-col>
                    <tiny-col :span="16">
                      {{ state.detailData.rack_unit }}
                    </tiny-col>
                  </tiny-row>

                </tiny-layout>
              </tiny-card>
            </tin-col>
          </tin-row>
        </tiny-layout>
      </div>
    </div>
  </fs-page>
</template>
<script lang="ts" setup>
import { onMounted, reactive, } from 'vue';
import { GetObj } from '/@/api/resource/physicalAsset/physicalServerMachine';
import { TinyCard, TinyLayout, TinyRow, TinyCol, } from '@opentiny/vue';
import { useRouter } from 'vue-router';
import { Monitor, DocumentCopy, ArrowDown, View } from '@element-plus/icons-vue';
import { copyText } from '/@/utils/copyText';
// import { toStringJSON } from 'xe-utils';
// import { toOPServerDetail } from '/@/router/intervalRouterTo/tenant';
import type { DetailPhysicalServerMachine } from '/@/views/interface/resource/physicalAsset/physicalServerMachine';
import { detailPhysicalServerMachineExample } from '/@/views/interface/resource/physicalAsset/physicalServerMachineExample';


const router = useRouter();


defineProps({
  id: {
    type: String,
    required: false,
    default: '',
  },
});

const routerIronicicHypervisorId = router.currentRoute.value.params.id || '';

const state = reactive({
  detailData: detailPhysicalServerMachineExample as DetailPhysicalServerMachine,
  loading: true,
  success: false,
})


const getDetailData = async () => {
  try {
    const response = await GetObj(routerIronicicHypervisorId);
    state.detailData = response.data;
    state.loading = false;
    state.success = true;
  }
  catch (e) {
    console.error('获取详情数据失败', e);
    state.loading = false;
    state.success = false;
    return;
  }
}

onMounted(async () => {
  await Promise.all([
    getDetailData()
  ])
});
</script>
<style lang="less" scoped>
.container-content {
  display: grid;
  grid-template-columns: auto;
  gap: 8px;
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
}

.toolbar-container {
  display: flex;
  align-items: center;
  gap: 12px; /* 元素间隔 */
  padding: 8px 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

/* 卡片内容限制 */
.detail-base-cards {
  margin: 16px;
  box-sizing: border-box;
  box-shadow: none;
}

.id-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  max-width: 100%;
}

.detial-sub-cards {
  line-height: 2.4;
  margin-bottom: 16px;
}


:deep(.desc-customer-label) {
  color: #909399;
  font-weight: bolder;
}

:deep(.desc-customer-content) {
  color: #000;
}

</style>