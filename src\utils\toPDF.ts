import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

export const downLoadPdfA4Multiple = () => {
  const ele = document.body
  html2canvas(ele, {
    scale: 2, // 使用设备的像素比
  }).then(canvas => {
    let position = 0 //页面偏移
    const A4Width = 595.28 // A4纸宽度
    const A4Height = 841.89 // A4纸宽

    // 一页PDF可显示的canvas高度
    const pageHeight = (canvas.width * A4Height) / A4Width
    // 未分配到PDF的canvas高度
    let unallottedHeight = canvas.height

    // canvas对应的PDF宽高
    const imgWidth = A4Width
    const imgHeight = (A4Width * canvas.height) / canvas.width

    const pageData = canvas.toDataURL('image/jpeg', 1.0)
    const pdf = new jsPDF('', 'pt', [A4Width, A4Height])

    // 当canvas高度 未超过 一页PDF可显示的canvas高度，无需分页
    if (unallottedHeight <= pageHeight) {
      pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
      pdf.save('下载多页PDF（A4纸）.pdf')
      return
    }

    while (unallottedHeight > 0) {
      pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
      unallottedHeight -= pageHeight
      position -= A4Height
      if (unallottedHeight > 0) {
        pdf.addPage()
      }
    }
    pdf.save('下载多页PDF（A4纸）.pdf')
  })
}

export const downLoadPdfAutoMultiple = async () => {
  const ele = document.body; 
  
  // 获取完整页面截图 
  const canvas = await html2canvas(ele, {
    scale: window.devicePixelRatio  * 2,
    scrollY: -window.scrollY,  // 确保捕获整个页面 
    windowHeight: document.body.scrollHeight,  // 设置窗口高度为文档总高度 
    useCORS: true, // 处理跨域资源 
    allowTaint: true, // 允许污染图像 
  });
 
  // PDF页面尺寸设置（A4比例）
  const pdfWidth = 595.28; // A4宽度(单位: pt)
  const pdfHeight = 841.89; // A4高度 
  
  // 计算缩放比例 
  const scale = pdfWidth / canvas.width; 
  const scaledHeight = canvas.height  * scale;
  
  // 创建PDF实例 
  const pdf = new jsPDF('p', 'pt', 'a4');
  
  // 计算每页可显示的高度 
  const pageHeight = pdfHeight;
  let remainingHeight = scaledHeight;
  let position = 0;
  
  // 分页处理 
  while (remainingHeight > 0) {
    // 当前页可显示的高度 
    const displayHeight = Math.min(remainingHeight,  pageHeight);
    
    // 添加图片到PDF 
    pdf.addImage( 
      canvas.toDataURL('image/jpeg',  1.0),
      'JPEG',
      0, // x坐标 
      position, // y坐标 
      pdfWidth, // 宽度 
      displayHeight // 高度 
    );
    
    // 更新剩余高度和位置 
    remainingHeight -= pageHeight;
    position -= pageHeight;
    
    // 如果还有内容，添加新页 
    if (remainingHeight > 0) {
      pdf.addPage('a4'); 
    }
  }
  
  // 保存PDF 
  pdf.save(' 完整页面导出.pdf');
};


export const segmentedPdfExport = async () => {
  const segmentHeight = window.innerHeight; 
  const totalSegments = Math.ceil(document.body.scrollHeight  / segmentHeight);
  const pdf = new jsPDF('p', 'pt', 'a4');
  
  for (let i = 0; i < totalSegments; i++) {
    const canvas = await html2canvas(document.body,  {
      scrollY: -window.scrollY  - (i * segmentHeight),
      windowHeight: segmentHeight,
      scale: 2,
      useCORS: true 
    });
    
    const imgData = canvas.toDataURL('image/jpeg'); 
    const imgWidth = pdf.internal.pageSize.getWidth(); 
    const imgHeight = (canvas.height  * imgWidth) / canvas.width; 
    
    if (i > 0) pdf.addPage(); 
    pdf.addImage(imgData,  'JPEG', 0, 0, imgWidth, imgHeight);
  }
  
  pdf.save(' 分段导出.pdf'); 
};

export const perfectPdfExport = async () => {
  try {
    // 1. 准备参数 
    const targetElement = document.body; 
    const pageWidth = 595.28; // A4宽度(单位: pt)
    const pageHeight = 841.89; // A4高度 
    const padding = 40; // 页边距 
    
    // 2. 创建PDF实例 
    const pdf = new jsPDF('p', 'pt', 'a4');
    let currentPosition = 0;
    
    // 3. 计算总分段数 
    const viewportHeight = window.innerHeight; 
    const totalHeight = targetElement.scrollHeight; 
    const segments = Math.ceil(totalHeight  / viewportHeight);
    
    // 5. 分段处理 
    for (let i = 0; i < segments; i++) {
      // 5.1 计算当前段位置 
      const scrollY = i * viewportHeight;
      
      // 5.2 截取当前视窗 
      const canvas = await html2canvas(targetElement, {
        scrollY: -scrollY,
        windowHeight: viewportHeight,
        scale: 2,
        useCORS: true,
        logging: false,
        backgroundColor: '#FFFFFF',
        removeContainer: true 
      });
      
      // 5.3 计算缩放比例 
      const imgWidth = pageWidth - padding * 2;
      const imgHeight = (canvas.height  * imgWidth) / canvas.width; 
      
      // 5.4 添加图片到PDF 
      if (i > 0) {
        pdf.addPage('a4'); 
        currentPosition = 0;
      }
      
      pdf.addImage( 
        canvas.toDataURL('image/jpeg',  0.95),
        'JPEG',
        padding,
        currentPosition + padding,
        imgWidth,
        imgHeight 
      );
      
      currentPosition += imgHeight;
      
      // 5.5 释放内存 
      canvas.remove(); 
      
    }
    
    pdf.save(' 完美导出.pdf');
    
  } catch (error) {
    console.error('PDF 导出失败:', error);
  }
};