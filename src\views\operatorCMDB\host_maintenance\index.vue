<template>
  <fs-page>
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <!-- <template #actionbar-right>
        <importExcel api="api/tenants/tenant-op-vlan/" v-if="isShowImportBtn">导入</importExcel>
      </template> -->
    </fs-crud>
  </fs-page>	
</template>

<script lang="ts" setup name="hostMaintenance">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { useRoute } from 'vue-router';
// import importExcel from '/@/components/importExcel/index.vue';
// import { auth } from "/@/utils/authFunction";

const router = useRoute();
const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });



// 导入按钮显示权限
// const isShowImportBtn: boolean = auth("resource:privateRoom:Import")

// 页面打开后获取列表数据
onMounted(() => {
	const requestQuery = router.query;
	if (requestQuery.id) {
		crudExpose.doSearch({ form: { id: requestQuery.id } });
	} else {
		crudExpose.doRefresh();
	}
});
</script>
