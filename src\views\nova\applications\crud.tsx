import {
  dict,
  AddReq,
  DelReq,
  <PERSON>Req,
  CrudExpose,
  UserPageQuery,
  CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import * as api from './api';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';


//此处为crudOptions配置
export default function ({ crudExpose, }: { crudExpose: CrudExpose; }): CreateCrudOptionsRet {
  const pageRequest = async (query: any) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  const exportRequest = async (query: UserPageQuery) => {
    return await api.exportData(query)
  }

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('ApplicationsModelViewSet:Create')
          },
          export: {
            show: auth('ApplicationsModelViewSet:Export'),
            text: "导出",//按钮文字
            title: "导出",//鼠标停留显示的信息
            click() {
              return exportRequest(crudExpose.getSearchFormData())
            }
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 160,
        buttons: {
          view: {
            text: '查看',
            link: true,
            type: 'primary',
            show: auth('ApplicationsModelViewSet:Retrieve')
          },
          edit: {
            text: '编辑',
            link: true,
            type: 'primary',
            show: auth('ApplicationsModelViewSet:Update'),
          },
          remove: {
            text: '删除',
            link: true,
            type: 'danger',
            show: auth('ApplicationsModelViewSet:Delete'),
          },
        },
      },
      columns: {
        name: {
          title: '应用名称',
          type: 'input',
          search: { show: true },
          column: { minWidth: 100 },
          form: {
            rules: [{ required: true, message: '应用名称必填' }],
            component: {
              placeholder: '请输入应用名称',
            },
          },
        },
        project: {
          title: '项目',
          type: 'dict-select',
          search: { show: false },
          column: { minWidth: 100 },
          dict: dict({
            isTree: true,
            url: '/api/ProjectsModelViewSet/all_projects/',
            value: 'id',
            label: 'name'
          }),
          form: {
            rules: [
              // 表单校验规则
              {
                required: true,
                message: '必填项',
              },
            ],
            component: {
              filterable: true,
              placeholder: '请选择项目',
              props: {
                checkStrictly: true,
                props: {
                  value: 'id',
                  label: 'name',
                },
              },
            },
          },
        },
        url: {
          title: '应用地址',
          type: 'copyable',
          search: { show: true },
          column: {
            minWidth: 200,
          },
          form: {
            rules: [{ required: true, message: '应用地址必填' }],
            component: {
              placeholder: '请输入应用地址',
            },
          },
        },
        port: {
          title: '端口号',
          type: 'number',
          search: { show: false },
          column: { minWidth: 60 },
          form: {
            component: {
              placeholder: '请输入端口号',
            },
          },
        },
        user: {
          title: '管理账号',
          type: 'input',
          search: { show: false },
          column: { minWidth: 100 },
          form: {
            component: {
              placeholder: '请输入管理账号',
            },
          },
        },
        passwd: {
          title: '管理密码',
          type: 'password',
          search: { show: false },
          column: { 
            minWidth: 100,
            show: false,
          },
          form: {
            component: {
              placeholder: '请输入管理密码',
            },
          },
        },
        app_type: {
          title: '应用类型',
          type: 'dict-select',
          dict: dict({
            data: dictionary('nova:app_type', undefined),
          }),
          form: {
            component: {
              span: 12,
            },
          },
          component: { props: { color: 'auto' } }, // 自动染色
        },
        deploy_dir: {
          title: '部署目录',
          type: 'input',
          search: { show: false },
          column: {
            minWidth: 100,
            show: false,
          },
          form: {
            component: {
              placeholder: '请输入部署目录',
            },
          },
        },
        memo: {
          title: '备注',
          type: 'input',
          search: { show: false },
          column: { minWidth: 100 },
        },

      },
    },
  };
}
