<template>
  <div>
    <el-tooltip effect="dark" :content="props.desc" placement="top">
       <tiny-image :src="iconSrc" fit="scale-down" class="icon-image" />
    </el-tooltip>
  </div>
</template>

<script lang="ts" setup name="showImageName">
import { computed } from 'vue';
import { TinyImage } from '@opentiny/vue';
import centos from '../../../assets/img/op-image-types/centos.svg';
import ubuntu from '../../../assets/img/op-image-types/ubuntu.svg';
import debian from '../../../assets/img/op-image-types/debian.svg';
import fedora from '../../../assets/img/op-image-types/fedora.svg';
import freebsd from '../../../assets/img/op-image-types/freebsd.svg';
import coreos from '../../../assets/img/op-image-types/coreos.svg';
import openeuler from '../../../assets/img/op-image-types/openeuler.svg';
import opensuse from '../../../assets/img/op-image-types/opensuse.svg';
import linux from '../../../assets/img/op-image-types/linux.svg';
import windows from '../../../assets/img/op-image-types/windows.svg';

// 定义props
const props = defineProps({
  desc: {
    type: String,
    required: true,
    default: ''
  }
});

// 计算图标路径
const iconSrc = computed(() => {
  const descLower = props.desc.toLowerCase(); // 将 desc 转换为小写
  if (descLower.includes('centos')) return centos;
  if (descLower.includes('ubuntu')) return ubuntu;
  if (descLower.includes('debian')) return debian;
  if (descLower.includes('fedora')) return fedora;
  if (descLower.includes('freebsd')) return freebsd;
  if (descLower.includes('coreos')) return coreos;
  if (descLower.includes('openeuler')) return openeuler;
  if (descLower.includes('opensuse')) return opensuse;
  if (descLower.includes('win')) return windows;
  return linux;
});
</script>

<style scoped>
.icon-image {
  width: 24px;
  height: 24px;
}
</style>