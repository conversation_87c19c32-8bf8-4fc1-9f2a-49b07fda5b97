export interface Base {
  seq: number | undefined;
  id: string | undefined;
  dept_belong_id: string | undefined;
  description: string | undefined;
  createor: string | undefined;
  modifier: string | undefined;
  update_datetime: string | undefined;
  create_datetime: string | undefined;
  is_deleted: boolean | undefined;
}


export interface TenantBase {
  seq: number | undefined;
  id: string | undefined;
  dept_belong_id: string | undefined;
  description: string | undefined;
  createor: string | undefined;
  modifier: string | undefined;
  update_datetime: string | undefined;
  create_datetime: string | undefined;
  is_deleted: boolean | undefined;
  project_id: string | undefined;
  tenant_id: string | undefined;
}


export type DynamicKeyValuePair<T extends string = string> = {
  [key in T] : string 
} & {
  key: string;
  value: string|number|Array<T>|undefined;
}

export interface StateResDetailBase {
  detailData: DynamicKeyValuePair[] | undefined;
  loading: boolean,
  success: boolean,
}


export interface StateResTableDetailBase {
  TableData: DynamicKeyValuePair[] | undefined;
  loading: boolean,
  success: boolean,
}
