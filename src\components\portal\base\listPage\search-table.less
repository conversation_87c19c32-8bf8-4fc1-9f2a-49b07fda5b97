.container-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 60px);
  overflow-x: hidden;
  overflow-y: auto;
}

.line {
  height: 1px;
  color: rgb(213, 213, 213);
  background-color: rgb(213, 213, 213);
  border: 0;
}

.contain {
  flex: 1 1 auto;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);

  .contain-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30px 30px 0 30px;

    hr {
      .line();

      width: 86%;
      margin: 0 20px;
    }

    span {
      color: #1a1818;
      font-size: 16px;
    }
  }

  .contain-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 52px;
  }

  .contain-img {
    position: relative;
    display: flex;
    cursor: pointer;

    img:hover {
      background: whitesmoke;
    }
  }

  .contain-text {
    padding-left: 10px;
    color: #4e5969;
    font-size: 14px;
    cursor: pointer;
  }
}

.bottom-line {
  padding-bottom: 24px;

  hr {
    .line();

    width: 96%;
    margin: 0 auto;
  }
}

.filter-form {
  margin: 30px 0;
}

.col {
  width: 96%;
  height: 70px;
}

:deep(.tiny-pager) {
  float: right;
}

:deep(.tiny-grid-button__wrapper) {
  width: 100%;
}

:deep(.tiny-form-item__label) {
  color: #494747;
  font-weight: normal;
}

:deep(.tiny-grid-header__column) {
  height: 35px;
  color: rgb(139, 137, 137);
  background-color: #f5f6f7;
}

.operation {
  color: #5e7ce0;
}

.btn {
  display: flex;
  justify-content: space-between;
  width: 100%;

  button {
    border-radius: 4px;
    width: 100px;
    height: 36px;
  }

  .screen {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    text-align: center;
    cursor: pointer;

    span {
      margin-left: 10px;
      color: #4e5969;
      font-size: 14px;
    }
  }
}

.status {
  &-dot {
    position: relative;
    top: -1px;
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 5px;
    vertical-align: middle;
    background-color: #1890ff;
    border-radius: 50%;
  }

  &-closed {
    .status-dot {
      background-color: #d9d9d9;
    }
  }

  &-finished {
    .status-dot {
      background-color: #52c41a;
    }
  }
}

.tiny-fullscreen {
  background: #fff;
}

.tiny-fullscreen-wrapper {
  min-height: 710px;
  padding: 0 30px;
}

.tiny-fullscreen-scroll {
  overflow-y: auto;
}

.search-btn {
  display: flex;
  button {
    height: 36px;
    width: 100px;
    border-radius: 4px;
  }
}
