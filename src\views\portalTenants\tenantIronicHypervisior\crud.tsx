import * as api from '/@/api/tenant/opIronicHypervisor';
import { dict, UserPageQuery, AddReq, DelReq, EditReq, compute, CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, h, computed } from 'vue';
import { toOPIronHypervisiorDetail, toOPServerDetail } from '/@/router/intervalRouterTo/tenant'


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	// const addRequest = async ({ form }: AddReq) => {
	//   return await api.createHisecNatServerPolicy(form);
	// };

	let selectedIds = ref([]);

	const onSelectionChange = (changed: any) => {
		selectedIds.value = changed.map((item: any) => item.id);
	};
	const delButtonShowComputed = computed(() => {
		const isShow = auth('tenant:adminIronicHypervisior:MultipleDelete') && selectedIds.value.length > 0;
		return isShow;
	});
	return {
		selectedIds,
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				// addRequest,
				editRequest,
				delRequest,
			},
			form: {
				labelWidth: '120px', //标签宽度
			},
			actionbar: {
				buttons: {
					add: {
						show: false,
						plain: true,
					},
					// selectionsDeleted: {
					// 	text: '删除',
					// 	type: 'danger',
					// 	plain: true,
					// 	show: delButtonShowComputed,
					// 	click: (): void => {
					// 		if (selectedIds.value.length === 0) {
					// 			ElMessage.warning('请先勾选');
					// 			return;
					// 		}
					// 		ElMessageBox.confirm(
					// 			h('p', null, [
					// 				h('span', null, '确定删除 '),
					// 				h('i', { style: 'color: red' }, selectedIds.value.length),
					// 				h('span', null, ' 个记录吗？'),
					// 			]),
					// 			{
					// 				confirmButtonText: '确定',
					// 				cancelButtonText: '取消',
					// 				type: 'warning',
					// 			}
					// 		)
					// 			.then(() => {
					// 				api.DelObjs({ keys: selectedIds.value }).then((response: any) => {
					// 					if (response.code === 2000 && response.msg === '删除成功') {
					// 						ElMessage.success('删除成功');
					// 						// TODO 刷新列表
					// 						crudExpose.doRefresh();
					// 					} else {
					// 						ElMessage.error('删除失败');
					// 					}
					// 				});
					// 			})
					// 			.catch(() => {
					// 				ElMessage({
					// 					type: 'info',
					// 					message: '取消删除',
					// 				});
					// 			});
					// 	},
					// },
				},
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 200,
				dropdown: {
					more: {
						text: '操作',
					},
				},
				buttons: {
					view: {
						link: true,
						type: 'primary',

						show: false,
					},
          edit: {
						link: true,
						type: 'primary',
						show: false,
					},
          // view_detail: {
          //   link: true,
          //   type: 'success',
          //   text: '详情',
          //   show: true,
          //   click: (context): void => {
          //     // 跳转至详情页
          //     toOPIronHypervisiorDetail(context.row.ironic_hyper_id);
          //   }
          // },
          manage_node_power_on: {
						link: true,
						text: '开机',
						type: 'primary',
						show: compute(({ row }) => {
              return row.power_state === "power off";
            }),
						dropdown: true,
						click: (context): void => {
							ElMessageBox.confirm(
								h('p', null, [h('span', null, `确定要将[裸机节点:${context.row.name}]`), h('i', { style: 'color: red' },`【开机】`), h('span', null, '吗？')]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
                  // ElMessage.error('演示操作成功，未完全测试，暂不执行任何操作。');
									api.ManageNodePowerState(context.row.ironic_hyper_id, { target_state: "power on" }).then((response: any) => {
										if (response.code === 2000) {
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消操作',
									});
								});
						},
					},
          manage_node_power_off: {
						link: true,
						text: '关机',
						type: 'primary',
						show: compute(({ row }) => {
              return row.power_state === "power on";
            }),
						dropdown: true,
						click: (context): void => {
							ElMessageBox.confirm(
								h('p', null, [h('span', null, `确定要将[裸机节点:${context.row.name}]`), h('i', { style: 'color: red' },`【当前配置状态:${context.row.provision_state}】【关机】`), h('span', null, '吗？')]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.ManageNodePowerState(context.row.ironic_hyper_id, { target_state: "power off" }).then((response: any) => {
										if (response.code === 2000) {
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消操作',
									});
								});
						},
					},
          manage_node_power_reboot: {
						link: true,
						text: '重启',
						type: 'primary',
						show: compute(({ row }) => {
              return row.power_state === "power on";
            }),
						dropdown: true,
						click: (context): void => {
							ElMessageBox.confirm(
								h('p', null, [h('span', null, `确定要将[裸机节点:${context.row.name}]`), h('i', { style: 'color: red' },`【重启】`), h('span', null, '吗？')]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.ManageNodePowerState(context.row.ironic_hyper_id, { target_state: "rebooting" }).then((response: any) => {
										if (response.code === 2000) {
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消操作',
									});
								});
						},
					},
          set_maintenance: {
						link: true,
						text: '配置维护',
						type: 'primary',
						show: compute(({ row }) => {
              return row.provision_state !== "active";
            }),
						dropdown: true,
						click: (context): void => {
							ElMessageBox.confirm(
								h('p', null, [h('span', null, `确定要将[裸机节点:${context.row.name}]`), h('i', { style: 'color: red' },`【${context.row.is_maintenance === true ? '关闭' : '开启'}维护模式】`), h('span', null, '吗？')]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.SetNodeMaintenance(context.row.ironic_hyper_id, { is_maintenance: !context.row.is_maintenance }).then((response: any) => {
										if (response.code === 2000) {
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消操作',
									});
								});
						},
					},
					manage_state_to_available: {
						link: true,
						text: '移动至Available',
						type: 'primary',
						show: compute(({ row }) => {
              return ["enroll", "manageable"].includes(row.provision_state);
            }),
						dropdown: true,
            click: (context): void => {
							ElMessageBox.confirm(
								h('p', null, [h('span', null, `确定要将[裸机节点: `), h('i', { style: 'color: red' }, context.row.name), h('span', null, ']【移动至Available】吗？')]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.ManageNodeStateTransition(context.row.ironic_hyper_id, { target_state: 'available' }).then((response: any) => {
										if (response.code === 2000) {
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消操作',
									});
								});
						},
					},
          manage_state_to_manageable: {
						link: true,
						text: '移动至Manageable',
						type: 'primary',
						show: compute(({ row }) => {
              return ["enroll", "available"].includes(row.provision_state);
            }),
						dropdown: true,
            click: (context): void => {
							ElMessageBox.confirm(
								h('p', null, [h('span', null, `确定要将[裸机节点: `), h('i', { style: 'color: red' }, context.row.name), h('span', null, ']【移动至Manageable】吗？')]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.ManageNodeStateTransition(context.row.ironic_hyper_id, { target_state: 'manage' }).then((response: any) => {
										if (response.code === 2000) {
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消操作',
									});
								});
						},
					},
          delete_node: {
						link: true,
						text: '删除节点',
						type: 'danger',
						show: compute(({ row }) => {
              return row.provision_state !== "active";
            }),
						dropdown: true,
						click: (context): void => {
							ElMessageBox.confirm(
								h('p', null, [h('span', null, `确定要将[裸机节点:${context.row.name}]`), h('i', { style: 'color: red' },'【删除】'), h('span', null, '吗？')]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.DeleteNode(context.row.ironic_hyper_id).then((response: any) => {
										if (response.code === 2000) {
											ElMessage.success(response.msg);
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error(response.msg);
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消操作',
									});
								});
						},
					},
          // validate_node: {
					// 	link: true,
					// 	text: '检查节点',
					// 	type: 'primary',
					// 	show: compute(({ row }) => {
          //     return row.provision_state !== "active";
          //   }),
					// 	dropdown: true,
					// 	click: (context): void => {
					// 		ElMessageBox.confirm(
					// 			h('p', null, [h('span', null, `确定要将[裸机节点:${context.row.name}]`), h('i', { style: 'color: red' },'【检查】'), h('span', null, '吗？')]),
					// 			{
					// 				confirmButtonText: '确定',
					// 				cancelButtonText: '取消',
					// 				type: 'warning',
					// 			}
					// 		)
					// 			.then(() => {
          //         ElMessage.error('演示操作成功,未完全测试，暂不执行任何操作。');
					// 				// api.SetNodeMaintenance({ is_maintenance: !context.row.is_maintenance }).then((response: any) => {
					// 				// 	if (response.code === 2000) {
					// 				// 		ElMessage.success('操作成功');
					// 				// 		// TODO 刷新列表
					// 				// 		crudExpose.doRefresh();
					// 				// 	} else {
					// 				// 		ElMessage.error('操作失败');
					// 				// 	}
					// 				// });
					// 			})
					// 			.catch(() => {
					// 				ElMessage({
					// 					type: 'info',
					// 					message: '取消操作',
					// 				});
					// 			});
					// 	},
					// },
					remove: {
						link: true,
						type: 'danger',
						show: false,
					},
				},
			},
			pagination: {
				show: true,
			},
			table: {
				rowKey: 'id',
				border: false,
				onSelectionChange,
			},
			columns: {
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
						show: true,
					},
				},
				// $checked: {
				// 	title: '选择',
				// 	form: { show: false },
				// 	column: {
				// 		type: 'selection',
				// 		align: 'left',
				// 		width: '55px',
				// 		// selectable(row, index) {
				// 		//   return row.id !== 1; //设置第一行不允许选择
				// 		// }
				// 	},
				// },
				name: {
					title: '裸机节点名称',
					search: {
						show: true,
					},
					column: {
						minWidth: 200,
            showTitle: "点击查看详情",
            component: {
              on: {
                // 注意：必须要on前缀
                onClick({ row }) {
                  if (row.ironic_hyper_id) {
                    toOPIronHypervisiorDetail(row.ironic_hyper_id);
                  } else {
                    ElMessage.error("裸机节点ID不存在，请联系管理员确认原因！")
                  }
                }
              }
            }
					},
					type: 'link',
					form: {
						show: true,
						rules: [
							// 表单校验规则
							// {required: false, message: '机房必填项'},
						],
						component: {
							placeholder: '请输入裸机节点名称',
						},
					},
				},
				power_state: {
					title: '电源状态',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('openstack:ironicHypervisor:powerState', undefined),
					}),
					form: {
						show: true,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '电源状态必填项' },
						],
						placeholder: '请选择电源状态',
					},
				},
				provision_state: {
					title: '配置状态',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('openstack:ironicHypervisor:provisionState', undefined),
					}),
					form: {
						show: true,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '配置状态必填项' },
						],
						placeholder: '请选择配置状态',
					},
				},
				instance_id: {
					title: '主机',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
            showTitle: "点击查看详情",
            component: {
              on: {
                // 注意：必须要on前缀
                onClick({ row }) {
                  if (row.instance_id) {
                    toOPServerDetail(row.instance_id);
                  }
                }
              }
            }
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/tenants/tenant-op-server/get_list_by_ids/?is_all=true',
						value: 'instance_id',
						label: 'name',
						// cache: true,
					}),
					form: {
						show: true,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '主机必填项' },
						],
						placeholder: '请选择主机',
					},
				},
				driver: {
					title: '驱动',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'input',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							{ required: false, message: '驱动非必填项' },
						],
						component: {
							placeholder: '请输入驱动',
						},
					},
				},
				is_maintenance: {
					title: '维护',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('button_whether_bool', undefined),
					}),
					form: {
						show: true,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '维护状态必填项' },
						],
						placeholder: '请选择维护状态',
					},
				},
        node: {
          title: '区域',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:host:area_node', undefined),
					}),
          editForm: {
            show: false,
          },
          column: {
            minWidth: 90,
          },
          form: {
            show: true,
            rules: [
              // 表单校验规则
              { required: true, message: 'openstack项目区域必填项' },
            ],
            component: {
              placeholder: '请输入openstack项目区域',
            }
          }
        },
			},
		},
	};
};
