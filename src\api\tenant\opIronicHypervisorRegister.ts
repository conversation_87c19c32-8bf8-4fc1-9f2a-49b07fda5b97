import { request } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/tenants/tenant-op-ironic-hypervisor-register/';
export function GetList(query: UserPageQuery) {
  return request({
    url: apiPrefix,
    method: 'get',
    params: query,
  });
}
export function GetObj(id: InfoReq| String) {
  return request({
    url: apiPrefix + id + '/',
    method: 'get',
  });
}
export function GetObj1(id: InfoReq| String) {
  return request({
    url: apiPrefix + id + '/',
    method: 'get',
  });
}
export function AddObj(obj: AddReq) {
  return request({
    url: apiPrefix,
    method: 'post',
    data: obj,
  });
}

export function UpdateObj(obj: EditReq) {
  return request({
    url: apiPrefix + obj.id + '/',
    method: 'put',
    data: obj,
  });
}

export function DelObj(id: string) {
  return request({
    url: apiPrefix + id + '/',
    method: 'delete',
    data: { id },
  });
}

export function DelObjs(obj: any) {
  return request({
    url: apiPrefix + 'multiple_delete/',
    method: 'delete',
    data: obj,
  });
}


export function GetListByIds(query: UserPageQuery) {
  return request({
    url: apiPrefix + 'get_list_by_ids/',
    method: 'get',
    params: query,
  });
}

export function ManageStartRegister(obj: EditReq) {
  return request({
    url: apiPrefix + 'manage_start_register/',
    method: 'post',
    data: obj,
  });
}

// export function ManageNodeStateTransition(obj: EditReq) {
//   return request({
//     url: apiPrefix + obj.id + '/',
//     method: 'post',
//     data: obj,
//   });
// }


