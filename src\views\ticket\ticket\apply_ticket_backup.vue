<template>
  <fs-page>
    <template #header>
      <div class="new-title">
        <!-- <tiny-steps vertical advanced size="large" :data="steps" :active="active" flex @click="stepsClick"></tiny-steps> -->
         <!-- 此页面关闭点击步骤跳转-->
        <tiny-steps vertical advanced size="large" :data="steps" :active="active" flex></tiny-steps>
      </div>
    </template>
  <div id="container-list" class="container-list">

    <div class="contain">
      <div id="account_config" class="create-step-container" v-if="active === 0">
        <div class="container-step-head">
          <span>租户配置</span>
        </div>
        <tiny-form ref="createServerAccountFormValid" :inline="false" label-position="left" label-width="150px" style="border-radius: 0px;" :model="createServerAccountFormData" :rules="createServerAccountFormRules">
          <tiny-form-item prop="is_had_account" label="有无租户">
            <tiny-button-group v-model="createServerAccountFormData
            .is_had_account" style="border-radius: 0px; margin-right: 10px;"
              :data="[{ text: '已有租户', value: '1' }, {text: '无租户', value: '0'}]"></tiny-button-group>
            <span
              style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px;">选择已有租户，则创建此租户下的服务器；若无租户，需要填写新的账号信息，根据此信息创建新的租户。</span>
          </tiny-form-item>
          <tiny-form-item label="选择租户" prop="account_id" v-if="createServerAccountFormData.is_had_account === '1'">
            <SelectAccountForm v-model:current-select-account-id="createServerAccountFormData.account_id"></SelectAccountForm>
          </tiny-form-item>
          <tiny-form-item label="创建租户" v-else>
            <PortalAccountCreate :is-direct-create="createServerAccountFormData.is_had_account" v-model:configed-account-info="createServerAccountFormData.create_account_params"></PortalAccountCreate>
          </tiny-form-item>
        </tiny-form>
        <tiny-divider></tiny-divider>
        <div style="
      display: flex;
      justify-content: center;
      justify-items: center;
      margin: 20px;
      ">
      <tiny-button type="primary" @click="prevStep" :disabled="true">上一步</tiny-button>
      <tiny-button type="primary" @click="active0NextStep">下一步</tiny-button>
      </div>
      </div>
      <div id="basical_config" class="create-step-container" v-if="active === 1">
        <div class="container-step-head">
          <span>基础配置</span>
        </div>
        <tiny-form ref="createServerFormBasicalValid" :model="createServerFormData" :inline="false" label-position="left" label-width="150px" :label-align="true" style="border-radius: 0px" :rules="createServerFormRules">
          <tiny-form-item label="区域" prop="node">
            <tiny-button-group v-model="createServerFormData
            .node" style="border-radius: 0px; margin-right: 10px"
              :data="[{ text: '金华', value: '金华' }]"></tiny-button-group>
            <span
              style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">不同区域的云服务产品之间内网互不相通；请就近选择靠近您业务的区域，可减少网络时延，提高访问速度。</span>
          </tiny-form-item>
          <tiny-form-item label="主机类型" prop="instance_type">
            <tiny-button-group v-model="createServerFormData.instance_type" style="border-radius: 0px; margin-right: 10px"
              :data="[{ text: '裸金属', value: '裸金属' },{ text: '虚拟机', value: '虚拟机', disabled: true }]"></tiny-button-group>
          </tiny-form-item>
          <tiny-form-item label="主机名称" prop="name">
            <tiny-input placeholder="请输入" v-model="createServerFormData.name" style="width: 40%; margin-right: 10px" :maxlength="63" show-word-limit></tiny-input>
          </tiny-form-item>
          <tiny-form-item label="主机描述" prop="description">
            <tiny-input placeholder="请输入" type="textarea" v-model="createServerFormData.description" style="width: 40%; margin-right: 10px" :maxlength="255" show-word-limit></tiny-input>
          </tiny-form-item>
          
          
        </tiny-form>
        <tiny-divider></tiny-divider>
        <div style="
      display: flex;
      justify-content: center;
      justify-items: center;
      margin: 20px;
      ">
      <tiny-button type="primary" @click="prevStep" :disabled="true">上一步</tiny-button>
      <tiny-button type="primary" @click="nextStep">下一步</tiny-button>
      </div>
      </div>
      <div id="host_config" class="create-step-container" v-if="active === 2">
        <div class="container-step-head">
          <span>主机配置</span>
        </div>
        <tiny-form ref="createServerFormHostValid" :model="createServerFormData" :inline="false" label-position="left" label-width="150px" style="border-radius: 0px" :rules="createServerFormRules">
          <tiny-form-item label="选择规格" prop="flavor_id">
            <SelectFlavorForm v-model:current-select-flavor-id="createServerFormData.flavor_id"/>
          </tiny-form-item>
          <tiny-form-item label="选择镜像" prop="image_id">
            <SelectImageForm v-model:current-select-image-id="createServerFormData.image_id"/>
          </tiny-form-item>
        </tiny-form>
        <tiny-divider></tiny-divider>
      </div>
          <div v-if="active === 2">
            <tiny-row style="border-radius: 0px; height: 100%; margin-bottom:10px;">
              <tiny-col :span="8">
                <tiny-row style="border-radius: 0px">
                  <tiny-col :span="5" style="display: flex">
                    <span style="margin-right: 10px">数量</span>
                    <tiny-input type="number" placeholder="请输入" v-model="createServerFormData.count" style="width: 120px; margin-right: 10px" :step="1" :min="1" :max="10"></tiny-input>
                    <span>台</span></tiny-col>
                  <tiny-col :span="7">
                    
                    <!-- <div>
                      <span style="font-size: 12px">配置费用</span>
                      <span style="padding-left: 10px; padding-right: 10px; color: #de504e">¥1.5776</span>
                      <span style="font-size: 12px">/小时</span>
                    </div>
                    <div>
                      <span style="font-size: 12px; border-radius: 0px">参考价格，具体扣费请以账单为准。</span>
                      <span style="font-size: 12px; color: #344899">了解计费详情</span>
                    </div> -->
                  </tiny-col></tiny-row></tiny-col>
              <tiny-col :span="4" style="
                display: flex;
                flex-direction: row-reverse;
                border-radius: 0px;
                height: 100%;
                justify-content: flex-start;
                align-items: center;
              ">
                <tiny-button text="提交申请" type="danger" style="max-width: unset" @click="clickCreate" :reset-time="5000"></tiny-button></tiny-col></tiny-row>
          </div>
    </div>
  </div>
</fs-page>
</template>

<script setup name="portalServerCreate" lang="ts">
import { reactive, ref, watch } from 'vue';
import { 
  TinyDivider,
  Modal as TinyModal,
  TinyForm,
  TinyButton,
  TinyButtonGroup,
  TinyFormItem,
  TinySteps,
} from '@opentiny/vue';
import SelectFlavorForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectFlavorForm.vue';
import SelectProjectForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectProjectForm.vue';
import SelectImageForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectImageForm.vue';
import SelectNetworkForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectNetworkForm.vue';
import SelectSecurityGroupForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectSecurityGroupForm.vue';
import SelectIronicHypervisorForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectIronicHypervisorForm.vue';
import SelectAccountForm from '/@/views/portalTenants/portal/portalAccount/createComp/selectAccountForm.vue';
import PortalAccountCreate from '/@/views/portalTenants/portal/portalAccount/create.vue';
import {createBaremetalServerObj} from '/@/api/tenant/opServer';
import { useRouter } from 'vue-router';
import { message } from '/@/utils/message';


const active = ref(0);


// 路由
const router = useRouter()

let createServerAccountFormData = ref({
  'is_had_account': '1',
  'account_id': '',
  'create_account_params': {},
});

const createServerAccountFormRules = ref({
  is_had_account: [
    { required: true, message:'请选择是否已有租户', trigger: 'blur' }
  ],
  account_id: [
    { required: true, message:'请选择账号', trigger: 'blur' },
  ],
  create_account_params: [{ required: false, message:'请选择账号', trigger: 'blur' }],
});


// 处理 is_had_account 改变
const handleIsHadAccountChange = (value: string) => {
  if (value === '0') {
    createServerAccountFormData.value.account_id = ''; // 清空 account_id
    updateCreateAccountParamsRule(true);
  } else {
    updateCreateAccountParamsRule(false);
  }
};

// 更新 create_account_params 的验证规则
const updateCreateAccountParamsRule = (isRequired: boolean) => {
  if (isRequired) {
    createServerAccountFormRules.value.create_account_params = [{ required: true, message: '请输入创建账号的参数', trigger: 'blur' }];
    createServerAccountFormRules.value.account_id = [
      { required: false, message:'请选择账号', trigger: 'blur' }
    ]
  } else {
    createServerAccountFormRules.value.create_account_params = [];
    createServerAccountFormRules.value.account_id = [
      { required: true, message:'请选择账号', trigger: 'blur' }
    ]
  }
};

// 监听 is_had_account 的变化
watch(() => createServerAccountFormData.value.is_had_account, (newValue) => {
  handleIsHadAccountChange(newValue);
});

let basiclServerFormData = ref({
  name: '',
  description: '',
  instance_type: '裸金属',
  node: '金华'
});

let createServerFormData = ref({
  image_id: '',
  flavor_id: '',
  count: 1,
  name: '',
  description: '',
  instance_type: '裸金属',
  node: '金华'
});



const createServerFormRules = ref({
  image_id: [
    { required: true, message:'请选择镜像', trigger: 'blur' }
  ],
  flavor_id: [
    { required: true, message:'请选择规格', trigger: 'blur' }
  ],
 
  count: [
    {required: true, message:'请输入创建数量', trigger: 'blur'},
    {type: 'number', min:1, max: 10, message:'最小为 1，最大为 10', trigger: 'blur'},
  ],
});

const createBasicalServerFormRules = ref({
  name: [
    {required: true, message:'请输入主机名称', trigger: 'blur'},
    {min:3, max: 63, message:'最小3个字符，最大63个字符', trigger: 'blur'},
  ],
  description: [
    {max: 255, message: '最大255个字符', trigger: 'blur'}
  ],
  node: [
    {required: true, message:'请选择区域', trigger: 'blur'}
  ],
  instance_type: [
    {required: true, message:'请选择主机类型', trigger: 'blur'}
  ],
});

// 点击创建按钮
const clickToServerList = () => {
  router.push(`/tenant/producter_and_services/elasti-computes/baremetal`)
}
// 租户数据验证
const createServerAccountFormValid = ref();


const steps = ref([
  {
    name: '租户配置',
    key: 'account_config',
    status: 'doing',
    active: 0,
  },
  { 
    name: '基础配置',
    key: 'basical_config',
    status: 'doing',
    active: 1,
  },
  {
    name: '主机配置',
    key: 'host_config',
    status: 'done',
    active: 2,
  },
]);

const nextStep = () => {
  active.value += 1
  if (active.value > 2) {
    active.value = 0
  }
}

const active0NextStep = () => {
  createServerAccountFormValid.value.validate((valid: any) => {
    console.error(valid)
    console.error('rules:',createServerAccountFormRules)
    console.error(createServerAccountFormData)
    if (!valid) {
      TinyModal.message({
        message: '创建租户信息有误，请确认?',
        status: 'error',
      });
      return
    }
    else {
      active.value += 1
  if (active.value > 2) {
    active.value = 0
  }
    }
  })
}


const prevStep = () => {
  active.value -= 1
  if (active.value < 0) {
    active.value = 2
  }
}

const scrollToActive = (active_step: number) => {
  active.value = active_step;
};

const scrollToSection = (index: number) => {
  const stepId = steps.value[index]?.key
  const element = document.getElementById(stepId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

const clickCreate = async () => {
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    const response = await createBaremetalServerObj(createServerAccountFormData);
    TinyModal.message({
        message: response.msg,
        status: 'success',
      });
      clickToServerList();
  } catch (error) {
    TinyModal.message({
        message: '创建失败',
        status: 'error',
      });
  }
  
};
</script>
<style scoped lang="less">
.new-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
  padding: 8px;
}

.fixed-header {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-server-steps {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-step-container {
  margin: 20px;
}

.container-step-head {
  font-size: 24px;
  font-weight: bolder;
  margin-bottom: 24px;
}

.content {
  margin-top: 150px;
  /* 确保内容不会被步骤条覆盖 */
  overflow-y: auto;
  /* 允许内容区域垂直滚动 */
}

.section {
  height: 100vh;
  /* 每个部分的高度为视口高度 */
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}
.container-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 20px);
  overflow-x: hidden;
  overflow-y: auto;
}

/* Tiny Vue 组件搜索栏修改圆角 */
:deep(.tiny-input__inner) {
  border-radius: 6px; /* 设置圆角大小 */
}
:deep(.tiny-button) {
  border-radius: 16px; /* 设置圆角大小 */
}

.line {
  height: 1px;
  color: rgb(213, 213, 213);
  background-color: rgb(213, 213, 213);
  border: 0;
}

.contain {
  flex: 1 1 auto;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);

  .contain-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 10px 0 10px;

    hr {
      .line();

      width: 86%;
      margin: 0 20px;
    }

    span {
      color: #1a1818;
      font-size: 16px;
    }
  }

  .contain-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 52px;
  }

  .contain-img {
    position: relative;
    display: flex;
    cursor: pointer;

    img:hover {
      background: whitesmoke;
    }
  }

  .contain-text {
    padding-left: 10px;
    color: #4e5969;
    font-size: 14px;
    cursor: pointer;
  }
}

.bottom-line {
  padding-bottom: 8px;

  hr {
    .line();

    width: 96%;
    margin: 0 auto;
  }
}

.filter-form {
  margin: 10px 0;
}

.col {
  width: 96%;
}

:deep(.tiny-grid) {
  &-header__column,
  &-body__column {
    &.col__selection,
    &.col__radio {
      padding: 0 8px 0 8px;
      & + th,
      + td {
        padding-left: 0;
      }
    }
  }
}

:deep(.tiny-pager) {
  float: right;
}

:deep(.tiny-grid-button__wrapper) {
  width: 100%;
}

:deep(.tiny-form-item__label) {
  color: #494747;
  font-weight: normal;
}

:deep(.tiny-grid-header__column) {
  height: 35px;
  color: rgb(139, 137, 137);
  background-color: #f5f6f7;
}

.operation {
  color: #5e7ce0;
}

.btn {
  display: flex;
  width: 100%;

  .screen {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    text-align: center;
    cursor: pointer;

    span {
      margin-left: 10px;
      color: #4e5969;
      font-size: 14px;
    }
  }
}

.btn > div:last-child {
    margin-left: auto;
}

.tiny-fullscreen {
  background: #fff;
}

.tiny-fullscreen-wrapper {
  min-height: 710px;
  padding: 0 30px;
}

.tiny-fullscreen-scroll {
  overflow-y: auto;
}

.search-btn {
  display: flex;
  button {
    height: 34px;
  }
}

.id-cell {
  display: flex;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}

.status-success {
  fill: #52c41a;
  margin-right: 8px;
}
.status-danger {
  fill: #C7000B;
  margin-right: 8px;
}

.status-warning {
  fill: #FA9841;
  margin-right: 8px;
}
</style>