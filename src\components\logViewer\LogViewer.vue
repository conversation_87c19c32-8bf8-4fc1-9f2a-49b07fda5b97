<script setup>
import { ref, watch, defineProps } from 'vue';
import { ElCard } from 'element-plus';

const props = defineProps({
  logData: {
    type: [Array, String],
    default: () => []
  }
});

// 将 ANSI 字符串转换为带有动态颜色的 HTML 内容
const ansiToHtml = (text) => {
  if (typeof text !== 'string') return '';
  
  const ansiRegex = /\u001b\[([0-9;]+)m(.*?)\u001b\[0m/g;
  const colorMap = {
    '30': 'black', '31': 'red', '32': 'green', '33': 'cyan',
    '34': 'blue', '35': 'magenta', '36': 'cyan', '37': 'white',
    '90': 'gray', '91': 'lightcoral', '92': 'lightgreen', '93': 'lightyellow',
    '94': 'lightblue', '95': 'violet', '96': 'lightcyan', '97': 'white'
  };

  return text
    .replace(ansiRegex, (_, codes, content) => {
      const colorCode = codes.split(';').pop();  // 获取颜色代码
      const color = colorMap[colorCode] || 'inherit';  // 获取对应颜色
      // 返回带有动态样式的HTML
      return `<span class="colored-text" style="color: ${color}">${content}</span>`;
    })
    .replace(/\n/g, '<br>');  // 保留换行符
};

const formattedLogs = ref([]);

watch(() => props.logData, (newLogs) => {
  if (typeof newLogs === 'string') {
    formattedLogs.value = [ansiToHtml(newLogs)];
  } else if (Array.isArray(newLogs)) {
    formattedLogs.value = newLogs.map(log => ansiToHtml(log));
  } else {
    formattedLogs.value = [];
  }
}, { immediate: true });
</script>

<template>
	<div>
		<div v-for="(log, index) in formattedLogs" :key="index" v-html="log" class="log-entry"></div>
	</div>
</template>

<style scoped>
.log-entry {
	margin-bottom: 5px;
	white-space: pre-wrap;
	font-family: monospace;
}

.colored-text {
	display: inline;
}
</style>
