<template>
  <div class="app-container">
    <iframe
      :src="iframeUrl"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
const iframeUrl = ref("http://**********:3001/d/rYdddlPWk/e69c8d-e58aa1-e599a8-e79b91-e68ea7-e58d95-e58fb0?orgId=1&refresh=1m&from=now-1h&to=now&theme=light")

// 根据url query参数，拼接grafana url地址变量
let route = useRoute()
const ip_private = route.query.ip_private
const area = route.query.area
if (area !== undefined) {
  iframeUrl.value += '&var-DS_PROMETHEUS=' + area
}
if (ip_private !== undefined) {
  iframeUrl.value += '&var-node=' + ip_private + ':9100'
}
</script>

<style lang="scss" scoped>
/** 关闭tag标签  */
.app-container {
  /* 50px = navbar = 50px */
  height: calc(100vh - 50px);
}

/** 开启tag标签  */
.hasTagsView {
  .app-container {
    /* 84px = navbar + tags-view = 50px + 34px */
    height: calc(100vh - 84px);
  }
}

iframe {
  width: 100%;
  height: 100%;
}
</style>