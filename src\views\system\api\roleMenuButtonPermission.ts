import { request } from "/@/utils/service";

/**
 * 获取角色的授权列表
 * @param roleId
 * @param query
 */
export function getRolePremission(query: object) {
  return request({
    url: '/api/system/role_menu_button_permission/get_role_premission/',
    method: 'get',
    params:query
  })
}

/***
 * 设置角色的权限
 * @param roleId
 * @param data
 */
export function setRolePermission(roleId: any, data: object) {
  return request({
    url: `/api/system/role_menu_button_permission/${roleId}/set_role_permission/`,
    method: 'put',
    data
  })
}

export function getDataPermissionRange() {
  return request({
    url: '/api/system/role_menu_button_permission/data_scope/',
    method: 'get',
  })
}
export function getDataPermissionDept() {
  return request({
    url: '/api/system/role_menu_button_permission/role_to_dept_all/',
    method: 'get'
  })
}

export function getDataPermissionMenu() {
  return request({
    url: '/api/system/role_menu_button_permission/get_role_permissions/',
    method: 'get'
  })
}

/**
 * 设置按钮的数据范围
 */
export function setBtnDatarange(roleId: number,data: object) {
  return request({
    url: `/api/system/role_menu_button_permission/${roleId}/set_btn_datarange/`,
    method: 'put',
    data
  })
}



export interface DataPermissionRangeType {
  label: string;
  value: number;
}

export interface CustomDataPermissionDeptType {
  id: number;
  name: string;
  patent: number;
  children: CustomDataPermissionDeptType[]
}

export interface CustomDataPermissionMenuType {
  id: number;
  name: string;
  is_catalog: boolean;
  menuPermission: { id: number; name: string; value: string }[] | null;
  columns: { id: number; name: string; title: string }[] | null;
  children: CustomDataPermissionMenuType[]
}

export interface MenusType{
  id: string;
  name: string;
  isCheck: boolean;
  radio: string;
  btns: { id:number, value: string; isCheck: boolean; data_range: number; dept:object; name:string }[];
  columns: { [key: string]: boolean | string; }[]
}

export interface MenuDataType {
  id: string;
  name: string;
  menus:MenusType[];
}


