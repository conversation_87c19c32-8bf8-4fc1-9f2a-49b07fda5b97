
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/image";

class ImageAPI {
  /**
   * 获取镜像分页列表
   *
   * @param queryParams 查询参数
   * @returns 镜像分页结果
   */
  static getPage(queryParams: ImagePageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取镜像表单数据
   *
   * @param id 镜像ID
   * @returns 镜像表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }



  /**
   * 获取镜像列表
   *
   * @returns 镜像列表
   */
  static getList() {
    return request({
      url: `${BASE_URL}/list`,
      method: "get",
    });
  }

  /**
   * 获取镜像的数据项
   *
   * @param typeCode 镜像编码
   * @returns 镜像数据项
   */
  static getOptions(code: string) {
    return request({
      url: `${BASE_URL}/${code}/options`,
      method: "get",
    });
  }
}

export default ImageAPI;

/**
 * 镜像查询参数
 */
export interface ImagePageQuery extends PageQuery {
  search?: string;    // keywords
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}


/**
 * 镜像分页对象
 */
export interface ImagePageVO {
  id: number;
  name: string;
  image_id: string;
  size: string;
}

export interface ImageForm {
  image_id?: number;
  name?: string;
  visibility?: number;
  size?: number;
  status?: string;
  min_ram?: number;
  min_disk?: number;
  image_type?: string;
  disk_format?: string;
  protected?: boolean;
  project_id?: string;
  project_name?: string;
  owner_id?: string;
  owner_name?: string;
}
