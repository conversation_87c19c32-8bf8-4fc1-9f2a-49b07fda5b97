<template>
  <div class="map-container" style="width: 100%; height: 100%" ref="mapContainer"></div>
</template>
 
<script lang="ts" setup name="ChartChinaMap">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as echarts from 'echarts';
import chinaAreaData from '/@/utils/geoAtlasJson/chinaArea.json'; 
 

const props = defineProps({
    theme: {
      type: String,
      default: '',
    },
    mapData: {
      type: Array as () => Array<{name: string, value: number}>,
      required: true,
      validator: (data: Array<{name: string, value: number}>) => {
        return data.every(item  => item.name  && typeof item.value  === 'number');
      }
    },
    mapTitle: {
      type: String,
      default: 'Map Title',
      required: false,
    },
    subTitle: {
      type: String,
      default: 'Sub Title',
      required: false,
    },
    subLink: {
      type: String,
      default: '',
      required: false,
    },
  });

    const mapContainer = ref<HTMLElement | null>(null);
    let chartInstance: echarts.ECharts | null = null;
 
    // 计算合适的最大值 
    const calculateMaxValue = (data: Array<{value: number}>) => {
      const max = Math.max(...data.map(item  => item.value)); 
      return max <= 0 ? 100 : max; // 防止所有值为0的情况 
    };
 
    const initChart = () => {
      if (!mapContainer.value)  return;
 
      // 销毁现有实例 
      if (chartInstance) {
        chartInstance.dispose(); 
      }
 
      // 初始化图表 
      chartInstance = echarts.init(mapContainer.value,  props.theme); 
      echarts.registerMap('china',  chinaAreaData);
 
      // 处理数据确保有效性 
      const validData = props.mapData.filter(item  => item.value  >= 0);
      const maxValue = calculateMaxValue(validData);
 
      // 配置选项 
      const option = {
        title: {
          text: props.mapTitle, 
          subtext: props.subTitle, 
          sublink: props.subLink, 
          left: 'left',
          top: 'left',
          textStyle: {
            fontSize: 16,
            "margin-bottom": 20,
            fontWeight: 'bolder'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            if (!params.data)  return `${params.name}<br/> 数量: 0`;
            return `${params.data.name}<br/> 数量: ${params.data.value}`; 
          },
          backgroundColor: 'rgba(50,50,50,0.7)',
          borderColor: '#333',
          textStyle: {
            color: '#fff'
          }
        },
        toolbox: {
          show: true,
          orient: 'horizontal',
          left: 'right',
          top: 'top',
          feature: {
            dataView: {
              readOnly: false,
              optionToContent: (opt: any) => {
                const data = opt.series[0].data; 
                let html = '<div style="padding:10px;"><table style="width:100%;">';
                data.forEach((item:  any) => {
                  html += `<tr>
                    <td>${item.name}</td> 
                    <td style="text-align:right">${item.value}</td> 
                  </tr>`;
                });
                html += '</table></div>';
                return html;
              }
            },
            saveAsImage: {
              type: 'png',
              name: props.mapTitle  
            }
          }
        },
        visualMap: {
          min: 0,
          max: maxValue,
          text: ['高', '低'],
          realtime: false,
          calculable: true,
          inRange: {
            color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
          },
          textStyle: {
            color: props.theme  === 'dark' ? '#fff' : '#333'
          },
          bottom: '5%',
          left: '5%'
        },
        geo: {
          map: 'china',
          roam: false,
          center: [105, 36],
          zoom: 1.2,
          label: {
            show: true,
            fontSize: 10,
            color: props.theme  === 'dark' ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.7)',
            emphasis: {
              show: false 
            }
          },
          itemStyle: {
            areaColor: props.theme  === 'dark' ? '#2B323B' : '#e6f4ff',
            borderColor: props.theme  === 'dark' ? '#404a59' : '#bae0ff',
            borderWidth: 1 
          },
          emphasis: {
            itemStyle: {
              areaColor: props.theme  === 'dark' ? '#184cff' : '#1677ff'
            }
          }
        },
        series: [
          {
            name: '客户分布',
            type: 'map',
            map: 'china',
            geoIndex: 0,
            data: validData,
            label: {
              show: true,
              formatter: (params: any) => {
                return params.name; 
              }
            },
            itemStyle: {
              borderColor: props.theme  === 'dark' ? '#389dff' : '#1677ff',
              borderWidth: 0.5 
            },
            emphasis: {
              label: {
                show: false 
              },
              itemStyle: {
                areaColor: props.theme  === 'dark' ? '#17008d' : '#0958d9'
              }
            }
          }
        ]
      };
 
      chartInstance.setOption(option); 
      
      // 标记为ECharts实例，便于导出组件识别 
      if (mapContainer.value)  {
        (mapContainer.value  as any).__echarts__ = chartInstance;
      }
    };
 
    const resizeChart = () => {
      chartInstance?.resize();
    };
 
    // 初始化 
    onMounted(() => {
      initChart();
      window.addEventListener('resize',  resizeChart);
    });
 
    // 清理 
    onBeforeUnmount(() => {
      chartInstance?.dispose();
      window.removeEventListener('resize',  resizeChart);
    });
 
    // 监听变化 
    watch([() => props.theme,  () => props.mapData],  () => {
      initChart();
    }, { deep: true });
 

</script>
<style scoped>
.map-container {
  width: calc(100% - 32px); /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important; /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>