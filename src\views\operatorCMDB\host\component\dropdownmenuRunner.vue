<template>
<el-dropdown>
      <el-button type="primary" :plain="true">
        执行作业<el-icon class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu :split-button="true">
          <el-dropdown-item @click="handleNotice('ping')">测试连接性</el-dropdown-item>
          <el-dropdown-item @click="handleNotice('ipmi_power_on')" :divided="true">开机</el-dropdown-item>
          <el-dropdown-item @click="handleNotice('ipmi_power_shutdown')">关机</el-dropdown-item>
          <el-dropdown-item @click="handleNotice('ipmi_power_off')">强制关机</el-dropdown-item>
          <el-dropdown-item @click="handleNotice('ipmi_power_reset')">重启</el-dropdown-item>
          <el-dropdown-item @click="handleNotice('install_node_exporter')" :divided="true">安装监控</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
</template>


<script lang="ts" setup name="dropdownmenuRunner">
import { onMounted } from 'vue'
import { ElMessage } from 'element-plus';
import * as api from '/@/api/operatorCMDB/host';



const props = defineProps({
  selectedIds: {
    type: Array as () => string[],
    default: () => [],
  },
  selectedHosts: {
    type: Array,
    default: () => [],
  },
})


const handleNotice = (code: string) => {
  if (props.selectedIds.length <= 0) {
    ElMessage.error('未选中主机')
  } else {
    const data = {
        "job_code": code,
        "hosts": props.selectedHosts,
        "params": ""
    }
    api.RemoteExecJob(data).then(
      (response: any)=> {
        if (response.code === 200 && response.msg === '成功') {
          ElMessage.success('发起作业成功')
        } else {
          ElMessage.success('发起作业失败')
        }
      }
    )
  }
}



onMounted(() => {
})
</script>

<style scoped>
.example-showcase .el-dropdown + .el-dropdown {
  margin-left: 15px;
}
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
</style>


