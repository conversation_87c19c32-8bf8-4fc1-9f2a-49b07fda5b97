import * as api from '/@/api/notice/noticeLog';
import {
  dict,
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  // compute,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import { dictionary } from '/@/utils/dictionary';
import { auth } from "/@/utils/authFunction";
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, h, computed } from 'vue';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  let selectedIds = ref([]);

  const onSelectionChange = (changed: any) => {
    selectedIds.value = changed.map((item: any) => item.id);
  };
  const delButtonShowComputed = computed(() => {
    const isShow = auth('notice:noticeLog:MultipleDelete') && selectedIds.value.length > 0
    return isShow;
  });
  return {
    selectedIds,
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      form: {
        labelWidth: "120px", //标签宽度
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('notice:noticeLog:Create'),
            plain: true,
          },
          selectionsDeleted: {
            text: '删除',
            type: 'danger',
            plain: true,
            show: delButtonShowComputed,
            click: (): void => {
              if (selectedIds.value.length === 0) {
                ElMessage.warning('请先勾选')
                return
              }
              ElMessageBox.confirm(
                h('p', null, [
                  h('span', null, '确定删除 '),
                  h('i', { style: 'color: red' }, selectedIds.value.length),
                  h('span', null, ' 个记录吗？'),
                ]),
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning',
                }
              )
                .then(() => {
                  api.DelObjs({ 'keys': selectedIds.value }).then(
                    (response: any) => {
                      if (response.code === 2000 && response.msg === '删除成功') {
                        ElMessage.success('删除成功')
                        // TODO 刷新列表
                        crudExpose.doRefresh()
                      } else {
                        ElMessage.error('删除失败')
                      }
                    }
                  )
                })
                .catch(() => {
                  ElMessage({
                    type: 'info',
                    message: '取消删除',
                  })
                })
            },
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            link: true,
            type: 'primary',
            show: auth('notice:noticeLog:Retrieve'),
          },
          edit: {
            link: true,
            type: 'primary',
            show: auth('notice:noticeLog:Update'),
          },
          remove: {
            link: true,
            type: 'danger',
            show: auth('notice:noticeLog:Delete'),
          },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
        border: false,
        onSelectionChange,
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            show: false,
          },
        },
        $checked: {
          title: "选择",
          form: { show: false },
          column: {
            type: "selection",
            align: "left",
            width: "55px",
            // selectable(row, index) {
            //   return row.id !== 1; //设置第一行不允许选择
            // }
          }
        },
        resource_id: {
          title: '资源ID',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '资源ID必填项' },
            ],
            component: {
              placeholder: '请输入资源ID',
            }
          }
        },
        message: {
          title: '消息内容',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '消息内容必填项' },
            ],
            component: {
              placeholder: '请输入消息内容',
            }
          }
        },
        is_success: {
          title: '成功',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'dict-radio',
          dict: dict({
            data: dictionary('button_whether_bool', undefined),
          }),
          column: {
            minWidth: 90,
          },

          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '是否成功必填项' },
            ],
            component: {
              placeholder: '请输入是否成功',
            }
          }
        },
        type: {
          title: '类型',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '类型必填项' },
            ],
            component: {
              placeholder: '请输入类型',
            }
          }
        },
      },
    },
  };
};
