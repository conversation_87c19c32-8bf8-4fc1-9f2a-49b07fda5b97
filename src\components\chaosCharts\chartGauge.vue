<template>
  <tiny-card custom-class="stat-cards" type="text">
    <tiny-chart-gauge :options="options" :extend="extChart" :toolbox="toolbox"></tiny-chart-gauge>
  </tiny-card>
  
</template>
 
<script setup lang="ts" name="ChartGauge">
import { PropType, ref, watch } from 'vue';
import { TinyCard, TinyChartGauge } from '@opentiny/vue';


const props = defineProps({
  data: {
    type: Number,
    required: true,
    default: 0
  },
  config: {
    type: Object as PropType<{
      title: string;
      subTitle: string;
      name: string;
    }>,
    required: true,
    default() {
      return {
        title: '',
    subTitle: '',
    name: '',
      }
    },
  }
});

const extChart = ref({
  title: {
          text: props.config.title, 
          subtext: props.config.subTitle, 
          // sublink: 'subLink', 
          left: 'left',
          top: 'left',
          textStyle: {
            "margin-bottom": 20,
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
});

const toolbox = ref({
  feature: {
    dataView: {},
    saveAsImage: {},
  }
})

const options = ref({
  // gradientColor 表示从仪表盘左到右的渐变色
  // gradientColor.length == 1 时也可以表示单色
  // 注意：splitColor 的优先级高于 gradientColor
  // gradientColor: ['#00bfc9', '#a9e4a9'],
  theme: 'cloud-light',
  text: {
    formatterStyle: {
      value: {
        fontSize: 16,
        fontWeight: 'bolder',
      },
      name: {
        fontSize: 16,
        fontWeighht: 'bolder',
      }
    },
  },
  splitColor: [
    [0.25, '#0d9458'],
    [0.5, '#eeba18'],
    [0.75, '#ec6f1a'],
    [1, '#f43146']
  ],
  pointer: true,
  data: [
    {
      value: props.data,
      name: props.config.name
    }
  ]
})


// Watch for props changes and update chart options 
watch(() => props.data, () => {
  options.value.data[0].value = props.data;
}, { deep: false });

// Watch for props changes and update chart options 
watch(() => props.config, () => {
  options.value.data[0].name = props.config.name;
}, { deep: true });
</script>

<style scoped>
.dashboard-sub-title {
  font-size: 16px;
  margin-bottom: 20px;
}
/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px); /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important; /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>