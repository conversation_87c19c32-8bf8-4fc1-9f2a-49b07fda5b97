<template>
  <tiny-card custom-class="stat-cards" type="text">
    <tiny-chart-pie :options="options" :extend="extChart" :toolbox="toolbox"></tiny-chart-pie>
  </tiny-card>
</template>

<script lang="ts" setup name="Chart<PERSON>ie">
import { PropType, ref, watch, } from 'vue';
import { TinyChartPie, TinyCard, } from '@opentiny/vue';

const props = defineProps({
  data: {
    type: Array<any>,
    required: true,
    default() {
      return []
    }
  },
  config: {
    type: Object as PropType<{
      title: string;
      subTitle: string;
    }>,
    required: true,
    default() {
      return {
        title: '',
        subTitle: '',
      }
    },
  }
});


const extChart = ref({
  title: {
    text: props.config.title,
    subtext: props.config.subTitle,
    // sublink: 'subLink', 
    left: 'left',
    top: 'left',
    textStyle: {
      "margin-bottom": 20,
      fontSize: 16,
      fontWeight: 'bold'
    }
  }
});

const toolbox = ref({
  feature: {
    dataView: {},
    // magicType: { type: ['pie', 'cicle'] },
    saveAsImage: {}
  }
})

let options: any = ref({
  type: 'pie',
  position: {
    center: ['50%', '50%']
  },
  legend: {
    show: true,
    // 可滚动的图例，生成切换按钮
    type: 'scroll',

    // 切换按钮在图例的位置
    pageButtonPosition: 'end',
    position: {
      right: '10%',
      top: 'center'
    },
    orient: 'vertical'
  },
  label: {
    show: true,
    type: 'percent',
    line: true
  },
  data: props.data
})


// Watch for props changes and update chart options 
watch(() => props.data, () => {
  options.value.data = props.data
}, { deep: true });

// Watch for props changes and update chart options 
watch(() => props.config, () => {
  extChart.value.title.text = props.config.title;
  extChart.value.title.subtext = props.config.subTitle;
}, { deep: true });
</script>
<style scoped>
.dashboard-sub-title {
  font-size: 16px;
  margin-bottom: 20px;
}

/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px);
  /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important;
  /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>