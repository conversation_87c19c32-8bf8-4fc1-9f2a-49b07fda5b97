import * as api from '/@/api/tenant/hisecEngineNatServerPolicy';
import { dict, UserPageQuery, AddReq, DelReq, EditReq, CreateCrudOptionsProps, CreateCrudOptionsRet, useCompute } from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, h, computed } from 'vue';

const { asyncCompute } = useCompute();

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.createHisecNatServerPolicy(form);
	};

	let selectedIds = ref([]);

	const onSelectionChange = (changed: any) => {
		selectedIds.value = changed.map((item: any) => item.id);
	};
	const delButtonShowComputed = computed(() => {
		const isShow = auth('tenant:adminTenantNATServerPolicy:MultipleDelete') && selectedIds.value.length > 0;
		return isShow;
	});

	const dynamticHosts = dict({
		url: '/api/tenants/tenant-op-server/get_list_by_ids/?is_all=true',
		value: 'instance_id',
		label: 'name',
		immediate: false,
	});

	const dynamticPublicIPV4 = dict({
		url: '/api/tenants/tenant-hisec-public-ip/get_list_by_ids/?is_all=True&used=False',
		value: 'public_ip',
		label: 'public_ip',
		immediate: false,
	});
	return {
		selectedIds,
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			form: {
				labelWidth: '120px', //标签宽度
			},
			actionbar: {
				buttons: {
					add: {
						show: auth('tenant:adminTenantNATServerPolicy:Create'),
						plain: true,
					},
					selectionsDeleted: {
						text: '删除',
						type: 'danger',
						plain: true,
						show: delButtonShowComputed,
						click: (): void => {
							if (selectedIds.value.length === 0) {
								ElMessage.warning('请先勾选');
								return;
							}
							ElMessageBox.confirm(
								h('p', null, [
									h('span', null, '确定删除 '),
									h('i', { style: 'color: red' }, selectedIds.value.length),
									h('span', null, ' 个记录吗？'),
								]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.DelObjs({ keys: selectedIds.value }).then((response: any) => {
										if (response.code === 2000 && response.msg === '删除成功') {
											ElMessage.success('删除成功');
											// TODO 刷新列表
											crudExpose.doRefresh();
										} else {
											ElMessage.error('删除失败');
										}
									});
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消删除',
									});
								});
						},
					},
				},
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 200,
				buttons: {
					view: {
						link: true,
						type: 'primary',
						show: auth('tenant:adminTenantNATServerPolicy:Retrieve'),
					},
					edit: {
						link: true,
						type: 'primary',
						show: false,
					},
					remove: {
						link: true,
						type: 'danger',
						show: auth('tenant:adminTenantNATServerPolicy:Delete'),
					},
				},
			},
			pagination: {
				show: true,
			},
			table: {
				rowKey: 'id',
				border: false,
				onSelectionChange,
			},
			columns: {
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
						show: false,
					},
				},
				$checked: {
					title: '选择',
					form: { show: false },
					column: {
						type: 'selection',
						align: 'left',
						width: '55px',
						// selectable(row, index) {
						//   return row.id !== 1; //设置第一行不允许选择
						// }
					},
				},
				name: {
					title: '策略名称',
					search: {
						show: true,
					},
					column: {
						minWidth: 200,
					},
					type: 'input',
					form: {
						show: false,
						rules: [
							// 表单校验规则
							// {required: false, message: '机房必填项'},
						],
						component: {
							placeholder: '请输入策略名称',
						},
					},
				},
				node: {
					title: '区域',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'dict-select',
					dict: dict({
						url: '/api/tenants/tenant-op-setting/get_op_all_nodes/',
						immediate: false,
					}),
					editForm: {
						show: false,
					},
					column: {
						minWidth: 90,
					},
					form: {
						show: true,
						rules: [
							// 表单校验规则
							{ required: true, message: 'openstack项目区域必填项' },
						],
						valueChange({ form }) {
							dynamticHosts.url = form.node
								? `/api/tenants/tenant-op-server/get_list_by_ids/?is_all=true&node=${form.node}`
								: '/api/tenants/tenant-op-server/get_list_by_ids/?is_all=true';
							dynamticHosts.reloadDict();

							dynamticPublicIPV4.url = form.node
								? `/api/tenants/tenant-hisec-public-ip/get_list_by_ids/?is_all=True&used=False&node=${form.node}`
								: '/api/tenants/tenant-hisec-public-ip/get_list_by_ids/?is_all=True&used=False';
							dynamticPublicIPV4.reloadDict();
						},
						helper: {
							render() {
								return <div style={'color:red'}>提示：可先选择配置的区域，对应的【主机和公网IP】会自动查询为对应区域节点的资源信息。</div>;
							},
						},
						component: {
							placeholder: '请输入openstack项目区域',
						},
					},
				},
				op_server: {
					title: '主机',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'dict-select',
					dict: dynamticHosts,
					form: {
						show: true,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
						},
						helper: {
							render() {
								return (
									<div style={'color:red'}>提示：为保证设备顺利转发NAT业务，需要配置安全策略。 修改服务器映射可能会导致业务中断，请谨慎修改。</div>
								);
							},
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '主机必填项' },
						],
						placeholder: '请选择主机',
					},
				},
				public_ip: {
					title: '公网IP',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'dict-select',
					dict: dynamticPublicIPV4,
					form: {
						show: true,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
						},
						rules: [
							// 表单校验规则
							{ required: true, message: '公网IP必填项' },
						],
						placeholder: '请选择公网IP',
					},
				},
				public_port: {
					title: '公网端口',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'number',
					column: {
						minWidth: 90,
					},
					form: {
						helper: {
							render() {
								return <div style={'color:#E6A23C'}>指定端口时会自动删除【全部映射默认规则】，可能会导致网络中断，请谨慎操作！</div>;
							},
						},
						rules: [
							// 表单校验规则
							{ required: false, message: '端口号非必填项' },
						],
						component: {
							placeholder: '请输入公网端口号 1~65535',
							min: 1,
							max: 65535,
						},
					},
				},
				inside_ip: {
					title: '内网IP',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'input',
					form: {
						show: false,
						component: {
							placeholder: '请选择内网IP',
						},
					},
				},
				inside_port: {
					title: '内网端口',
					search: {
						show: true,
					},
					// treeNode: true,
					type: 'number',
					column: {
						minWidth: 90,
					},
					form: {
						helper: {
							// position: "label",
							// tooltip: {
							//   placement: "top-start"
							// },
							render() {
								return <div style={'color:#E6A23C'}>公网端口存在时，必须填写内网端口，请根据实际情况填写。</div>;
							},
						},
						rules: [
							// 表单校验规则
							{ required: false, message: '端口号非必填项' },
						],
						component: {
							placeholder: '请输入内网端口号 1~65535',
							min: 1,
							max: 65535,
						},
					},
				},
				project_id: {
					title: '项目',
					search: {
						show: false,
					},
					column: {
						minWidth: 60,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/tenants/tenant-project/get_list_by_ids/?is_all=True',
						value: 'project_id',
						label: 'name',
						// cache: true,
					}),
					form: {
						show: false,
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							rules: [
								// 表单校验规则
								// {required: false, message: '机房必填项'},
							],
							placeholder: '请选择所属项目',
						},
					},
				},
				protocol: {
					title: '协议',
					search: {
						show: false,
					},
					column: {
						minWidth: 90,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('natserver:protocol', undefined),
					}),
					form: {
						show: false,
						rules: [
							// 表单校验规则
							{ required: false, message: '是否使用中必填项' },
						],
						component: {
							placeholder: '请选择是否使用中',
						},
					},
				},
				description: {
					title: '描述',
					search: {
						show: false,
					},
					type: 'textarea',
					column: {
						minWidth: 90,
					},
					form: {
						rules: [
							// 表单校验规则
							// {required: true, message: '描述非必填项'},
						],
						component: {
							placeholder: '请输入描述',
						},
					},
				},
			},
		},
	};
};
