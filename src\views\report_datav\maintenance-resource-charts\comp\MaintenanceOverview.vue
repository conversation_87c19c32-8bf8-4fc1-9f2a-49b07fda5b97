<template>
  <el-card>
    <!-- 左侧资产概述 -->
    <el-row :gutter="50">
      <el-col :span="12">
        <AssetOverview :data="assetOverviewData" />
      </el-col>
    
    <!-- 右侧待办事项 -->
      <el-col :span="12">
        <TodoList :data="todoData" @row-click="handleRowClick" />
      </el-col>
    </el-row>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router'
import AssetOverview from './AssetOverview.vue';
import TodoList from './TodoList.vue';

const router = useRouter()
interface MaintenanceData {
  id: string;
  operatorcmdb_host: string;
  issue_type: string;
  issue_status: string;
  customer: string;
  create_datetime: string;
}

interface AssetOverviewData {
  total_maintenance: number;
  resolved_count: number;
  resolved_rate: number;
  reported_count: number;
  reported_rate: number;
  in_progress_count: number;
  in_progress_rate: number;
}

interface Props {
  data: MaintenanceData[];
}

const props = defineProps<Props>();

// 计算资产概述数据
const assetOverviewData = computed<AssetOverviewData>(() => {
  const total = props.data.length;
  const resolved = props.data.filter(item => item.issue_status === '已解决').length;
  const reported = props.data.filter(item => item.issue_status === '已上报').length;
  const inProgress = props.data.filter(item => item.issue_status === '进行中').length;

  return {
    total_maintenance: total,
    resolved_count: resolved,
    resolved_rate: total > 0 ? Number(((resolved / total) * 100).toFixed(2)) : 0,
    reported_count: reported,
    reported_rate: total > 0 ? Number(((reported / total) * 100).toFixed(2)) : 0,
    in_progress_count: inProgress,
    in_progress_rate: total > 0 ? Number(((inProgress / total) * 100).toFixed(2)) : 0,
  };
});

// 计算待办事项数据（已上报和进行中，按时间倒序）
const todoData = computed(() => {
  return props.data
    .filter(item => ['已上报', '进行中'].includes(item.issue_status))
    .sort((a, b) => new Date(b.create_datetime).getTime() - new Date(a.create_datetime).getTime());
});

// 行点击事件
const handleRowClick = (row: MaintenanceData) => {
  router.replace({
    path: '/operatorCMDB/hostMaintenance',
    query: {
      id: row.id
    }
  })
};
</script>

<style scoped>
</style> 