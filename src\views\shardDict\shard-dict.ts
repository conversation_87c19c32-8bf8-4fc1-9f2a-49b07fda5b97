import { dict } from "@fast-crud/fast-crud";
import { getModelList } from "/@/api/models/search"

export const modelsDataDict = dict({
  value: "value",
  label: "label",
  async getData() {
    const apiRes = await getModelList({app_name: 'resource'});
    const models = await apiRes.data.models.map((item: {
      [x: string]: string; label: string; app: string; key: string; 
      }) => {
      item.value = item.app + '.' + item.key
      item.label = item.title
      return item
    })
    return models
  }
});
