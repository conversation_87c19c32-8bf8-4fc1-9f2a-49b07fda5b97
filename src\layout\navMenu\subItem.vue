<template>
  <!-- <el-sub-menu :key="showParentMenuName" :disabled="true">
    <template #title>
      <span class="parentMenu">{{ showParentMenuName }}</span>
    </template>
  </el-sub-menu> -->
  <div class="div-marigin" v-if="props.isCollapse && props.parentMenuName !== 'unknown'">
    <span class="parentMenu">{{ showParentMenuName }}</span>
  </div>
	<template v-for="val in chils">
		<el-sub-menu :index="val.path" :key="val.path" v-if="val.children && val.children.length > 0">
			<template #title>
				<SvgIcon :name="val.meta.icon" />
				<span>{{ $t(val.meta.title) }}</span>
			</template>
			<sub-item :chil="val.children" />
		</el-sub-menu>
		<template v-else>
			<el-menu-item :index="val.path" :key="val.path">
				<template v-if="!val.meta.isLink || (val.meta.isLink && val.meta.isIframe)">
					<SvgIcon :name="val.meta.icon" />
					<span>{{ $t(val.meta.title) }}</span>
				</template>
				<template v-else>
					<a class="w100" @click.prevent="onALinkClick(val)">
						<SvgIcon :name="val.meta.icon" />
						{{ $t(val.meta.title) }}
					</a>
				</template>
			</el-menu-item>
		</template>
	</template>
</template>

<script setup lang="ts" name="navMenuSubItem">
import { computed } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import other from '/@/utils/other';


// 定义父组件传过来的值
const props = defineProps({
	// 菜单列表
	chil: {
		type: Array<RouteRecordRaw>,
		default: () => [],
	},
  parentMenuName: {
    type: String,
    default: 'unknown',
  },
  isCollapse: {
    type: Boolean,
    default: true,
  },
});

const showParentMenuName = computed(() => {
  return props.parentMenuName
})
// 获取父级菜单数据
const chils = computed(() => {
	return <RouteItems>props.chil;
});
// 打开外部链接
const onALinkClick = (val: RouteItem) => {
	other.handleOpenLink(val);
};
</script>

<style scoped>
.div-marigin {
  margin-left: 5px;
  margin-bottom: 10px;
  margin-top: 10px;
}
.parentMenu {
  font-size: large;
  font-weight: bolder;
  padding: 10px;
}
</style>
