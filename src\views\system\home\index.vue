<template>
  <fs-page>
    <h1 class="page-title">概览</h1>
    <div class="overview-container">
      <UsageOverview 
        :quota="projectQuotaSetUsageData.quota_set" 
        :usage="projectQuotaSetUsageData.usage" 
      />
      <ImportantNotices />
    </div>
    <div class="my-resource-overview-container">
      <MyResources :stats="portalServerStatiscalData" />
    </div>
    <!-- <div>
      <PortalServerIndex />
    </div> -->
  </fs-page>
</template>
 
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { GetTenantPortalDashboard } from '/@/api/tenant/account'
import UsageOverview from '/@/views/system/home/<USER>/UsageOverview.vue'; 
import ImportantNotices from '/@/views/system/home/<USER>/ImportantNotices.vue'; 
import MyResources from '/@/views/system/home/<USER>/MyResources.vue'; 
// import PortalServerIndex from '/@/views/portalTenants/portal/portalServer/index.vue';
 
const portalServerStatiscalData = ref({
  total: 0,
  running: 0,
  coming_expire: 0,
  coming_created: 0,
  had_expired: 0,
  gpu_counts: 0,
})
 
const projectQuotaSetUsageData = ref({
  quota_set: { cores: 0, instances: 0, ram: 0 },
  usage: { cores: 0, instances: 0, ram: 0 }
})
 
onMounted(async () => {
  try {
    const { data } = await GetTenantPortalDashboard()
    portalServerStatiscalData.value  = data.server_statical  
    projectQuotaSetUsageData.value  = data.project_quota_set_and_usage  
  } catch (error) {
    console.error(' 数据加载失败:', error)
  }
})
</script>
 
<style scoped>
.page-title {
  font-size: 24px;
  font-weight: bolder;
  margin: 0 0 12px 12px;
}
 
.overview-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 8px;
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
}
 
.my-resource-overview-container {
  display: flex;
  gap: 16px;
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  box-sizing: border-box;
}
/* 深度样式穿透 */
:deep(.tiny-card__content) {
  min-width: 0;
  display: flex;
  flex-direction: column;
}

:deep(.tiny-progress__text) {
  font-size: 14px !important;
}
</style>