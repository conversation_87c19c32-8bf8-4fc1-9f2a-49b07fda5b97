<!-- XTermLogViewer.vue  -->
<template>
	<div ref="xtermContainer" class="terminal"></div>
</template>
 
<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount, computed, PropType } from 'vue';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';

// 定义 props 并设置默认值
const props = defineProps({
	logData: {
		type: Object,
		default: () => ({
			msg: 'default value...',
		}),
	},
	closeTerminal: {
		type: Boolean,
		default: false,
	},
	fontSize: {
		type: String,
		default: 14,
  },
  isAnsiColorFixed: {
		type: Boolean,
		default: false,
  },
	theme: {
		// @ts-ignore
		type: Object as PropType<Terminal.ITheme>,
		default: () => ({
			foreground: '#ffffff',
			background: '#000000',
			cursor: 'help',
		}),
	},
});

// 定义一个引用，用于指向DOM元素
const xtermContainer = ref<HTMLElement | null>(null);
const terminal = ref<Terminal | null>(null);
let fitAddon: FitAddon | null = null;

// 初始化终端并加载日志数据
onMounted(() => {
	if (!xtermContainer.value) return;

	// 创建一个新的终端实例
	const newTerminal = new Terminal({
		cursorBlink: true,
		fontSize: props.fontSize,
		theme: props.theme,
		convertEol: true, // 自动将换行符转换为回车换行; 不然可能会出现无法左对齐的情况
	});

	// 添加fit插件以自动调整大小
	fitAddon = new FitAddon();
	newTerminal.loadAddon(fitAddon);

	// 将终端挂载到DOM元素
	newTerminal.open(xtermContainer.value);

	// 确保FitAddon正确应用
	fitAddon.fit();

	// 手动触发尺寸调整
	window.setTimeout(() => {
		if (fitAddon) {
			fitAddon.fit();
		}
	}, 100);

	// 将终端实例赋值给ref
	terminal.value = newTerminal;
});

const ansiColorFixedFormat = (msg: string) => {
  return msg
  // 关键字染色
  .replace(/\b(ERROR|Exception|failed|unreachable)/gi, '\x1B[31m$1\x1B[0m') // 红色
  .replace(/\b(WARN|warnning)\b/gi, '\x1B[33m$1\x1B[0m')  // 黄色
  .replace(/\b(INFO|ok|success)\b/gi, '\x1B[32m$1\x1B[0m')  // 绿色
  // IP地址高亮
  .replace(/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/g, '\x1B[34m$1\x1B[0m');
}


// 显示日志数据的函数
const displayLogs = (term: Terminal, log: string) => {
	if (term) {
    if (props.isAnsiColorFixed){
      term.write(ansiColorFixedFormat(log));
    } else {
		  term.write(log);
    }
	}
};

// 监听 logData 变化
watch(
	() => props.logData,
	(newLog) => {
		if (terminal.value) {
			displayLogs(terminal.value, newLog.msg);
		}
	},
	{ deep: true }
);

// 监听 closeTerminal 变化
watch(
	() => props.closeTerminal,
	(closeTerminal) => {
		if (!closeTerminal) {
			if (terminal.value) {
				terminal.value.clear();
			}
		}
	},
	{ immediate: true }
);

// 在组件销毁时清理终端实例
onBeforeUnmount(() => {
	if (terminal.value) {
		terminal.value.dispose();
	}
});
</script>
 
<style scoped>
.terminal {
	width: 100%;
	height: 700px;
	border: 1px solid #ccc;
}
</style>