
// import request from '/@/utils/request';
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/gpu-instance/";

class DictAPI {
  /**
   * 获取服务器分页列表
   *
   * @param queryParams 查询参数
   * @returns 服务器分页结果
   */
  static getPage(queryParams: DictPageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取服务器表单数据
   *
   * @param id 服务器ID
   * @returns 服务器表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 新增服务器
   *
   * @param data 服务器表单数据
   */
  static add(data: DictForm) {
    return request({
      url: `${BASE_URL}`,
      method: "post",
      data: data,
    });
  }

  /**
   * 修改服务器
   *
   * @param id 服务器ID
   * @param data 服务器表单数据
   */
  static update(id: number, data: DictForm) {
    return request({
      url: `${BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  }

  /**
   * 删除服务器
   *
   * @param ids 服务器ID，多个以英文逗号(,)分隔
   */
  static deleteByIds(ids: string) {
    return request({
      url: `${BASE_URL}/${ids}`,
      method: "delete",
    });
  }

  /**
   * 获取服务器列表
   *
   * @returns 服务器列表
   */
  static getList() {
    return request({
      url: `${BASE_URL}/list`,
      method: "get",
    });
  }

  /**
   * 获取服务器的数据项
   *
   * @param typeCode 服务器编码
   * @returns 服务器数据项
   */
  static getOptions(code: string) {
    return request({
      url: `${BASE_URL}/${code}/options`,
      method: "get",
    });
  }
}

export default DictAPI;

/**
 * 服务器查询参数
 */
export interface DictPageQuery extends PageQuery {
  /**
   * 关键字(服务器名称/编码)
   */
  search?: string;
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}


/**
 * 服务器分页对象
 */
export interface DictPageVO {
  /**
   * 服务器ID
   */
  id: number;
  /**
   * 服务器名称
   */
  name: string;
  /**
   * 服务器编码
   */
  code: string;
  /**
   * 服务器状态（1-启用，0-禁用）
   */
  status: number;
  /**
   * 服务器项列表
   */
  dictItems: DictItem[];
}

/**
 * 服务器项
 */
export interface DictItem {
  /**
   * 服务器项ID
   */
  id?: number;
  /**
   * 服务器项名称
   */
  name?: string;
  /**
   * 服务器项值
   */
  value?: string;
  /**
   * 排序
   */
  sort?: number;
  /**
   * 状态（1-启用，0-禁用）
   */
  status?: number;
}

// TypeScript 类型声明

/**
 * 服务器
 */
export interface DictForm {
  /**
   * 服务器ID
   */
  id?: number;
  /**
   * 服务器名称
   */
  name?: string;
  /**
   * 服务器编码
   */
  code?: string;
  /**
   * 服务器状态（1-启用，0-禁用）
   */
  status?: number;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 服务器数据项列表
   */
  dictItems?: DictItem[];
}
