<template>
  <div>
    <div style="margin-bottom: 12px;">
              <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image :src="imageImg" alt="镜像图标" style="width: 24px; height: 16px;"></tiny-image>当前镜像</span>
              <tiny-tag type="success" size="medium"> {{ currentSelectImage }} </tiny-tag>
              
            </div>
    <tiny-grid ref="selectImageGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @radio-change="handleRadioChange" :resizable="true">
      <tiny-grid-column type="radio" width="40"></tiny-grid-column> 
      <tiny-grid-column field="name" title="ID/名称" width="180px" :filter="nameFilter">
        <template #default="{row}">
          <div class="id-cell">
              <!-- <span class="id-text">{{ row.ironicHyperisor_id.slice(0, 16) }}</span> -->
              <tiny-link :underline="false" type="primary">{{ row.image_id.slice(0, 8) }}</tiny-link>
              <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.image_id)"></tiny-link>
          </div>
          <p>{{ row.description }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column title="镜像类型" align="center" field="image_type">
      </tiny-grid-column>
      <tiny-grid-column title="系统类型" align="center" field="system_type">
      </tiny-grid-column>
      <tiny-grid-column field="min_disk" title="最小系统盘" align="center">
        <template #default="{row}">
          {{ Math.round(row.size/1024/1024) }} MiB
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="min_ram" title="最小内存" align="center">
        <template #default="{row}">
          {{ Math.round(row.size/1024/1024) }} MiB
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="status" title="访问控制" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.enabled ? '是': '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="disk_format" title="规格" align="center">
        <template #default="{row}">
          {{ toUpper(row.disk_format) }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="size" title="镜像大小" align="center">
        <template #default="{row}">
          {{ Math.round(row.size/1024/1024) }} MiB
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="node" title="区域" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="sync_time" title="同步时间" align="center" :sortable="true">
        <template #default="{row}">
          {{ formatNow(row.sync_time) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectImageForm">
import { ref, reactive, toRefs, watch } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyTag,
  TinyImage,
  TinyInput,
  TinyPager,
} from '@opentiny/vue';
import { GetList } from '/@/api/tenant/opImage';
import { toUpper } from 'lodash';

import { iconCopy } from '@opentiny/vue-icon';
import { formatNow } from '/@/utils/formatTime';
import { copyText } from '/@/utils/copyText';
import imageImg from '/@/assets/img/image.svg';


const props = defineProps({
  projectId: {
    type: String,
    required: false,
    default: ''
  },
  currentSelectImageId: {
    type: String,
    required: false,
    default: ''
  },
  image_type: {
    type: String,
    required: false,
    default: '',
  },
  node: {
    type: String,
    required: false,
    default: '',
  },
});
// 当前选中值
let currentSelectImage = ref<string>('--');
const emit = defineEmits(['update:currentSelectImageId']);

const TinyIconCopy = iconCopy();

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input,default,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})

// 初始化请求数据
interface FilterOptions {
  image_id: string;
  image_type: string;
  enabled: string;
  description: string;
  name: string;
  disk_format: string;
  is_to_portal: boolean;
  node: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    description: '',
    image_id: props.currentSelectImageId,
    image_type: props.image_type,
    enabled: '',
    name: '',
    is_to_portal: true,
    disk_format: 'qcow2',
    node: props.node,
  },
});
let tableData = ref([]);

const selectImageGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectImageGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;

    // 如果有初始选中的 ID，设置默认选中行
    if (props.currentSelectImageId) {
      // @ts-ignore
      const selectedRow = tableData.value.find(row => row.image_id === props.currentSelectImageId);
      if (selectedRow) {
        selectImageGrid.value?.setRadioRow(selectedRow);
        // @ts-ignore
        currentSelectImage.value = selectedRow.name;
      }
    }
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    description: '',
    image_id: '',
    image_type: props.image_type,
    enabled: '',
    name: '',
    disk_format: 'qcow2',
    is_to_portal: true,
    node: props.node,
  };
  // reloadGrid();
}
const handleRadioChange = () => {
  let selectedRow = selectImageGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectImage.value = `${selectedRow.name}`
    emit('update:currentSelectImageId', selectedRow.image_id)
  }
  
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
      filterOptions.value.name = filters.filters.name.value.text;
    }
  reloadGrid();
}


watch(() => props.image_type, (newImageType, oldImageType) => {
  if (newImageType && newImageType !== oldImageType) {
    filterOptions.value.image_type = newImageType;
    reloadGrid();
  }
});

// 监听 node 变化 
watch(() => props.node,  (newNode, oldNode) => {
  if (newNode && newNode !== oldNode) {
    filterOptions.value.node  = newNode;
    reloadGrid();
  }
});

</script>
<style lang="less" scoped>
.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>
