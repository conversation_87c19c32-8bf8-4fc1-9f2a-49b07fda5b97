import { request } from '/@/utils/service';
import { UserPageQuery, AddReq,InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/scheduletask/work/';

export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}
export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

export function RunWork(obj: any) {
	return request({
		url: apiPrefix + 'exec/',
		method: 'post',
		data: obj,
	});
}

export function ApplyWork(obj: any) {
	return request({
		url: apiPrefix + 'apply/',
		method: 'post',
		data: obj,
	});
}

export function AddObj(obj: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

