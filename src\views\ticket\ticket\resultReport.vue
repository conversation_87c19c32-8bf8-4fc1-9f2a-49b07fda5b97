<template>
  <fs-page>
    <div id="container-list" class="container-list">
      <!-- 加载中状态 -->
      <div v-if="isLoading" class="loading-state">
        <p>正在加载数据...</p>
      </div>
      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-state">
        <p>加载数据失败，请稍后再试。</p>
      </div>
      <div class="contain" v-else>
        <div id="account_config" class="create-step-container">
          <div class="container-step-head">
            <span>租户配置</span>
          </div>
          <tiny-form ref="createServerAccountFormValid" :inline="false" label-position="left" label-width="150px"
            style="border-radius: 0px;" :model="createServerFormData" :rules="createServerFormRules" :disabled="true">
            <tiny-form-item label="选择租户" prop="account_id">
              <span
                style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px;">选择已有租户，则创建此租户下的服务器；若无租户，请联系管理员。</span>
              <SelectAccountForm v-model:current-select-account-id="createServerFormData.account_id">
              </SelectAccountForm>
            </tiny-form-item>
          </tiny-form>
          <tiny-divider></tiny-divider>
        </div>

        <div id="host_config" class="create-step-container">
          <div class="container-step-head">
            <span>主机配置</span>
          </div>
          <tiny-form ref="createServerHostFormValid" :model="createServerFormData" :inline="false" label-position="left"
            label-width="150px" style="border-radius: 0px" :rules="createServerFormRules" :disabled="true">
            <tiny-form-item label="选择规格" prop="flavor_id">
              <SelectFlavorForm v-model:current-select-flavor-id="createServerFormData.flavor_id"
                :selected-row-id="createServerFormData.flavor_id" />
            </tiny-form-item>
            <tiny-form-item label="选择镜像" prop="image_id">
              <SelectImageForm v-model:current-select-image-id="createServerFormData.image_id" />
            </tiny-form-item>
            <tiny-form-item label="选择预装软件" prop="softwares">
              <SelectSoftwareForm v-model:current-select-softwarees="createServerFormData.softwares"/>
            </tiny-form-item>
          </tiny-form>
          <tiny-divider></tiny-divider>
        </div>

        <div id="basical_config" class="create-step-container">
          <div class="container-step-head">
            <span>基础配置</span>
          </div>
          <tiny-form ref="createServerBasicalFormVaild" :model="createServerFormData" :inline="false"
            label-position="left" label-width="150px" :label-align="true" style="border-radius: 0px"
            :rules="createServerFormRules" :disabled="true">
            <tiny-form-item label="区域" prop="node">
              <tiny-button-group v-model="createServerFormData
            .node" style="border-radius: 0px; margin-right: 10px"
              :data="allNodes" :text-field="nodeValueField" :value-field="nodeValueField"></tiny-button-group>
              <span
                style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">不同区域的云服务产品之间内网互不相通；请就近选择靠近您业务的区域，可减少网络时延，提高访问速度。</span>
            </tiny-form-item>
            <tiny-form-item label="主机类型" prop="instance_type">
              <tiny-button-group v-model="createServerFormData.instance_type"
                style="border-radius: 0px; margin-right: 10px"
                :data="[{ text: '裸金属', value: '裸金属' }, { text: '虚拟机', value: '虚拟机', disabled: true }]"></tiny-button-group>
            </tiny-form-item>
            <tiny-form-item label="主机名称" prop="name">
              <tiny-input placeholder="请输入" v-model="createServerFormData.name" style="width: 40%; margin-right: 10px"
                :maxlength="63" show-word-limit></tiny-input>
            </tiny-form-item>
            <tiny-form-item label="主机描述" prop="description">
              <tiny-input placeholder="请输入" type="textarea" v-model="createServerFormData.description"
                style="width: 40%; margin-right: 10px" :maxlength="255" show-word-limit></tiny-input>
            </tiny-form-item>
            <tiny-form-item label="分配公网" prop="is_need_extra_public_ip">
              <tiny-radio-group v-model="createServerFormData.is_need_extra_public_ip">
                <tiny-radio :label="true">是</tiny-radio>
                <tiny-radio :label="false">否</tiny-radio>
              </tiny-radio-group>
              <span
              style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">开通后，默认开放规则为IP绑定；访问公网IP:{内网端口}即可。
            </span>
            </tiny-form-item>
            <tiny-form-item label="公网IP数量" prop="count" v-if="createServerFormData.is_need_extra_public_ip === true">
              <tiny-input type="number" placeholder="请输入" v-model="createServerFormData.count"
                style="width: 40%; margin-right: 10px" :disabled="true"></tiny-input>
            </tiny-form-item>
            <tiny-form-item label="过期时间" prop="expire_at">
              <tiny-date-picker v-model="createServerFormData.expire_at" :picker-options="pickerOptions" type="datetime"
                placeholder="请选择过期日期" default-time="23:59:59" style="width: 40%; margin-right: 10px;"
                value-format="yyyy-MM-dd HH:mm:ss"></tiny-date-picker>
            </tiny-form-item>
            <tiny-form-item label="数量" prop="count">
              <tiny-input type="number" placeholder="请输入" v-model="createServerFormData.count"
                style="width: 120px; margin-right: 10px;" :step="1" :min="1" :max="10"></tiny-input>
            </tiny-form-item>
          </tiny-form>
          <tiny-divider></tiny-divider>
        </div>
        <tiny-collapse v-model="previewCreateServerInfo" accordion style="margin: 20px; background-color: #5e7ce0;">
          <tiny-collapse-item title="查看主机匹配的Openstack信息" name="1">
            <div id="project_config" class="create-step-container">
              <div class="container-step-head">
                <span>项目配置</span>
              </div>
              <tiny-form ref="createServerFormProjectIdValid" :inline="false" label-position="left" label-width="150px"
                style="border-radius: 0px" :model="createServerFormData" :rules="createServerFormRules">
                <tiny-form-item label="选择项目" prop="project_id">
                  <SelectProjectForm v-model:current-select-project-id="createServerFormData.project_id" />
                </tiny-form-item>
              </tiny-form>
            </div>

            <div id="network_config" class="create-step-container">
              <div class="container-step-head">
                <span>网络配置</span>
              </div>
              <tiny-form ref="createServerFormNetworkValid" :model="createServerFormData" :inline="false"
                label-position="left" label-width="150px" style="border-radius: 0px" :rules="createServerFormRules">
                <tiny-form-item label="选择网络" prop="network_id">
                  <SelectNetworkForm :project-id="createServerFormData.project_id"
                    v-model:current-select-network-id="createServerFormData.network_id" />
                </tiny-form-item>
                <tiny-form-item label="选择安全组" prop="security_group_names">
                  <SelectSecurityGroupForm :project-id="createServerFormData.project_id"
                    v-model:current-select-security-group-id="createServerFormData.security_group_names" />
                </tiny-form-item>
              </tiny-form>
            </div>
          </tiny-collapse-item>
        </tiny-collapse>
        <div id="created_hosts" class="create-step-container">
          <div class="container-step-head">
            <tiny-tooltip pre :content="preToolTipData" placement="top-start" type="success">
              <tiny-badge :value="2" :offset="['0', '-75%']"
                :type="serverHostCount.activeHostCount === Number(createServerFormData.count) && serverHostCount.activeHostCount === serverHostCount.allHostCount ? 'success' : 'danger'">
                <span>已创建主机信息</span>
                <template #content>
                  {{ serverHostCount.activeHostCount }} / {{ serverHostCount.allHostCount }} / {{
                    createServerFormData.count }}
                </template>
              </tiny-badge>
            </tiny-tooltip>
          </div>
          <PortalServerIndex :belong_ticket_id="urlParams.ticket_id"
            v-model:active-host-count="serverHostCount.activeHostCount"
            v-model:all-host-count="serverHostCount.allHostCount" />
          <div>
            <div class="footer-button">
              <!-- <tiny-button text="确认修复" type="success" style="max-width: unset" :reset-time="5000"
                :disabled="serverHostCount.activeHostCount !== Number(createServerFormData.count) || serverHostCount.activeHostCount !== serverHostCount.allHostCount"
                v-if="urlParams.step === '2'" @click="updateTicketStatusData('分配公网', 3)"></tiny-button> -->
                <tiny-button text="查看日志" type="primary" style="max-width: unset" :reset-time="5000"
                v-if="urlParams.step === '2' && createServerFormData.error_info" @click="openServerLog"></tiny-button>
              <tiny-button text="修复完成确认" type="success" style="max-width: unset" :reset-time="5000"
                v-if="urlParams.step === '2' && auth('ticket:ticketResultReport:TenantBaremetalServerManualFixed')" @click="updateTicketStatusData('分配公网', 3)"></tiny-button>

                <!-- <tiny-button text="完成订单" type="success" style="max-width: unset" :reset-time="5000"
                :disabled="serverHostCount.activeHostCount !== Number(createServerFormData.count) || serverHostCount.activeHostCount !== serverHostCount.allHostCount"
                v-if="urlParams.step === '3'" @click="updateTicketStatusData('订单完成', 4)"></tiny-button> -->
              <tiny-button text="完成订单" type="primary" style="max-width: unset" :reset-time="5000"
              v-if="urlParams.step === '3' && auth('ticket:ticketResultReport:TenantBaremetalServerManualFinished')" @click="updateTicketStatusData('订单完成', 4)"></tiny-button>
              <tiny-tooltip type="normal" effect="light">
                <template #content>
                  <span style="display:flex;">点击 “生成交付报告” 后，才会出现 “下载报告” 按钮;</span>
                  <span style="display:flex;">需要生成最新数据的报告内容请重新点击 “生成交付报告” ,再点击 “下载报告” 按钮，获取最新报告。</span>
                </template>
                <tiny-button text="生成交付报告" type="primary" style="max-width: unset" :reset-time="5000"
                  v-if="urlParams.step === '4' && auth('ticket:ticketResultReport:TenantBaremetalServerManualGenReport')" @click="createTicketReportData(urlParams.ticket_id)"></tiny-button>
              </tiny-tooltip>
              <tiny-button type="success" style="max-width: unset; margin-left: 20px;" :reset-time="5000" :disabled="isDownloadButtonVisible" @click="downloadTicketReport">下载报告</tiny-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <tiny-modal v-model="isShowLogRef" title="提示" message="窗口内容" width="80%">
      <div>
        <XTermLogViewer :logs="formattedLogs" />
       </div>
    </tiny-modal>
  </fs-page>
</template>

<script setup name="portalServerCreateReport" lang="ts">
import { onMounted, ref, computed, } from 'vue';
import {
  Divider as TinyDivider,
  Modal as TinyModal,
  Form as TinyForm,
  Button as TinyButton,
  ButtonGroup as TinyButtonGroup,
  FormItem as TinyFormItem,
  TinyBadge,
  TinyTooltip,
  TinyRadio,
  TinyRadioGroup,
} from '@opentiny/vue';
import SelectFlavorForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectFlavorForm.vue';
import SelectImageForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectImageForm.vue';
import SelectAccountForm from '/@/views/portalTenants/portal/portalAccount/createComp/selectAccountForm.vue';
import SelectProjectForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectProjectForm.vue';
import SelectNetworkForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectNetworkForm.vue';
import PortalServerIndex from '/@/views/portalTenants/portal/portalServer/index.vue';
import SelectSecurityGroupForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectSecurityGroupForm.vue';
import SelectSoftwareForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectPreInstallSoftwareForm.vue';
import XTermLogViewer from '/@/components/myXterm/XTermLogViewer.vue';
import { updateTicketStatus, createTicketReport, getCurrentTicketTasks } from '/@/api/ticket/ticket';
import { useRouter, useRoute } from 'vue-router';
import { pickerOptions } from '/@/utils/pickDatetimeOptions';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';


// 定义加载状态
const isLoading = ref(true);
const hasError = ref(false);

const route = useRoute();
const nodeValueField = ref('value');
const allNodes = dictionary('operator_cmdb:host:area_node', undefined);

const preToolTipData = ref(
  [
    '已创建成功的主机数量 / 已创建主机的总数 / 需求主机总数;',
    '三者相同时，方可点击“完成订单”或“下一步”。'
  ].join('\n')
);
const previewCreateServerInfo = ref(1);

const urlParams = {
  ticket_id: route.params.ticket_id as string,
  step: route.params.step as string,
};

// 路由
const router = useRouter();

let createServerFormData = ref({
  image_id: '',
  flavor_id: '',
  count: 1,
  name: '',
  description: '',
  instance_type: '裸金属',
  node: '',
  account_id: '',
  project_id: '',
  network_id: '',
  security_group_names: '',
  error_info: [],
  expire_at: '',
  extr_public_ipv4: '',
  softwares: [],
  is_need_extra_public_ip: false,
});

const serverHostCount = ref({
  activeHostCount: 0,
  allHostCount: 0
});

const createServerFormRules = ref({
  account_id: [
    { required: true, message: '请选择账号', trigger: 'blur' },
  ],
  name: [
    { required: true, message: '请输入主机名称', trigger: 'blur' },
    { min: 3, max: 63, message: '最小3个字符，最大63个字符', trigger: 'blur' },
  ],
  description: [
    { max: 255, message: '最大255个字符', trigger: 'blur' }
  ],
  node: [
    { required: true, message: '请选择区域', trigger: 'blur' }
  ],
  instance_type: [
    { required: true, message: '请选择主机类型', trigger: 'blur' }
  ],
  count: [
    { required: true, message: '请输入创建数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10, message: '最小为 1，最大为 10', trigger: 'blur' },
  ],
  expire_at: [
    { required: true, message: '请选择过期时间', trigger: 'blur' }
  ],
  image_id: [
    { required: true, message: '请选择镜像', trigger: 'blur' }
  ],
  flavor_id: [
    { required: true, message: '请选择规格', trigger: 'blur' }
  ],
  project_id: [
    { required: true, message: '请选择项目', trigger: 'blur' }
  ],
  network_id: [
    { required: true, message: '请选择网络', trigger: 'blur' }
  ],
});

const createTicketReportData = async (id: string) => {
  try {
    const response = await createTicketReport(id);
    TinyModal.message({
      message: response.msg,
      status: 'success',
    });
    // 刷新数据
    getCreatedTicketTask();
  } catch (error) {
    TinyModal.message({
      message: '创建失败',
      status: 'error',
    });
  }
};

const updateTicketStatusData = async (current_status: string, current_step_index: number) => {
  try {
    const response = await updateTicketStatus({
      ticket_id: urlParams.ticket_id,
      current_step_index: current_step_index,
      current_status: current_status,
    });
    TinyModal.message({
      message: response.msg,
      status: 'success',
    });

    router.go(-1);
  } catch (error) {
    TinyModal.message({
      message: '更新失败',
      status: 'error',
    });
  }
};

let currnetTicketData = ref({
  report_url: '',
  ticket_id: '',
});

// 计算是否显示“下载报告”按钮
const isDownloadButtonVisible = computed(() => {
  return currnetTicketData.value.report_url === '' || !auth('ticket:ticketResultReport:TenantBaremetalServerManualGenReport')
});


let currentTaskData = ref({
step: 0,
async_worker_result: ''
});
const getCreatedTicketTask = async () => {
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    const taskResponse = await getCurrentTicketTasks({ ticket_id: urlParams.ticket_id, is_step_index: 0 });
    currnetTicketData.value = taskResponse.data.ticket
    currentTaskData.value = taskResponse.data.tasks[0]
    // @ts-ignore
    if (currentTaskData.value?.form_data) {
      // @ts-ignore
      createServerFormData.value = currentTaskData.value?.form_data
      isLoading.value = false;
    }
    else {
      hasError.value = true;
      isLoading.value = false;
    }
  } catch (error) {
    hasError.value = true;
    isLoading.value = false;
    TinyModal.message({
      message: '获取失败',
      status: 'error',
    });
  }
};

// 定义一个计算属性，确保传递给 XTermLogViewer 的 logs 总是一个数组
const formattedLogs = computed(() => {
  if (Array.isArray(createServerFormData.value.error_info)) {
    return createServerFormData.value.error_info;
  } else {
    // 返回默认的日志数据
    return [
      { timestamp: new Date().toISOString(), level: 'INFO', msg: 'No error information available.' },
    ];
  }
});

const isShowLogRef = ref(false);
const openServerLog = () => {
  if (!Array.isArray(createServerFormData.value.error_info)) {
    createServerFormData.value.error_info =  [
      // @ts-ignore
      { timestamp: new Date().toISOString(), level: 'INFO', msg: '暂无日志' },
    ];
  } 
  isShowLogRef.value = true;
}

const downloadTicketReport = () => {
  if (currnetTicketData.value.report_url) {
    fetch(currnetTicketData.value.report_url)
      .then(response => {
        if (!response.ok) {
          throw new Error('文件下载失败');
        }
        return response.blob();
      })
      .then(blob => {
        // 设置自定义文件名
        const fileName = `${currnetTicketData.value.ticket_id}交付报告.docx`;

        // 创建一个临时的 <a> 标签
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;  // 确保这里设置了文件名
        a.click();

        // 释放 URL 对象
        window.URL.revokeObjectURL(url);
        TinyModal.message({
          message: '获取报告地址成功',
          status: 'success',
        });
      })
      .catch(error => {
        TinyModal.message({
          message: '下载失败',
          status: 'danger',
        });
      })
  } else {
    // 如果没有报告 URL，提示用户
    TinyModal.message({
      message: '报告尚未生成，请稍后再试',
      status: 'warning',
    });
  }
}


// 获取已提交的参数

onMounted(async () => {
  try {
    await Promise.all([
      getCreatedTicketTask()
    ])
    // 所有异步操作完成后，设置加载状态为 false
    // ticketIsSuccess.value = false;
  } catch {
    // baseResourceIsLoading.value = false;
  }

});
</script>
<style scoped lang="less">
.new-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
  padding: 8px;
}

.footer-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px;
}

.loading-state,
.error-state {
  text-align: center;
  margin-top: 50px;
  font-size: 18px;
  color: #8a8e99;
}

.show-error-info {
  max-width: 100%;
}

.error-tag-info {
  margin-bottom: 8px;
  display: block;
  font-weight: 300;
}

.fixed-header {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-server-steps {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-step-container {
  margin: 20px;
}

.container-step-head {
  font-size: 24px;
  font-weight: bolder;
  margin-bottom: 24px;
}

.content {
  margin-top: 150px;
  /* 确保内容不会被步骤条覆盖 */
  overflow-y: auto;
  /* 允许内容区域垂直滚动 */
}

.section {
  height: 100vh;
  /* 每个部分的高度为视口高度 */
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.container-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 20px);
  overflow-x: hidden;
  overflow-y: auto;
}

/* Tiny Vue 组件搜索栏修改圆角 */
:deep(.tiny-input__inner) {
  border-radius: 6px;
  /* 设置圆角大小 */
}

:deep(.tiny-button) {
  border-radius: 16px;
  /* 设置圆角大小 */
}

.line {
  height: 1px;
  color: rgb(213, 213, 213);
  background-color: rgb(213, 213, 213);
  border: 0;
}

.contain {
  flex: 1 1 auto;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);

  .contain-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 10px 0 10px;

    hr {
      .line();

      width: 86%;
      margin: 0 20px;
    }

    span {
      color: #1a1818;
      font-size: 16px;
    }
  }

  .contain-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 52px;
  }

  .contain-img {
    position: relative;
    display: flex;
    cursor: pointer;

    img:hover {
      background: whitesmoke;
    }
  }

  .contain-text {
    padding-left: 10px;
    color: #4e5969;
    font-size: 14px;
    cursor: pointer;
  }
}

.bottom-line {
  padding-bottom: 8px;

  hr {
    .line();

    width: 96%;
    margin: 0 auto;
  }
}

.filter-form {
  margin: 10px 0;
}

.col {
  width: 96%;
}

:deep(.tiny-grid) {

  &-header__column,
  &-body__column {

    &.col__selection,
    &.col__radio {
      padding: 0 8px 0 8px;

      &+th,
      +td {
        padding-left: 0;
      }
    }
  }
}

:deep(.tiny-pager) {
  float: right;
}

:deep(.tiny-grid-button__wrapper) {
  width: 100%;
}

:deep(.tiny-form-item__label) {
  color: #494747;
  font-weight: normal;
}

:deep(.tiny-grid-header__column) {
  height: 35px;
  color: rgb(139, 137, 137);
  background-color: #f5f6f7;
}

.operation {
  color: #5e7ce0;
}

.btn {
  display: flex;
  width: 100%;

  .screen {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    text-align: center;
    cursor: pointer;

    span {
      margin-left: 10px;
      color: #4e5969;
      font-size: 14px;
    }
  }
}

.btn>div:last-child {
  margin-left: auto;
}

.tiny-fullscreen {
  background: #fff;
}

.tiny-fullscreen-wrapper {
  min-height: 710px;
  padding: 0 30px;
}

.tiny-fullscreen-scroll {
  overflow-y: auto;
}

.search-btn {
  display: flex;

  button {
    height: 34px;
  }
}

.id-cell {
  display: flex;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    /* 根据需要调整宽度 */
  }
}

.status-success {
  fill: #52c41a;
  margin-right: 8px;
}

.status-danger {
  fill: #C7000B;
  margin-right: 8px;
}

.status-warning {
  fill: #FA9841;
  margin-right: 8px;
}
</style>