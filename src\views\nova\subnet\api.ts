
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/subnet";

class SubnetAPI {
  /**
   * 获取子网分页列表
   *
   * @param queryParams 查询参数
   * @returns 子网分页结果
   */
  static getPage(queryParams: SubnetPageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取子网表单数据
   *
   * @param id 子网ID
   * @returns 子网表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 获取子网列表
   *
   * @returns 子网列表
   */
  static getList(project_name?: string) {
    let url = `${BASE_URL}/list/`
    if (project_name) {
      url += `?project_name=${project_name}`;
    }
    return request({
      url: url,
      method: "get",
    });
  }

}

export default SubnetAPI;

/**
 * 子网查询参数
 */
export interface SubnetPageQuery extends PageQuery {
  search?: string;    // keywords
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}

/**
 * 子网分页数组中的单个对象
 */
export interface SubnetPageVO {
  subnet_id: string;
  name: string;
  project_id: string;
  project_name: string;
  network_id: string;
  ip_version: number;
  is_dhcp_enabled: boolean;
  gateway_ip: string;
  cidr: string;
  allocation_pools: string;
  host_routes: string;
  dns_nameservers: string;
}

/**
 * 子网表单
 */
export interface SubnetForm {
  subnet_id?: string;
  name?: string;
  project_id?: string;
  project_name?: string;
  network_id?: string;
  ip_version?: number;
  is_dhcp_enabled?: number;
  gateway_ip?: string;
  cidr?: string;
  allocation_pools?: string;
  host_routes?: string;
  dns_nameservers?: string;
}
