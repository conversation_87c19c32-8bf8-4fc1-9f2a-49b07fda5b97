<!-- <template>
  <div class="transfer-container">
    <el-row :gutter="24">
      <el-col :span="12" class="left">
        <div class="header">{{ titles[0] }}</div>
        <div class="panel">
          <el-input
            v-model="inputContent"
            placeholder="请输入内容"
            clearable
            @input="handleInputChange"
            @clear="inputClear"
          />
          <el-table
            ref="tableRef"
            :data="currentTableData"
            tooltip-effect="dark"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column prop="id" label="用户名" />
            <el-table-column type="selection" width="40" />
          </el-table>
        </div>
      </el-col>

      <el-col :span="12" class="right">
        <div class="header">{{ titles[1] }}</div>
        <div class="panel">
          <ul>
            <li
              class="item"
              v-for="(item, index) in currentSelection"
              :key="index"
            >
              <span>{{ item.id }}</span>
              <i class="el-icon-close" @click="deleteHandle(item)"></i>
            </li>
          </ul>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';

const props = defineProps({
  titles: {
    type: Array,
    default: () => ['源列表', '目标列表'],
  },
  tableData: {
    type: Array,
    required: true,
  },
  selectedData: {
    type: Array,
    required: true,
  },
  labelKey: {
    type: String,
    default: 'username',
  },
  maxSelect: {
    type: Number,
    default: Infinity,
  },
});

console.error(props)
const emit = defineEmits(['selectChange']);

const inputContent = ref('');
const currentSelection = ref([]);
const currentTableData = ref([]);
const tableRef = ref(null);

watch(() => props.tableData, (newVal) => {
  currentTableData.value = newVal;
});

watch(() => props.selectedData, (newVal) => {
  currentSelection.value = newVal;
});

function handleSelectionChange(selection) {
  currentSelection.value = selection;
  emit('selectChange', currentSelection.value);
}

function handleInputChange() {
  if (inputContent.value === '') {
    inputClear();
    return;
  }

  const filterData = props.tableData.filter((item) => {
    return (
      item.id === inputContent.value ||
      item[props.labelKey].includes(inputContent.value)
    );
  });

  currentTableData.value = filterData;
}

function inputClear() {
  currentTableData.value = props.tableData.filter((item) => {
    return !currentSelection.value.some((citem) => citem.userId === item.userId);
  });
}

function deleteHandle(special) {
  const index = currentSelection.value.findIndex(
    (item) => item.userId === special.userId
  );

  if (index !== -1) {
    currentSelection.value.splice(index, 1);
    tableRef.value.toggleRowSelection(special, false);
    emit('selectChange', currentSelection.value);
  }
}

nextTick(() => {
  currentTableData.value = props.tableData;
});
</script>

<style scoped>
.transfer-container div {
  box-sizing: border-box;
}

.transfer-container {
  width: 100%;
  padding: 30px;
  text-align: center;

  .table-transfer {
    min-width: 800px;
    margin: 0 auto;
    .header {
      height: 28px;
      line-height: 28px;
      padding-left: 30px;
      color: darkblue;
      text-align: left;
    }
    .panel {
      width: 100%;
      height: 400px;
      border: 1px solid #eaebee;
      padding: 10px 30px;
      border-radius: 10px;
    }
    .buttons {
      line-height: 300px;
    }
    .left {
      .header {
        border-radius: 0px 20px 0 0px;
      }
      .el-input {
        width: 100%;
        margin: 20px 0;
      }
      .el-input__inner {
        border-radius: 20px;
        height: 30px;
      }
      .el-table__body-wrapper {
        min-height: 200px;
        max-height: 270px;
        overflow: auto;
      }
      .cell {
        padding: 0px;
      }
      td {
        border: none;
      }
      th.is-leaf {
        border: none;
      }
      .el-table td {
        padding: 5px 0px;
      }
      .el-table {
        border-bottom: none !important;
      }
      .el-table__body-wrapper {
        border-bottom: none !important;
      }
    }
    .right {
      .header {
        border-radius: 20px 0px 0px 0px;
      }
      ul {
        width: 100%;
        margin: 0;
        padding: 0;
        padding-top: 10px;
        li {
          display: flex;
          justify-content: space-between;
          height: 30px;
          line-height: 30px;
        }
        span,
        i {
          display: inline-block;
        }
        .el-icon-close:before {
          line-height: 30px;
        }
      }
    }
  }
}
</style> -->
