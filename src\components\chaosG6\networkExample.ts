export const exampleData = {
  "data": {
    "nodes": [
      {
        "id": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "data": {
          "type": "switch",
          "text": "测试001",
          "ip": "0.0.0.0",
          "vendor": "华为",
          "status": "active",
          "details": {
            "sn": "4E21A0150680",
            "firmware_version": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.720800",
            "vlan_range": null
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-dfcf31dcddf649a78fabf5cfef228188",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/7",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:09.018279"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-c08e61db169148108b187b419b13d32a",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/48",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "346A:C224:3B55",
            "last_sync_at": "2025-08-01T13:38:09.012076"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-5a71507a33d241539efa9125344937b4",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/47",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "049F:CAD4:49D1",
            "last_sync_at": "2025-08-01T13:38:09.004758"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-c46607cba51645f5a0aa42e7a034b097",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/46",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.997895"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-da0328663bea444dbc833fa436a63639",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/45",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "F44C:7F5D:B592",
            "last_sync_at": "2025-08-01T13:38:08.991991"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-bb3a7fbb4d5a486bb71132e60cb252f0",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/44",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "AC61:75E5:2A8C",
            "last_sync_at": "2025-08-01T13:38:08.985573"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-f2d937565d0243afa920cde799fa0c28",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/15",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.977769"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-a86985ce88fe4d19abad63ec51661a08",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/14",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.972949"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-818a5627d29a400184de88801c3c606e",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/13",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.967502"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-cefdbdbf3d304ed5af664e7f57ffb594",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/12",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.963039"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-b52f64a721e847f48d7107682b1869d2",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/11",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.958965"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-acac6936089b40058f636e0d22b81b8a",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/22",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.954042"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-14b19676cd8b4d7391316add7c3ec57d",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/21",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.948660"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-00e286b4915542c3af32d19bffb75c5f",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/20",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.943935"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-68c5f2af23fd45dcaf2d9ac9b1c72fef",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/19",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.938013"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-00811a92b2dd452bb02d550419cc6f9e",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/17",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.932562"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-0a896e8cb2ef44a28c9264a7bcfb4625",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/6",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.927915"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-d77ade73391d46349901d257eba16497",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/5",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.920831"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-2db0b0a8d4824232bb89b5a7b4a18fc8",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/4",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.915655"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-9a86ddc94bab41a7b826d1beb71dc663",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/3",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.910206"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-379773c9248b4b039f9bef105d4f4e1e",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/2",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.902514"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-d5be871e8c7048ebb11634f19547abe3",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/1",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.898679"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-f2707b3056b74addbc8e872515898661",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/34",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.894412"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-b6b15a41c06c4ed3bc7400ce430142d4",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/33",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.886886"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-4aa88f2af331442d80eeaaa403ef3016",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/9",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.880437"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-9f441ffcde184301bca8715750824387",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/8",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.875734"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-6dc848fb27ab47b1a31e839c3f3de7a0",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/8",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.871622"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-9cc9e843a11a4c6daec691d93609813e",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/7",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.867819"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-f822de67e30e4087ac82971dd44735a8",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/43",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.863494"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-d43244194dfe48298e6900e7a824d289",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/42",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.858504"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-71ff3f1200534339a63b923e1669fb84",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/41",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.852874"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-0fdebdacb4c04523be806be9164ea63a",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/40",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.847900"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-cc27d42f0ac841a89d022d7d6a2c3309",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/39",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.842590"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-4cd198bd551444cdadfdf51b3292978e",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/38",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.837238"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-6445019f3c794ccab9f4b83a5344cc4d",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/37",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.831804"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-91ac9f7cc951482ea62dae9e9b4eb804",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/36",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.827165"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-6311972da5904bfd88619bf543f187b5",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/35",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.822318"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-f7492f602d4a4f16a259acdb95f8191c",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/32",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.817907"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-9016431d58334d73bc3663c11e8eeeb6",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/31",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.811776"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-b218a0213515444c9965e0f5f7fc438b",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/30",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.806275"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-4c1d9e02c2ad4f5192854e7eabf5c441",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/29",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.799776"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-b6b4513331dc4d6aa21aaf9aac747273",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/28",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.794926"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-a52069bcbb1e4005b64f97b2b7aa7ff0",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/27",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.790163"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-923a8820e0e24a0c80f4e0ba7258b766",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/26",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.784426"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-dd1035d8b86f495088e024e3302f70bb",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/25",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.778845"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-43544a61d2d342baa0ed33b6b34c26c1",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/23",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.770004"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-20cab834d20a4edb81c5a58c01ba50ee",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/2",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:09.028085"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-dae192e2fcbe48a6b28c822b0968c84f",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/1",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:09.022855"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-565e4369fde744b8aa4faf691ebcbd38",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/24",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "FA16:3EEA:57EF",
            "last_sync_at": "2025-08-01T13:38:08.774114"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-4546ee041e874d9d84dfa50a0ebb3001",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/18",
          "speed": "Unknown",
          "state": "up",
          "status": "active",
          "details": {
            "line_state": "up",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.765209"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-2610957c0c4846cc8120c8253d12449b",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/16",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.758892"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-90c86b49da7445328cd9aaa58d61caac",
        "data": {
          "type": "physical-port",
          "text": "25GE1/0/10",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.753259"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-845da4d5a7ab4ba0b501a92098345f7c",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/6",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.746010"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-5fc0c96fc22c43559f1f0db1d3f78514",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/5",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.739137"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-f6f66a7de5f044c4b6b4983c07abd265",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/4",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.734702"
          }
        }
      },
      {
        "id": "physical-cmdb_vs_physical_interface-d13319cf7ee44a5a898ec4f48f6f53f2",
        "data": {
          "type": "physical-port",
          "text": "100GE1/0/3",
          "speed": "Unknown",
          "state": "down",
          "status": "inactive",
          "details": {
            "line_state": "down",
            "mac_address": "N/A",
            "last_sync_at": "2025-08-01T13:38:08.728267"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-d620ee5c9f084e2c9355cad630a0f11e",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk77",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/7"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.208818"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-627af24789204554975d94231be24020",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk48",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/48"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.203178"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-dd3c4c6dcbec4191a33c732dfbd5a29e",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk47",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/47"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.198028"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-befbad796f16452197602fc9f0cffa29",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk46",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/46"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.192608"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-57e827c0552441fdb9f56242d6aa561e",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk45",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/45"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.186342"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-95f635b3649d42dda72dfb93138ac268",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk44",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/44"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.178573"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-1b1ca4ea2cf1402bb280ccea1052bf81",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk34",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/15",
              "25GE1/0/15(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.172936"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-91d50880fbf2495b8568df6bd993c750",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk33",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/14",
              "25GE1/0/14(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.167190"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-7d4ee5db78bb4a73be625c28d9db75a4",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk32",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/13",
              "25GE1/0/13(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.160595"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-dee6239188d9410a8206e46987ed7fd9",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk31",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/12",
              "25GE1/0/12(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.154338"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-025b7ec2a4bb40999298c84d7c4dd5a4",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk30",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/11",
              "25GE1/0/11(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.146125"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-c9223c2ad8074734965858680d124773",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk28",
          "state": "down",
          "status": "inactive",
          "member_count": 0,
          "details": {
            "members": [],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.139041"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-4edd9e329e0a45edb75a644d08f58267",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk27",
          "state": "down",
          "status": "inactive",
          "member_count": 0,
          "details": {
            "members": [],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.133136"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-ffe38d59d84041a399482a64da1ca8de",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk24",
          "state": "down",
          "status": "inactive",
          "member_count": 0,
          "details": {
            "members": [],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.124451"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-f3beb66a30c94042be133da6e8d3584a",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk22",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/22"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.118231"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-d31975ccd6e94293b0d7783cc66e0a64",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk21",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/21",
              "25GE1/0/21(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.110911"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-d90f6f730ba749e3a30ced4ce0eec29b",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk20",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/20",
              "25GE1/0/20(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.105041"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-8109f51db82a46159b6c69135604324c",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk19",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/19(hr)",
              "25GE1/0/19"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.099788"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-f53d79eceb704700a0467b4080189899",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk17",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/17",
              "25GE1/0/17(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.094116"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-26e7cc4b71134b15a0db29eb1231c55f",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk16",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/6"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.088527"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-b3ef2e93c6874f0e94d89b7a3475ef69",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk15",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/5(hr)",
              "25GE1/0/5"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.083164"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-1870dc0244bf4c51a9d96f4aa851b2ce",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk14",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/4(hr)",
              "25GE1/0/4"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.078075"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-f673dd4da7274a83a26eb40056b78aac",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk13",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/3(hr)",
              "25GE1/0/3"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.070960"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-45f347bf6ba14434a00a1c11eb3e62d3",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk12",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/2(hr)",
              "25GE1/0/2"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.065295"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-8a6ecb4cc2a74a4c9e2f3ae68db9bb09",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk11",
          "state": "up",
          "status": "active",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/1",
              "25GE1/0/1(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.059149"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-a55b4681035346ba89a3d723ca3f3210",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk9",
          "state": "down",
          "status": "inactive",
          "member_count": 2,
          "details": {
            "members": [
              "25GE1/0/33",
              "25GE1/0/34"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.052275"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-04f794b6aed248479c936ed1034ba491",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk8",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/9"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.046504"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-8a93034cff0940c0a77c816191a95885",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk100",
          "state": "up",
          "status": "active",
          "member_count": 3,
          "details": {
            "members": [
              "100GE1/0/2(r)",
              "100GE1/0/2",
              "100GE1/0/1"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.214610"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-5a67c98a1ea74c2b91584dcd69c8cff9",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk7",
          "state": "down",
          "status": "inactive",
          "member_count": 1,
          "details": {
            "members": [
              "25GE1/0/8"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.039760"
          }
        }
      },
      {
        "id": "trunk-cmdb_vs_trunk_interface-a0ab47e1768a4740a22530507496cfd7",
        "data": {
          "type": "trunk-port",
          "text": "Eth-Trunk1",
          "state": "up",
          "status": "active",
          "member_count": 4,
          "details": {
            "members": [
              "100GE1/0/8(h)",
              "100GE1/0/8",
              "100GE1/0/7",
              "100GE1/0/7(hr)"
            ],
            "line_state": "",
            "mac_address": null,
            "last_sync_at": "2025-08-01T13:38:09.033072"
          }
        }
      }
    ],
    "edges": [
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-dfcf31dcddf649a78fabf5cfef228188",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-c08e61db169148108b187b419b13d32a",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-5a71507a33d241539efa9125344937b4",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-c46607cba51645f5a0aa42e7a034b097",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-da0328663bea444dbc833fa436a63639",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-bb3a7fbb4d5a486bb71132e60cb252f0",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-f2d937565d0243afa920cde799fa0c28",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-a86985ce88fe4d19abad63ec51661a08",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-818a5627d29a400184de88801c3c606e",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-cefdbdbf3d304ed5af664e7f57ffb594",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-b52f64a721e847f48d7107682b1869d2",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-acac6936089b40058f636e0d22b81b8a",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-14b19676cd8b4d7391316add7c3ec57d",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-00e286b4915542c3af32d19bffb75c5f",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-68c5f2af23fd45dcaf2d9ac9b1c72fef",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-00811a92b2dd452bb02d550419cc6f9e",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-0a896e8cb2ef44a28c9264a7bcfb4625",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-d77ade73391d46349901d257eba16497",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-2db0b0a8d4824232bb89b5a7b4a18fc8",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-9a86ddc94bab41a7b826d1beb71dc663",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-379773c9248b4b039f9bef105d4f4e1e",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-d5be871e8c7048ebb11634f19547abe3",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-f2707b3056b74addbc8e872515898661",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-b6b15a41c06c4ed3bc7400ce430142d4",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-4aa88f2af331442d80eeaaa403ef3016",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-9f441ffcde184301bca8715750824387",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-6dc848fb27ab47b1a31e839c3f3de7a0",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-9cc9e843a11a4c6daec691d93609813e",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-f822de67e30e4087ac82971dd44735a8",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-d43244194dfe48298e6900e7a824d289",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-71ff3f1200534339a63b923e1669fb84",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-0fdebdacb4c04523be806be9164ea63a",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-cc27d42f0ac841a89d022d7d6a2c3309",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-4cd198bd551444cdadfdf51b3292978e",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-6445019f3c794ccab9f4b83a5344cc4d",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-91ac9f7cc951482ea62dae9e9b4eb804",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-6311972da5904bfd88619bf543f187b5",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-f7492f602d4a4f16a259acdb95f8191c",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-9016431d58334d73bc3663c11e8eeeb6",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-b218a0213515444c9965e0f5f7fc438b",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-4c1d9e02c2ad4f5192854e7eabf5c441",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-b6b4513331dc4d6aa21aaf9aac747273",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-a52069bcbb1e4005b64f97b2b7aa7ff0",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-923a8820e0e24a0c80f4e0ba7258b766",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-dd1035d8b86f495088e024e3302f70bb",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-43544a61d2d342baa0ed33b6b34c26c1",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-20cab834d20a4edb81c5a58c01ba50ee",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-dae192e2fcbe48a6b28c822b0968c84f",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-565e4369fde744b8aa4faf691ebcbd38",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-4546ee041e874d9d84dfa50a0ebb3001",
        "data": {
          "text": "up",
          "type": "physical-connection",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-2610957c0c4846cc8120c8253d12449b",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-90c86b49da7445328cd9aaa58d61caac",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-845da4d5a7ab4ba0b501a92098345f7c",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-5fc0c96fc22c43559f1f0db1d3f78514",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-f6f66a7de5f044c4b6b4983c07abd265",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "physical-cmdb_vs_physical_interface-d13319cf7ee44a5a898ec4f48f6f53f2",
        "data": {
          "text": "down",
          "type": "physical-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-d620ee5c9f084e2c9355cad630a0f11e",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-d620ee5c9f084e2c9355cad630a0f11e",
        "target": "physical-cmdb_vs_physical_interface-dfcf31dcddf649a78fabf5cfef228188",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-627af24789204554975d94231be24020",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-627af24789204554975d94231be24020",
        "target": "physical-cmdb_vs_physical_interface-c08e61db169148108b187b419b13d32a",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-dd3c4c6dcbec4191a33c732dfbd5a29e",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-dd3c4c6dcbec4191a33c732dfbd5a29e",
        "target": "physical-cmdb_vs_physical_interface-5a71507a33d241539efa9125344937b4",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-befbad796f16452197602fc9f0cffa29",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-befbad796f16452197602fc9f0cffa29",
        "target": "physical-cmdb_vs_physical_interface-c46607cba51645f5a0aa42e7a034b097",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-57e827c0552441fdb9f56242d6aa561e",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-57e827c0552441fdb9f56242d6aa561e",
        "target": "physical-cmdb_vs_physical_interface-da0328663bea444dbc833fa436a63639",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-95f635b3649d42dda72dfb93138ac268",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-95f635b3649d42dda72dfb93138ac268",
        "target": "physical-cmdb_vs_physical_interface-bb3a7fbb4d5a486bb71132e60cb252f0",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-1b1ca4ea2cf1402bb280ccea1052bf81",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-1b1ca4ea2cf1402bb280ccea1052bf81",
        "target": "physical-cmdb_vs_physical_interface-f2d937565d0243afa920cde799fa0c28",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-91d50880fbf2495b8568df6bd993c750",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-91d50880fbf2495b8568df6bd993c750",
        "target": "physical-cmdb_vs_physical_interface-a86985ce88fe4d19abad63ec51661a08",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-7d4ee5db78bb4a73be625c28d9db75a4",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-7d4ee5db78bb4a73be625c28d9db75a4",
        "target": "physical-cmdb_vs_physical_interface-818a5627d29a400184de88801c3c606e",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-dee6239188d9410a8206e46987ed7fd9",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-dee6239188d9410a8206e46987ed7fd9",
        "target": "physical-cmdb_vs_physical_interface-cefdbdbf3d304ed5af664e7f57ffb594",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-025b7ec2a4bb40999298c84d7c4dd5a4",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-025b7ec2a4bb40999298c84d7c4dd5a4",
        "target": "physical-cmdb_vs_physical_interface-b52f64a721e847f48d7107682b1869d2",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-c9223c2ad8074734965858680d124773",
        "data": {
          "text": "0 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-4edd9e329e0a45edb75a644d08f58267",
        "data": {
          "text": "0 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-ffe38d59d84041a399482a64da1ca8de",
        "data": {
          "text": "0 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-f3beb66a30c94042be133da6e8d3584a",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-f3beb66a30c94042be133da6e8d3584a",
        "target": "physical-cmdb_vs_physical_interface-acac6936089b40058f636e0d22b81b8a",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-d31975ccd6e94293b0d7783cc66e0a64",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-d31975ccd6e94293b0d7783cc66e0a64",
        "target": "physical-cmdb_vs_physical_interface-14b19676cd8b4d7391316add7c3ec57d",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-d90f6f730ba749e3a30ced4ce0eec29b",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-d90f6f730ba749e3a30ced4ce0eec29b",
        "target": "physical-cmdb_vs_physical_interface-00e286b4915542c3af32d19bffb75c5f",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-8109f51db82a46159b6c69135604324c",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-8109f51db82a46159b6c69135604324c",
        "target": "physical-cmdb_vs_physical_interface-68c5f2af23fd45dcaf2d9ac9b1c72fef",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-f53d79eceb704700a0467b4080189899",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-f53d79eceb704700a0467b4080189899",
        "target": "physical-cmdb_vs_physical_interface-00811a92b2dd452bb02d550419cc6f9e",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-26e7cc4b71134b15a0db29eb1231c55f",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-26e7cc4b71134b15a0db29eb1231c55f",
        "target": "physical-cmdb_vs_physical_interface-0a896e8cb2ef44a28c9264a7bcfb4625",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-b3ef2e93c6874f0e94d89b7a3475ef69",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-b3ef2e93c6874f0e94d89b7a3475ef69",
        "target": "physical-cmdb_vs_physical_interface-d77ade73391d46349901d257eba16497",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-1870dc0244bf4c51a9d96f4aa851b2ce",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-1870dc0244bf4c51a9d96f4aa851b2ce",
        "target": "physical-cmdb_vs_physical_interface-2db0b0a8d4824232bb89b5a7b4a18fc8",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-f673dd4da7274a83a26eb40056b78aac",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-f673dd4da7274a83a26eb40056b78aac",
        "target": "physical-cmdb_vs_physical_interface-9a86ddc94bab41a7b826d1beb71dc663",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-45f347bf6ba14434a00a1c11eb3e62d3",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-45f347bf6ba14434a00a1c11eb3e62d3",
        "target": "physical-cmdb_vs_physical_interface-379773c9248b4b039f9bef105d4f4e1e",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-8a6ecb4cc2a74a4c9e2f3ae68db9bb09",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-8a6ecb4cc2a74a4c9e2f3ae68db9bb09",
        "target": "physical-cmdb_vs_physical_interface-d5be871e8c7048ebb11634f19547abe3",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-a55b4681035346ba89a3d723ca3f3210",
        "data": {
          "text": "2 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-a55b4681035346ba89a3d723ca3f3210",
        "target": "physical-cmdb_vs_physical_interface-b6b15a41c06c4ed3bc7400ce430142d4",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-a55b4681035346ba89a3d723ca3f3210",
        "target": "physical-cmdb_vs_physical_interface-f2707b3056b74addbc8e872515898661",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-04f794b6aed248479c936ed1034ba491",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-04f794b6aed248479c936ed1034ba491",
        "target": "physical-cmdb_vs_physical_interface-4aa88f2af331442d80eeaaa403ef3016",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-8a93034cff0940c0a77c816191a95885",
        "data": {
          "text": "3 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-8a93034cff0940c0a77c816191a95885",
        "target": "physical-cmdb_vs_physical_interface-20cab834d20a4edb81c5a58c01ba50ee",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-8a93034cff0940c0a77c816191a95885",
        "target": "physical-cmdb_vs_physical_interface-dae192e2fcbe48a6b28c822b0968c84f",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-5a67c98a1ea74c2b91584dcd69c8cff9",
        "data": {
          "text": "1 members",
          "type": "trunk-connection",
          "status": "inactive"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-5a67c98a1ea74c2b91584dcd69c8cff9",
        "target": "physical-cmdb_vs_physical_interface-9f441ffcde184301bca8715750824387",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "switch-cmdb_virtual_switch-b05cd59c375246e5bc2499773ccb5b04",
        "target": "trunk-cmdb_vs_trunk_interface-a0ab47e1768a4740a22530507496cfd7",
        "data": {
          "text": "4 members",
          "type": "trunk-connection",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-a0ab47e1768a4740a22530507496cfd7",
        "target": "physical-cmdb_vs_physical_interface-6dc848fb27ab47b1a31e839c3f3de7a0",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      },
      {
        "source": "trunk-cmdb_vs_trunk_interface-a0ab47e1768a4740a22530507496cfd7",
        "target": "physical-cmdb_vs_physical_interface-9cc9e843a11a4c6daec691d93609813e",
        "data": {
          "text": "member",
          "type": "trunk-member",
          "status": "active"
        }
      }
    ]
  }
}