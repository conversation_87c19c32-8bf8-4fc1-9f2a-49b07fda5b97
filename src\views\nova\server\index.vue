<!-- 分类服务器 -->
<template>
  <div class="app-container">
    
      <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="queryParams.search"
            placeholder="输入名称、IP进行搜索"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetClick()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>


    <el-card shadow="never">
      <div class="mb-[10px]">
        <el-button type="success" @click="handleAddClick()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button
          type="danger"
          :disabled="ids.length === 0"
          @click="handleDelete()"
        >
          <i-ep-delete />
          删除
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        highlight-current-row
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <!-- <el-table-column type="expand" label="服务器项列表" width="100">
          <template #default="props">
            <el-table :data="props.row.dictItems">
              <el-table-column label="服务器项键" prop="name" width="200" />
              <el-table-column label="服务器项值" prop="value" align="center" />
              <el-table-column label="排序" prop="sort" align="center" />
            </el-table>
          </template>
        </el-table-column> -->
        <el-table-column label="名称" prop="name" />
        <el-table-column label="IP" prop="ipaddr" />
        <el-table-column label="状态">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 'ACTIVE'" type="success">运行中</el-tag>
            <div v-else-if="scope.row.status === '创建中' || scope.row.status === 'BUILD'" class="icon-center">
              {{ scope.row.status }} <el-icon class="rotate-icon" size="16px"><Loading /></el-icon>
            </div>
            <el-tag v-else-if="scope.row.status === 'SHUTOFF'" type='danger'>已关机</el-tag>
            <el-tag v-else-if="scope.row.status === 'ERROR'" type='danger' effect="plain">错误</el-tag>
            <el-tag v-else type='info'> {{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="规格" prop="flavor_name" />
        <el-table-column label="宿主机" prop="compute_host" />
        <el-table-column label="网络" prop="netname" />
        <el-table-column label="Key Pair" prop="key_name" />
        <!-- <el-table-column label="cpu" prop="vcpus" width="60"/>
        <el-table-column label="内存" prop="ram" width="100"/>
        <el-table-column label="系统盘" prop="disk" width="60"/>
        <el-table-column label="创建时间" prop="created_at" /> -->
        <el-table-column fixed="right" label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="handleEditClick(scope.row.id, scope.row.name)"
            >
              <i-ep-edit />
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click.stop="handleDelete(scope.row.id)"
            >
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!--服务器弹窗-->

    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="80%"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="computedRules"
        label-width="100px"
      >
        <el-card shadow="never">
          <el-row>
            <el-col :span="12">
              <el-form-item label="实例名" prop="name">
                <el-input v-model="formData.name" placeholder="请输入服务器名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="镜像" prop="image_name">
                <el-select v-model="formData.image_name" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in imageOptions" :key="index" :label="item.name" :value="item.image_id"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卷大小（GB）" prop="volume_size">
                <el-input v-model="formData.volume_size" placeholder="请输入系统盘大小" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="规格" prop="flavor_name">
                <el-select v-model="formData.flavor_name" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in flavorOptions" :key="index" :label="item.name" :value="item.name"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="网络" prop="netname">
                <el-select v-model="formData.netname" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in netOptions" :key="index" :label="item.name" :value="item.name"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="Key" prop="key_name">
                <el-select v-model="formData.key_name" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in keypairOptions" :key="index" :label="item.name" :value="item.name"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="安全组" prop="security_groups">
                <el-select v-model="formData.security_groups" multiple class="full-width-input" clearable>
                  <el-option v-for="(item, index) in sgOptions" :key="index" :label="item.name" :value="item.name"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </el-card>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitClick" :loading="loading">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import "/@/styles/index.scss";   // 基础样式
import { reactive, ref, computed, onMounted, onUnmounted,defineOptions } from "vue";
import { ElForm, ElInput, ElDrawer, ElButton, ElTag, ElMessage, ElMessageBox } from "element-plus";
import  Pagination from  "/@/components/Pagination/index.vue";

defineOptions({
  name: "Server",
  inherititems: false,
});

import ServerAPI, { ServerPageQuery, ServerPageVO, ServerForm, SecurityGroup } from "/@/api/cmdb/server";
import ImageAPI from "/@/api/cmdb/image";
import FlavorAPI from "/@/api/cmdb/flavor";
import SecurityGroupAPI from "../security_group/api";
import NetworkAPI from "../network/api";
import KeypairAPI from "../keypair/api";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const project_name = ref<string>("xingzai");
const user_id = ref<string>("a936b4cd05374cfb8af8317c6355b187");

const imageOptions = ref<any[]>([]);
const flavorOptions = ref<any[]>([]);
const sgOptions = ref<SecurityGroup[]>([]);
const netOptions = ref<any[]>([]);
const keypairOptions = ref<any[]>([]);

const queryParams = reactive<ServerPageQuery>({
  page: 1,
  limit: 10,
});

const tableData = ref<ServerPageVO[]>();

// 服务器弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

const formData = reactive<ServerForm>({});

const computedRules = computed(() => {
  const rules: Partial<Record<string, any>> = {
    name: [{ required: true, message: "请输入服务器名称", trigger: "blur" }],
    image_name: [{ required: true, message: "请选择镜像", trigger: "blur" }],
    volume_size: [{ required: true, message: "请输入卷大小", trigger: "blur" }],
    flavor_name: [{ required: true, message: "请选择规格", trigger: "blur" }],
    netname: [{ required: true, message: "请选择网络", trigger: "blur" }],
    security_groups: [{ required: true, message: "请选择安全组", trigger: "blur" }],
  };
  if (formData.volumeItems) {
    formData.volumeItems.forEach((attr, index) => {
      rules[`dictItems.${index}.name`] = [
        { required: true, message: "请输入服务器项名称", trigger: "blur" },
      ];
    });
  }
  return rules;
});


// 查询镜像列表
function getImageList() { 
  ImageAPI.getList()
    .then((data: any) => {
      imageOptions.value = data.results;
    });
}

// 查询Flavor列表
function getFlavorList() { 
  FlavorAPI.getList()
    .then((data: any) => {
      flavorOptions.value = data.results;
    });
}

// 查询安全组列表
function getSgList() { 
  // Todo： 项目名称替换成用户自己的
  SecurityGroupAPI.getList(project_name.value)   
    .then((data: any) => {
      sgOptions.value = data.results;
    });
}


// 查询网络列表
function getNetworkList() { 
  // Todo： 项目名称替换成用户自己的
  NetworkAPI.getList(project_name.value)   
    .then((data: any) => {
      netOptions.value = data.results;
    });
}

// 查询Keypair列表
function getKeypairList() { 
  // Todo： 项目名称替换成用户自己的
  KeypairAPI.getList(project_name.value, user_id.value)   
    .then((data: any) => {
      keypairOptions.value = data.results;
    });
}


// 查询
function handleQuery() {
  loading.value = true;
  ServerAPI.getPage(queryParams)
    .then((data: any) => {
      tableData.value = data.data;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetClick() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

// 行选择
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 新增服务器
function handleAddClick() {
  dialog.visible = true;
  dialog.title = "新增服务器";
}

/**
 * 编辑服务器
 *
 * @param id 服务器ID
 */
function handleEditClick(id: number, name: string) {
  dialog.visible = true;
  dialog.title = "【" + name + "】服务器修改";
  ServerAPI.getFormData(id).then((data: any) => {
    Object.assign(formData, data);
  });
}

// 提交服务器表单
function handleSubmitClick() {
  dataFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      loading.value = true;
      const id = formData.insid;
      if (id) {
        ServerAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        ServerAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });


}

/** 关闭服务器新增、编辑弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.insid = undefined;
  formData.volumeItems = [];
}
/**
 * 删除服务器
 *
 * @param id 服务器ID
 */
function handleDelete(id?: number) {
  const attrGroupIds = [id || ids.value].join(",");
  if (!attrGroupIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      ServerAPI.deleteByIds(attrGroupIds).then(() => {
        ElMessage.success("删除成功");
        handleResetClick();
      });
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

/** 新增Volume项 */
// function handleAddAttrClick() {
//   formData.volumeItems = formData.volumeItems ?? [];
//   formData.volumeItems.push({ sort: 1, status: 1 });
// }

/** 删除服务器项 */
function handleDeleteAttrClick(index: number) {
  if (formData.volumeItems && formData.volumeItems.length > 0) {
    formData.volumeItems.splice(index, 1);
  }
}

// 自动获取接口
function fetchData() {
  ServerAPI.getPage(queryParams)
    .then((data: any) => {
      tableData.value = data.data;
      total.value = data.total;
    })
}


  // 设置定时器，每隔固定时间刷新表格
  const timer = setInterval(fetchData, 10000); // 例如，每 10 秒刷新一次

onMounted(() => {
  handleQuery();
  getImageList();
  getFlavorList();
  getSgList();
  getNetworkList();
  getKeypairList();
});

// 组件卸载时清除定时器
onUnmounted(() => {
  clearInterval(timer);
});
</script>


<style>
.rotate-icon {
  animation: rotate 2s infinite linear;
  top: 3px;
}
 
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>