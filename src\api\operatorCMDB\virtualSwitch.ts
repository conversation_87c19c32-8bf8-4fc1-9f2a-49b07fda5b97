import { request } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/operatorcmdb/virtual_switch/';

export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

export function GetListByIds(query: UserPageQuery) {
	return request({
		url: apiPrefix + 'get_list_by_ids/',
		method: 'get',
		params: query,
	});
}

export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

export function AddObj(obj: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

export function UpdateObj(obj: EditReq) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export function DelObj(id: DelReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

export function DelObjs(obj: any) {
	return request({
		url: apiPrefix + 'multiple_delete/',
		method: 'delete',
		data: obj,
	});
}

// 同步交换机信息
export function SyncSwitchData(obj: any) {
	return request({
		url: apiPrefix + obj.id + '/sync_interfaces/',
		method: 'post',
		data: obj,
	});
}
