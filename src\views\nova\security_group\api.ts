
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/sg";

class SecurityGroupAPI {
  /**
   * 获取安全组分页列表
   *
   * @param queryParams 查询参数
   * @returns 安全组分页结果
   */
  static getPage(queryParams: SecurityGroupPageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取安全组表单数据
   *
   * @param id 安全组ID
   * @returns 安全组表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }



  /**
   * 获取安全组列表
   *
   * @returns 安全组列表
   */
  static getList(project_name?: string) {
    let url = `${BASE_URL}/list/`
    if (project_name) {
      url += `?project_name=${project_name}`;
    }
    return request({
      url: url,
      method: "get",
    });
  }

  /**
   * 获取安全组的数据项
   *
   * @param typeCode 安全组编码
   * @returns 安全组数据项
   */
  static getOptions(code: string) {
    return request({
      url: `${BASE_URL}/${code}/options`,
      method: "get",
    });
  }
}

export default SecurityGroupAPI;

/**
 * 安全组查询参数
 */
export interface SecurityGroupPageQuery extends PageQuery {
  search?: string;    // keywords
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}

/**
 * 安全组分页数组中的单个对象
 */
export interface SecurityGroupPageVO {
  sg_id: number;
  name: string;
  description: string;
  is_shared: number;
  project_id: string;
  project_name: string;
  security_group_rules: string;
}

/**
 * 安全组表单
 */
export interface SecurityGroupForm {
  sg_id?: string;
  name?: string;
  description?: string;
  is_shared?: number;
  project_id?: string;
  project_name?: string;
  security_group_rules: string;
}
