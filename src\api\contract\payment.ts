import { request,downloadFile } from '/@/utils/service';
import { UserPageQuery, AddReq,InfoReq,DelReq,EditReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/contract/payment/';

export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}
export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + "detail_info" + '/',
    method: 'get',
    params: {"id":id},
	});
}

export function SaveObj(obj: AddReq) {
	return request({
		url: apiPrefix + "save" + "/",
		method: 'post',
		data: obj,
	});
}

export function GenerateObj(obj: AddReq) {
	return request({
		url: apiPrefix + "generate" + "/",
		method: 'post',
		data: obj,
	});
}

export function DelObj(id: DelReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

export function exportData(params:any){
  return downloadFile({
      url: apiPrefix + 'export_all_payments/',
      params: params,
      method: 'get',
  })
}
