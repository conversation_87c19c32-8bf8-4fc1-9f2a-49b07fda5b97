<template>
  <el-transfer
    v-bind="$attrs"
    v-model="selectedKeys"
    v-on="listeners"
    :data="transferData"
    :titles="['Available', 'Selected']"
  />
</template>
<script lang="ts">
import { defineComponent, ref, watch, computed } from 'vue';
// 导入TransferDataItem类型
import { TransferDataItem } from 'element-plus/es/components/transfer/src/transfer'; 

// interface Props {
//   value: string[];
//   data: TransferDataItem[];
// }

export default defineComponent({
  name: 'CustomFSELTransfer',
  inheritAttrs: false, // 确保不会覆盖组件内部的属性
  props: {
    value: {
      type: Array as () => string[],
      default: () => [],
    },
    data: {
      type: Array as () => TransferDataItem[],
      default: () => [],
      required: true,
    },
  },
  emits: ['update:value', 'change'], // 明确声明可以发出的事件
  setup(props, context) {
    console.warn('props:', props)
    console.warn('context:', context)
    const selectedKeys = ref(props.value);

    const transferData = computed(() => {
      return props.data.map(item => ({
        key: item.id.toString(), // 假设id是每个项目的唯一标识符
        label: item.label, // 使用现有的label属性
        disabled: item.disabled || false, // 可以根据需要设置disabled
      }));
    });
    const listeners = computed(() => context.attrs) // 获取所有绑定的事件监听器
    // const onTransferChange = (value: string[], direction: string, movedKeys: string[]) => {
    //   ElMessage.info(`Change to ${direction}, keys: ${movedKeys.join(', ')}`);
    //   context.emit('update:value', value); // 使用正确的事件名称
    // };

    watch(selectedKeys, (newVal) => {
      context.emit('change', newVal);
    });
    return {
      selectedKeys,
      transferData,
      // onTransferChange,
      listeners,
    };
  },
});
</script>