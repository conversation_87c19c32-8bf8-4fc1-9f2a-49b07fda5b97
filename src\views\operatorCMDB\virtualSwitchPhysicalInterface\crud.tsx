import * as api from '/@/api/operatorCMDB/virtualSwitchTrunkInterface';
import {
	UserPageQuery,
	AddReq,
	DelReq,
	EditReq,
	CreateCrudOptionsProps,
	CreateCrudOptionsRet,
	dict,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, h } from 'vue';
import { useRouter } from 'vue-router';

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const router = useRouter();
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};

	let selectedIds = ref([]);
	const onSelectionChange = (changed: any) => {
		selectedIds.value = changed.map((item: any) => item.id);
	};

	return {
		selectedIds,
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			actionbar: {
				buttons: {
					add: {
						show: auth('operatorcmdb:virtualSwitchTrunkInterface:Create'),
						plain: true,
						type: 'primary',
					},
					selectionsDeleted: {
						text: '批量删除',
						type: 'danger',
						plain: true,
						show: auth('operatorcmdb:virtualSwitchTrunkInterface:MultipleDelete'),
						click: (): void => {
							if (selectedIds.value.length === 0) {
								ElMessage.warning('请先勾选')
								return
							}
							ElMessageBox.confirm(
								h('p', null, [
									h('span', null, '确定删除 '),
									h('i', { style: 'color: red' }, selectedIds.value.length),
									h('span', null, ' 个记录吗？'),
								]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.DelObjs({ 'keys': selectedIds.value }).then(
										(response: any) => {
											if (response.code === 2000 && response.msg === '删除成功') {
												ElMessage.success('删除成功')
											} else {
												ElMessage.error('删除失败')
											}
										}
									)
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消删除',
									})
								})
						},
					}
				},
			},
			rowHandle: {
				fixed: 'right',
				width: 200,
				buttons: {
					view: {
						show: auth('operatorcmdb:virtualSwitchTrunkInterface:Retrieve'),
						type: 'primary',
						link: true,
					},
					edit: {
						link: true,
						type: 'primary',
						show: auth('operatorcmdb:virtualSwitchTrunkInterface:Update'),
					},
					remove: {
						link: true,
						type: 'danger',
						show: auth('operatorcmdb:virtualSwitchTrunkInterface:Delete'),
					},
					viewLog: {
						type: 'primary',
						text: '查看日志',
						link: true,
						show: auth('system:auditLog:GetResourceLogs'),
						click(context) {
							router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
						}
					},
				},
			},
			pagination: {
				show: true,
				'default-page-size': 10,
				'default-current': 1,
			},
			table: {
				rowKey: 'id',
				onSelectionChange,
			},
			form: {
				labelWidth: 120,
				row: { gutter: 20 },
			},
			columns: {
				$checked: {
					title: "选择",
					form: { show: false },
					column: {
						type: "selection",
						align: "center",
						width: "55px",
					}
				},
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
					},
				},
				virtual_switch: {
					title: '虚拟交换机',
					search: {
						show: true,
					},
					column: {
						minWidth: 180,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/operatorcmdb/virtual_switch/get_list_by_ids/?is_all=true',
						value: 'id',
						label: 'name',
						cache: false,
					}),
					form: {
						rules: [
							{ required: true, message: '虚拟交换机必填项' },
						],
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							placeholder: '请选择虚拟交换机',
						},
					},
				},
				name: {
					title: '接口名称',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 150,
						showOverflowTooltip: true,
						sortable: 'custom',
					},
					form: {
						rules: [
							{ required: true, message: '接口名称必填项' },
							{ max: 255, min: 1, message: '最大: 255, 最小: 1', trigger: 'blur' }
						],
						component: {
							placeholder: '请输入接口名称',
						},
					},
					speed: {
						title: '速度(Mbps)',
						search: {
							show: false,
						},
						column: {
							minWidth: 120,
						},
						type: 'number',
						form: {
							component: {
								placeholder: '请输入速度',
							},
						},
					},
					state: {
						title: '状态',
						search: {
							show: true,
						},
						column: {
							minWidth: 100,
						},
						type: 'input',
						form: {
							rules: [
								{ max: 30, message: '最大长度: 30', trigger: 'blur' }
							],
							component: {
								placeholder: '请输入状态',
							},
						},
					},
					line_state: {
						title: '协议状态',
						search: {
							show: true,
						},
						column: {
							minWidth: 100,
						},
						type: 'input',
						form: {
							rules: [
								{ max: 30, message: '最大长度: 30', trigger: 'blur' }
							],
							component: {
								placeholder: '请输入协议状态',
							},
						},
					},
					mac_address: {
						title: 'MAC地址',
						search: {
							show: true,
						},
						column: {
							minWidth: 150,
						},
						type: 'input',
						form: {
							rules: [
								{ max: 64, message: '最大长度: 64', trigger: 'blur' }
							],
							component: {
								placeholder: '请输入MAC地址',
							},
						},
					},
					last_sync_at: {
						title: '最后同步时间',
						search: {
							show: false,
						},
						column: {
							minWidth: 150,
							sortable: 'custom',
						},
						type: 'datetime',
						form: {
							show: false,
						},
					},
					description: {
						title: '描述',
						search: {
							show: true,
						},
						type: 'textarea',
						column: {
							minWidth: 200,
							showOverflowTooltip: true,
						},
						form: {
							component: {
								placeholder: '请输入描述',
							},
						},
					},
				},
			},
		},
	};
};