<template>
  <div class="container-list">
    <div class="contain">
      <div class="contain-head">
        <span>{{ title }}</span>
        <hr/>
        <div class="contain-img">
          <img
              v-if="setCollapse"
              src="/@/assets/img/collapse.png"
              alt="collapse"
              @click="toggleCollapse"
          />
          <img
              v-if="!setCollapse"
              src="/@/assets/img/expand.png"
              alt="expand"
              @click="toggleCollapse"
          />
        </div>
        <div class="contain-text">
          {{ setCollapse ? '展开' : '收起' }}
        </div>
      </div>
      <tiny-form
          :model="filterOptions"
          label-position="right"
          label-width="100px"
          class="filter-form"
          size="small"
      >
        <tiny-row :flex="true" justify="center" class="col" :key="rowIndex" v-for="(row, rowIndex) in rows">
          <tiny-col
              v-for="field in row"
              :key="field.key"
              :span="24 / colsPerRow"
              label-width="100px"
          >
            <tiny-form-item :label="field.label">
              <tiny-input v-if="field.component === 'tiny-input'" v-model="filterOptions[field.key]" :placeholder="field.placeholder">
              </tiny-input>
              <tiny-select v-if="field.component === 'tiny-select'"
                    v-model="filterOptions[field.key]"
                    :options="field.options"
                ></tiny-select>
            </tiny-form-item>
          </tiny-col>
        </tiny-row>

        <tiny-row :flex="true" justify="end" class="col">
          <tiny-col :span="4">
            <tiny-form-item>
              <div class="search-btn">
                <tiny-button type="primary" @click="reloadGrid">
                  查询
                </tiny-button>
                <tiny-button @click="handleFormReset">
                  重置
                </tiny-button>
              </div>
            </tiny-form-item>
          </tiny-col>
        </tiny-row>
      </tiny-form>
      <div class="bottom-line">
        <hr/>
      </div>
      <tiny-fullscreen
          :teleport="true"
          :page-only="true"
          :z-index="999"
          :fullscreen="fullscreen"
          @update:fullscreen="toggleFullscreen"
      >
        <div class="tiny-fullscreen-scroll">
          <div class="tiny-fullscreen-wrapper">
            <tiny-grid
                ref="taskGrid"
                :fetch-data="fetchDataOption"
                :pager="pagerConfig"
                :loading="loading"
                size="medium"
                :auto-resize="true"
            >
              <template #toolbar>
                <tiny-grid-toolbar>
                  <template #buttons>
                    <div class="btn">
                      <tiny-button @click="exportCsv">
                        导出
                      </tiny-button>
                      <div class="screen">
                        <img
                            v-if="!fullscreen"
                            src="/@/assets/img/screen-out.png"
                            class="screen-image"
                            @click="toggleFullscreen"
                        />
                        <img
                            v-if="fullscreen"
                            src="/@/assets/img/screen-in.png"
                            class="screen-image"
                            @click="toggleFullscreen"
                        />
                        <span @click="toggleFullscreen">
                          {{ fullscreen ? '关闭' : '全屏' }}
                        </span>
                      </div>
                    </div>
                  </template>
                </tiny-grid-toolbar>
              </template>

              <tiny-grid-column
                  v-for="column in columns"
                  :key="column.field"
                  :field="column.field"
                  :title=column.title
                  align="center"
              >
                <template v-if="column.type === 'status'" #default="{ row }">
                  <span
                      class="status"
                      :class="{
                      'status-closed': row.status === '0',
                      'status-finished': row.status === '1',
                    }"
                  >
                    <span class="status-dot"></span>
                    <span class="status-text">
                      {{ getStatusText(row.status) }}
                    </span>
                  </span>
                </template>
                <template v-if="column.type === 'operation'" #default="data">
                  <a class="operation" @click="handleDelete(data.row.id)">
                    删除
                  </a>
                </template>
              </tiny-grid-column>
            </tiny-grid>
          </div>
        </div>
      </tiny-fullscreen>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, toRefs } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyGridToolbar,
  TinyForm,
  TinyFormItem,
  TinyInput,
  TinyButton,
  TinyRow,
  TinyCol,
  TinySelect,
  TinyPager,
  TinyFullscreen,
  Modal,
} from '@opentiny/vue';
import { GetList, DelObj } from '/@/api/tenant/account';

interface Props {
  title: string;
  colsPerRow: number;  // 每行展示的字段数量
  api: Function;
  formFields: Array<{ key: string; label: string; component: string; placeholder?: string; options?: any}>;
  columns: Array<{ field: string; title: string; type?: string }>;
  statusOptions: Array<{ value: string; label: string }>;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  colsPerRow: 3,
  api: () => Promise.resolve({}),
  formFields: () => [],
  columns: () => [],
  statusOptions: () => [],
  
});

const state = reactive<{
  loading: boolean;
  filterOptions: Record<string, any>;
}>({
  loading: false,
  filterOptions: {},
});

const pagerConfig = reactive({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20],
    total: 0,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});


// 计算每行的字段
const rows = computed(() => {
  const numRows = Math.ceil(props.formFields.length / props.colsPerRow);
  const result: any[][] = [];

  for (let i = 0; i < numRows; i++) {
    result.push(props.formFields.slice(i * props.colsPerRow, (i + 1) * props.colsPerRow));
  }

  return result;
});


let tableData = ref([]);
const taskGrid = ref();
const { loading, filterOptions } = toRefs(state);

const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
 
  const queryParmas = {
    searchInfo: rest,
    ...params,
  };

  state.loading = true;
  try {
    console.error(queryParmas)
    const response = await props.api(queryParmas);
    const { data, total } = response;
    tableData.value = data;
   
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    console.error('page', page)
    const { currentPage, pageSize } = page;
    return fetchData({ page: currentPage, limit: pageSize });
  },
});

const handleDelete = (id: string) => {
  DelObj(id).then(() => {
    Modal.message({
      message: '已删除',
      status: 'success',
    });
  });
};

const getStatusText = (status: string) => {
  return props.statusOptions.find(({ value }) => status === value)?.label || '';
};

const reloadGrid = () => {
  taskGrid?.value.handleFetch('reload');
  fetchData();
};

const handleFormReset = () => {
  state.filterOptions = {};
  reloadGrid();
};

const setCollapse = ref(true);
const toggleCollapse = () => {
  setCollapse.value = !setCollapse.value;
};

const exportCsv = () => {
  taskGrid.value.exportCsv({
    filename: 'table.csv',
    original: true,
    isHeader: false,
    data: tableData.value,
  });
};

const fullscreen = ref(false);
const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value;
};
</script>

<style scoped lang="less">
@import './search-table.less';
</style>