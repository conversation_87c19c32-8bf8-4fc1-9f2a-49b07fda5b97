<template>
  <div>
    <tiny-grid ref="selectSoftwareGrid" :data="tableData" border :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @select-change="handleSelectChange" header-align="center"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }">
      <tiny-grid-column type="index" width="60"></tiny-grid-column>
      <tiny-grid-column type="selection" width="60"></tiny-grid-column>
      <tiny-grid-column type="expand" title="操作" width="60">
        <template #default="data">
          <tiny-grid :data="toStringJSON(data.row.options)">
            <tiny-grid-column field="label" title="参数名称" sortable></tiny-grid-column>
            <tiny-grid-column field="selected_value" title="值" :edit="{}">
              <template #edit="data">
                <tiny-select v-model="data.row.selected_value" searchable clearable>
                  <tiny-option v-for="sub in data.row.options" :key="sub.value" :label="sub.label" :value="sub.value">
                  </tiny-option>
                </tiny-select>
              </template>
              <template #default="data">
                <tiny-select v-model="data.row.selected_value">
                  <tiny-option v-for="sub in data.row.options" :key="sub.value" :label="sub.label" :value="sub.value">
                  </tiny-option>
                </tiny-select>
              </template>
            </tiny-grid-column>
          </tiny-grid>
        </template>

      </tiny-grid-column>
      <tiny-grid-column field="name" title="软件名称" :filter="nameFilter"></tiny-grid-column>
      <tiny-grid-column field="description" title="描述"></tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted } from 'vue';
import { toStringJSON } from 'xe-utils';
import {
  TinyGrid,
  TinyGridColumn,
  TinyInput,
  TinyPager,
  TinyTag,
  TinySelect,
  TinyOption,
  TinyTooltip,
} from '@opentiny/vue';
import { GetList, GetAllSoftwaress } from '/@/api/operatorCMDB/fastInstallSoftware';
import { iconHelp } from '@opentiny/vue-icon';
import softwarePackagesImg from '/@/assets/img/software-packages.svg';

interface SoftwareItem {
  id: string;
  name: string;
  key: string;
  current_version: string;
  enabled: boolean;
}
const TinyIconHelp = iconHelp();

const props = defineProps({
  currentSelectSoftwarees: {
    type: Array,
    required: false,
    default: () => []
  }
});


// 当前选中值
let currentSelectSoftwares = ref<Array<SoftwareItem>>([]);

const emit = defineEmits(['update:currentSelectSoftwarees']);

// TODO: implement update,未来可能要用
// eslint-disable-next-line no-unused-vars
const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',  
  layout: 'input,enum,default,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})

// 初始化请求数据
interface FilterOptions {
  name: string;
  enabled: boolean;
  id: string;
  current_version: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    enabled: true,
    name: '',
    current_version: '',
    id: '',
  },
});
const allSoftwaressData = ref([]);
let tableData = ref<Array<SoftwareItem>>([]);

const selectSoftwareGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectSoftwareGrid?.value.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  state.loading = true;
  try {
    const response = await GetAllSoftwaress(queryParmas);
    const { data, total } = response;
    tableData.value = data;
    // 如果有初始选中的 ID，设置默认选中行     
    // if (props.currentSelectSoftwarees) {
    //   const selectedRows = tableData.value.filter(row => props.currentSelectSoftwarees.some(selected => selected.id === row.id));
    //   if (selectedRows) {
    //     // 更新选中行的 current_version        
    //     tableData.value.forEach(row => {
    //       const selected = props.currentSelectSoftwarees.find(selected => selected.id === row.id);
    //       if (selected) {
    //         row.current_version = selected.current_version;
    //       }
    //     });

    //     selectSoftwareGrid.value?.setSelection(selectedRows, true);
    //     currentSelectSoftwares.value = selectedRows;
    //   }
    // }
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置  
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤    
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    enabled: true,
    name: '',
    current_version: '',
    id: '',
  };
  // reloadGrid();
}

const handleSelectChange = () => {
  console.error('asdsadadaaddsa')
  // 获取选中记录数组（自动处理多选情况）
  const selectedRecords = selectSoftwareGrid.value.getSelectRecords();
  console.error(selectedRecords)
  // 关闭所有行（使用安全导航操作符）
  selectSoftwareGrid.value.clearRowExpand();
  // 展开选中行（使用安全导航操作符）
  selectSoftwareGrid.value.setRowExpansion(selectedRecords, true);

  // 提取ID字段数组（使用安全导航操作符）
  const selectedIds = selectedRecords?.map((item: { id: string; }) => item.id) || []

  // 同步到父组件（传递数组格式）
  emit('update:currentSelectSoftwarees', selectedRecords)

  // 可选：格式化的选中信息展示（根据需求）
  currentSelectSoftwares.value = selectedRecords || []
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any) => {
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();
  }
  // 更新 filterOptions  
  if (filters.filters.name && filters.filters.name.type === 'input') {
    filterOptions.value.name = filters.filters.name.value.text;
  }
  reloadGrid();
}

const getAllSoftwaress = async () => {
  // 所有验证通过后，调用 createBaremetalServerObj 函数  
  try {
    const taskResponse = await GetAllSoftwaress({});
    allSoftwaressData.value = taskResponse.data
  } catch (error) {
    // TOD  
  }
};

onMounted(async () => {
  try {
    await Promise.all([
      // 获取裸机节点      
      fetchData(),
      // getAllSoftwaress(),    
    ])
    // 所有异步操作完成后，设置加载状态为 false  
  } catch {
    // TOD  
  }

});

</script>