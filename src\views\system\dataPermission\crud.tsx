import * as api from '/@/views/system/api/role';
import {
  dict,
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  // useFs,
  compute,
  // CreateCrudOptionsProps,
  CrudOptions,
  CrudExpose
} from '@fast-crud/fast-crud';
import { dictionary } from '/@/utils/dictionary';
// import { successMessage } from '/@/utils/message';
import { auth } from "/@/utils/authFunction";
import { modelsDataDict } from '../../shardDict/shard-dict';
import { successMessage } from '../../../utils/message';
import { pinyin } from 'pinyin-pro';
import { useRouter } from 'vue-router';

interface CreateCrudOptionsTypes {
  output: any;
  crudOptions: CrudOptions;
}

// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({crudExpose}: {
  crudExpose: CrudExpose;
  handleDataPermisson: Function;
}):CreateCrudOptionsTypes {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    // 仅查询默认的【数据权限】类角色
    query['type'] = 1
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('role:Create'),
            plain: true,
          }
        }
      },
      form: {
        labelWidth: 120
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            show: false,
          },
          edit: {
            type: 'primary',
            link: true,
            show: auth('role:Update')
          },
          
          dataPermission: {
            text: '配置数据',
            show: auth('role:Permission'),
            link: true,
            type: 'primary',
            click(scope) {
              router.push({
                path: "/system/dataPermission/config",
                query: {
                  'roleId': scope.row.id,
                  'roleName': scope.row.name,
                }
              })
            },
        },
        remove: {
          type: 'danger',
          link: true,
          order: 3,
          show: auth('role:Delete')
        },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        name: {
          title: '数据组名称',
          type: 'text',
          search: {show: true},
          column: {
              minWidth: 120,
              sortable: 'custom',
          },
          form: {
              rules: [{required: true, message: '角色名称必填'}],
              // [TODO] 取消告警
              // @ts-ignore
              valueChange({form, value}) {
                form.key = "data-perm-" + pinyin(value, { toneType: 'none', type: 'array' }).flat().join('') // 获取角色名转换为pinyin并同时更新角色的 key
              },
              component: {
                  placeholder: '请输入角色名称',
              },

          },
      },
      description: {
        title: '描述',
        search: {
          show: true,
        },
        type: 'textarea',
        column: {
          minWidth: 90,
        },
        form: {
          rules: [
            // 表单校验规则
          ],
          component: {
            placeholder: '请输入描述',
          }
        }
      },
      key: {
          title: '权限标识',
          type: 'text',
          search: {show: false},
          column: {
            show: false,
            minWidth: 120,
          },
          form: {
            show: false,
            rules: [{required: true, message: '权限标识必填'}],
            component: {
                placeholder: '输入权限标识',
            },
          },
      },
      type: {
        title: "角色类型",
          search: { show: false },
          type: "number",
          dict: modelsDataDict,
          form: {
            show: false,
            value: 1
					},
          column: {
            show: false,
          }
      },
      status: {
        title: '状态',
        search: {show: true},
        type: 'dict-radio',
        form: {
          rules: [{required: true, message: '权限标识必填'}],
          value: true,
          component: {
            placeholder: '输入权限标识',
          },
        },
        column: {
          width: 100,
          component: {
          name: 'fs-dict-switch',
          activeText: '',
          inactiveText: '',
          style: '--el-switch-on-color: var(--el-color-primary); --el-switch-off-color: #dcdfe6',
          onChange: compute((context) => {
              return () => {
                  api.UpdateObj(context.row).then((res: APIResponseData) => {
                      successMessage(res.msg as string);
                  });
              };
          }),
          },
        },
        dict: dict({
          data: dictionary('button_status_bool', undefined),
        }),
        label: 'label',
        value: 'value',
      },
        // user: {
        //   title: '用户ID',
        //   search: {
        //     show: false,
        //   },
        //   type: 'input',
        //   column: {
        //     minWidth: 90,
        //   },
        //   form: {
        //     show: false,
        //     rules: [
        //       // 表单校验规则
        //       {required: true, message: '用户ID必填项'},
        //     ],
        //     component: {
        //       placeholder: '请输入用户ID',
        //     }
        //   },
        // },
      },
    },
  };
};
