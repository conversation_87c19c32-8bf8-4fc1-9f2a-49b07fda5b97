
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/flavor";

class FlavorAPI {
  /**
   * 获取规格分页列表
   *
   * @param queryParams 查询参数
   * @returns 规格分页结果
   */
  static getPage(queryParams: FlavorPageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取规格表单数据
   *
   * @param id 规格ID
   * @returns 规格表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }



  /**
   * 获取规格列表
   *
   * @returns 规格列表
   */
  static getList() {
    return request({
      url: `${BASE_URL}/list`,
      method: "get",
    });
  }

  /**
   * 获取规格的数据项
   *
   * @param typeCode 规格编码
   * @returns 规格数据项
   */
  static getOptions(code: string) {
    return request({
      url: `${BASE_URL}/${code}/options`,
      method: "get",
    });
  }
}

export default FlavorAPI;

/**
 * 规格查询参数
 */
export interface FlavorPageQuery extends PageQuery {
  search?: string;    // keywords
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}


/**
 * 规格分页对象
 */
export interface FlavorPageVO {
  id: number;
  name: string;
  flavor_id: string;
}

export interface FlavorForm {
  flavor_id?: number;
  name?: string;
  ram?: number;
  vcpus?: number;
  disk?: number;
}
