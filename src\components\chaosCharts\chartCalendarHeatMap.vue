<template>
  <tiny-card custom-class="stat-cards" type="text">
      <tiny-chart-heatmap :options="options" :extend="extChart" :toolbox="toolbox"></tiny-chart-heatmap>
  </tiny-card>

</template>

<script lang="ts" setup name="ChartCalendarHeatMap">
import { ref, watch, PropType } from 'vue';
import { TinyChartHeatmap, TinyCard, } from '@opentiny/vue';

const props = defineProps({
  data: {
    type: Array<any>,
    required: true,
    default() {
      return []
    },
  },
  config: {
    type: Object as PropType<{
      title: string;
      subTitle: string;
      xColumn: string;
    }>,
    required: true,
    default() {
      return {
        title: '',
    subTitle: '',
    xColumn: '',
      }
    },
  }
});

const extChart = ref({
  title: {
          text: props.config.title, 
          subtext: props.config.subTitle, 
          // sublink: 'subLink', 
          left: 'left',
          top: 'left',
          textStyle: {
            "margin-bottom": 20,
            fontSize: 16,
            fontWeight: 'bold'
          }
        }
});

const toolbox = ref({
  feature: {
    dataView: {},
    saveAsImage: {}
  }
})

// let options: any = ref({
//   data: props.data,
//   label: {
//     show: true,
//     position: 'right',
//     offset: [6, 0],
//     formatter: (params: any) => {
//             if (!params.data)  return '';
//             return `${params.value}`; 
//           },
//   },
//   xAxis: {
//     data: props.config.xColumn
//   },
//   direction: 'horizontal' // 横向柱状图
// })
const options = ref({
  tooltip: {
        formatter: (params, ticket, callback) => {
            const color = params.color;
            const data = params.data;
            const [x, y, z, ...others] = data;
            let htmlString = '<div style="margin-bottom:4px;">数量</div>';
            htmlString +=
                '<div style="margin-bottom:4px;">' +
                '<span style="display:inline-block;width:10px;height:10px;margin-right:8px;border-radius:5px;border-style:solid;border-width:1px;border-color:' + color + ';background-color:' + color + ';"></span>' +
                '<span style="display:inline-block;margin-right:8px;min-width:60px;">Value</span>' +
                '<span>' + z + '</span>' +
                '</div>';
            return htmlString
        },
    },
  // 图表类型 (矩形热力图)
  theme: 'cloud-light',
  type: 'CalendarHeatMapChart',
  showLabel: true,
  
  // padding 控制图表距离容器的上，右，下，左 padding 值
  padding: [50, 120, 20, 20],
  // 矩形的颜色，默认值'#1F55B5'
  color: '#1F55B5',

  // 视觉滑块的配置，本属性显示视觉滑块，不传不显示
  // handle: {
  //   // 是否反转显示文本，默认值 false
  //   inverse: true,

  //   // 两端的文本，默认值为 data 第三个属性值的最大值和最小值
  //   text: ['亮', '暗'],
  //   // 水平与垂直放置，默认值 vertical
  //   // vertical 或者 horizontal
  //   orient: 'vertical',

  //   // 是否显示手柄，默认值不显示
  //   calculable: true,

  //   position: {
  //     bottom: '6%',
  //     right: '4%'
  //   }
  // },
  data: [
    { network_class: '10G', gpu_model: 'A800-PCle', value: 0 },
    { network_class: '25G', gpu_model: 'A800-PCle', value: 0 },
    { network_class: '25G', gpu_model: '4090', value: 1 },
  ],
  xAxis: {
    name: '网络类型',
    // data: 'network_class',
  },
  yAxis: {
    name: 'GPU类型',
    // data: 'gpu_model',
  }
})

// // Watch for props changes and update chart options 
// watch(() => props.data, () => {
//   options.value.data = props.data
// }, { deep: true });


// // Watch for props changes and update chart options 
// watch(() => props.config, () => {
//   options.value.xAxis.data = props.config.xColumn
// }, { deep: true });
</script>
<style scoped>
.dashboard-sub-title {
  font-size: 16px;
  margin-bottom: 20px;
}

/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px);
  /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important;
  /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>