{"nodes": [{"id": "physical_server_1", "type": "physical_server", "name": "Dell R740", "serial_number": "SVR-2023-001", "cpu": "Intel Xeon Gold 6248R", "memory": "256GB", "location": "Rack A-01", "link": "http://baidu.com"}, {"id": "physical_server_2", "type": "physical_server", "name": "HPE DL380", "serial_number": "SVR-2023-002", "cpu": "AMD EPYC 7763", "memory": "512GB", "location": "Rack A-02", "link": "http://baidu.com"}, {"id": "bare_metal_node_1", "type": "bare_metal_node", "name": "BMN001", "hostname": "bmn001.example.com", "ipmi_address": "*************", "status": "active", "link": "http://baidu.com"}, {"id": "bare_metal_node_2", "type": "bare_metal_node", "name": "BMN002", "hostname": "bmn002.example.com", "ipmi_address": "*************", "status": "active", "link": "http://baidu.com"}, {"id": "bare_metal_server_1", "type": "bare_metal_server", "name": "BMS001", "hostname": "bms001.example.com", "os": "CentOS 7.9", "cpu_cores": 48, "memory": "256GB", "link": "http://baidu.com"}, {"id": "bare_metal_server_2", "type": "bare_metal_server", "name": "BMS002", "hostname": "bms002.example.com", "os": "Ubuntu 20.04", "cpu_cores": 64, "memory": "512GB", "link": "http://baidu.com"}, {"id": "disk_1_1", "type": "disk", "name": "Disk 1", "server_id": "bare_metal_server_1", "capacity": "2TB", "mount_point": "/data", "link": "http://baidu.com"}, {"id": "disk_1_2", "type": "disk", "name": "Disk 2", "server_id": "bare_metal_server_1", "capacity": "4TB", "mount_point": "/storage", "link": "http://baidu.com"}, {"id": "disk_2_1", "type": "disk", "name": "Disk 1", "server_id": "bare_metal_server_2", "capacity": "1TB", "mount_point": "/", "link": "http://baidu.com"}, {"id": "network_1_1", "type": "network", "name": "eth0", "server_id": "bare_metal_server_1", "ip_address": "**********", "mac_address": "00:1A:2B:3C:4D:5E", "speed": "10Gbps", "link": "http://baidu.com"}, {"id": "network_1_2", "type": "network", "name": "eth1", "server_id": "bare_metal_server_1", "ip_address": "*************", "mac_address": "00:1A:2B:3C:4D:5F", "speed": "1Gbps", "link": "http://baidu.com"}, {"id": "network_2_1", "type": "network", "name": "eth0", "server_id": "bare_metal_server_2", "ip_address": "**********", "mac_address": "00:1A:2B:3C:4D:6E", "speed": "10Gbps", "link": "http://baidu.com"}], "edges": [{"source": "physical_server_1", "target": "bare_metal_node_1", "relation": "hosts", "description": "物理服务器托管裸机节点"}, {"source": "physical_server_2", "target": "bare_metal_node_2", "relation": "hosts", "description": "物理服务器托管裸机节点"}, {"source": "bare_metal_node_1", "target": "bare_metal_server_1", "relation": "provisions", "description": "裸机节点提供裸金属服务器"}, {"source": "bare_metal_node_2", "target": "bare_metal_server_2", "relation": "provisions", "description": "裸机节点提供裸金属服务器"}, {"source": "bare_metal_server_1", "target": "disk_1_1", "relation": "has_disk", "description": "服务器拥有磁盘"}, {"source": "bare_metal_server_1", "target": "disk_1_2", "relation": "has_disk", "description": "服务器拥有磁盘"}, {"source": "bare_metal_server_2", "target": "disk_2_1", "relation": "has_disk", "description": "服务器拥有磁盘"}, {"source": "bare_metal_server_1", "target": "network_1_1", "relation": "has_network", "description": "服务器网络接口"}, {"source": "bare_metal_server_1", "target": "network_1_2", "relation": "has_network", "description": "服务器网络接口"}, {"source": "bare_metal_server_2", "target": "network_2_1", "relation": "has_network", "description": "服务器网络接口"}]}