<!-- 分类镜像 -->
<template>
  <div class="app-container">
    
      <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="name">
          <el-input
            v-model="queryParams.search"
            placeholder="输入镜像ID、名称进行搜索"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetClick()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>


    <el-card shadow="never">
      <div class="mb-[10px]">
        <el-button type="success" @click="handleAddClick()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button
          type="danger"
          :disabled="ids.length === 0"
          @click="handleDelete()"
        >
          <i-ep-delete />
          删除
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        highlight-current-row
        :data="tableData"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />

        <!-- <el-table-column type="expand" label="" width="100">
          <template #default="props">
            <el-table :data="props.row.dictItems">
              <el-table-column label="镜像项键" prop="name" width="200" />
              <el-table-column label="镜像项值" prop="value" align="center" />
              <el-table-column label="排序" prop="sort" align="center" />
            </el-table>
          </template>
        </el-table-column> -->
        <el-table-column label="ID" prop="image_id" />
        <el-table-column label="名称" prop="name" />
        <el-table-column label="类型" prop="image_type" />
        <el-table-column label="状态" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
              {{ scope.row.status === 'active' ? "运行中" : scope.row.vm_state }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="可见性" prop="visibility" />
        <el-table-column label="收保护的" prop="protected" />
        <el-table-column label="磁盘格式" prop="disk_format" />
        <el-table-column label="大小" prop="size" />
        <!-- <el-table-column fixed="right" label="操作" align="center" width="220">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click.stop="handleEditClick(scope.row.id, scope.row.name)"
            >
              <i-ep-edit />
              编辑
            </el-button>
            <el-button
              type="danger"
              link
              size="small"
              @click.stop="handleDelete(scope.row.id)"
            >
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.limit"
        @pagination="handleQuery"
      />
    </el-card>

    <!--镜像弹窗-->

    <el-drawer
      v-model="dialog.visible"
      :title="dialog.title"
      size="80%"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="computedRules"
        label-width="100px"
      >
        <el-card shadow="never">
          <el-row>
            <el-col :span="12">
              <el-form-item label="实例名" prop="name">
                <el-input v-model="formData.name" placeholder="请输入镜像名称" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="镜像" prop="image_name">
                <el-select v-model="formData.image_name" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in imageOptions" :key="index" :label="item.label" :value="item.value"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卷大小（GB）" prop="volume_size">
                <el-input v-model="formData.volume_size" placeholder="请输入系统盘大小" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="规格" prop="flavor_name">
                <el-select v-model="formData.flavor_name" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in flavorOptions" :key="index" :label="item.name" :value="item.name"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="网络" prop="netname">
                <el-select v-model="formData.netname" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in netOptions" :key="index" :label="item.name" :value="item.name"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="Key" prop="key_name">
                <el-select v-model="formData.key_name" class="full-width-input" clearable>
                  <el-option v-for="(item, index) in keyOptions" :key="index" :label="item.label" :value="item.value"
                    :disabled="item.disabled"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="安全组" prop="key_name">
                <el-select v-model="formData.key_name" multiple class="full-width-input" clearable>
                  <el-option v-for="(item, index) in sgOptions" :key="index" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

        </el-card>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmitClick">确 定</el-button>
          <el-button @click="handleCloseDialog">取 消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import "/@/styles/index.scss";   // 基础样式
import { reactive, ref, computed, onMounted, defineOptions } from "vue";
import { ElForm, ElInput, ElDrawer, ElButton, ElTag, ElMessage, ElMessageBox } from "element-plus";
import  Pagination from  "/@/components/Pagination/index.vue";

defineOptions({
  name: "Image",
  inherititems: false,
});

import ImageAPI, { ImagePageQuery, ImagePageVO, ImageForm, SecurityGroup } from "/@/api/cmdb/image";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const imageOptions = ref<any[]>([{
          "label": "ubunt22.04",
          "value": 1
        }, {
          "label": "centos7",
          "value": 2
        }, {
          "label": "debian11",
          "value": 3
        }],);

const flavorOptions = ref<any[]>([{
          "name": "ecs.min"
        }, {
          "name": "ecs.middle"
        }, {
          "name": "ecs.size"
  }],);

  const netOptions = ref<any[]>([{
          "name": "vlan",
        }, {
          "name": "1--2-3-4",
        }, {
          "name": "19-20-30-40",
  }],);

  const keyOptions = ref<any[]>([{
          "label": "default",
          "value": 1
        }, {
          "label": "fx",
          "value": 2
        }, {
          "label": "czy",
          "value": 3
        }],);

  const sgOptions = ref<SecurityGroup[]>([{
          "name": "default",
          "id": "4856f1d4-536a-459b-ab62-f7ebd613865f"
        }, {
          "name": "xz_chenzy",
          "id": "7803592b-8b18-4152-8a1c-213c86923e1f"
        }, {
          "name": "czy",
          "id": "a1ea53e3-3f1a-4438-8b63-cf8126e9c3e1"
        }],);

const queryParams = reactive<ImagePageQuery>({
  page: 1,
  limit: 10,
});

const tableData = ref<ImagePageVO[]>();

// 镜像弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

const formData = reactive<ImageForm>({});

const computedRules = computed(() => {
  const rules: Partial<Record<string, any>> = {
    name: [{ required: true, message: "请输入镜像名称", trigger: "blur" }],
    image_name: [{ required: true, message: "请选择镜像", trigger: "blur" }],
    volume_size: [{ required: true, message: "请输入卷大小", trigger: "blur" }],
    flavor_name: [{ required: true, message: "请选择规格", trigger: "blur" }],
    netname: [{ required: true, message: "请选择网络", trigger: "blur" }],
    key_name: [{ required: true, message: "请选择KeyPair", trigger: "blur" }],
    security_groups: [{ required: true, message: "请选择安全组", trigger: "blur" }],

  };
  if (formData.volumeItems) {
    formData.volumeItems.forEach((attr, index) => {
      rules[`dictItems.${index}.name`] = [
        { required: true, message: "请输入镜像项名称", trigger: "blur" },
      ];
    });
  }
  return rules;
});


// 查询镜像列表


// 查询
function handleQuery() {
  loading.value = true;
  ImageAPI.getPage(queryParams)
    .then((data) => {
      tableData.value = data.data;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetClick() {
  queryFormRef.value.resetFields();
  queryParams.page = 1;
  handleQuery();
}

// 行选择
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 新增镜像
function handleAddClick() {
  dialog.visible = true;
  dialog.title = "新增镜像";
}

/**
 * 编辑镜像
 *
 * @param id 镜像ID
 */
function handleEditClick(id: number, name: string) {
  dialog.visible = true;
  dialog.title = "【" + name + "】镜像修改";
  ImageAPI.getFormData(id).then((data) => {
    Object.assign(formData, data);
  });
}

// 提交镜像表单
function handleSubmitClick() {
  dataFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      loading.value = true;
      const id = formData.insid;
      if (id) {
        ImageAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        ImageAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭镜像弹窗 */
function handleCloseDialog() {
  dialog.visible = false;

  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.insid = undefined;
  formData.volumeItems = [];
}
/**
 * 删除镜像
 *
 * @param id 镜像ID
 */
function handleDelete(id?: number) {
  const attrGroupIds = [id || ids.value].join(",");
  if (!attrGroupIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }
  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      ImageAPI.deleteByIds(attrGroupIds).then(() => {
        ElMessage.success("删除成功");
        handleResetClick();
      });
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

/** 新增Volume项 */
function handleAddAttrClick() {
  formData.volumeItems = formData.volumeItems ?? [];
  formData.volumeItems.push({ sort: 1, status: 1 });
}

/** 删除镜像项 */
function handleDeleteAttrClick(index: number) {
  if (formData.volumeItems && formData.volumeItems.length > 0) {
    formData.volumeItems.splice(index, 1);
  }
}

onMounted(() => {
  handleQuery();
});
</script>
