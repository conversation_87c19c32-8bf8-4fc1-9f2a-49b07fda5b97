<template>
	<div>
		<!-- 合同基本信息 -->
		<el-descriptions size="large" :column="2" border>
			<el-descriptions-item>
				<template #label>
					<el-form-item label="合同名称" required class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item label prop="name" required :rules="options.rules.name">
					<el-input v-model="props.item.name" placeholder="请输入合同名称" :disabled="isDetail" />
				</el-form-item>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="父级合同" class="form-label-in-descriptions" label-width="130" />
				</template>
				<div class="element-between">
					<el-select
						v-model="props.item.parent_code"
						filterable
						placeholder="请选择父级合同"
						@visible-change="visibleChanged"
						clearable
						:disabled="isDetail"
					>
						<el-option
							v-for="s in contractList"
							:key="s.id"
							:label="s.code + '&nbsp;&nbsp;&nbsp;' + s.name + '&nbsp;&nbsp;&nbsp;' + s.customer_name + '&nbsp;&nbsp;&nbsp;' + s.sign_time"
							:value="s.code"
						/>
					</el-select>
					<!-- <el-tooltip placement="top">
						<template #content>
							合同分为新签与续签,父级合同理解为续签合同所绑定的上一级合同。
							<br />若不选择父级合同,则创建的合同默认为新签合同。
							<br />若选择了父级合同,则创建的合同为续签合同。
						</template>
						<el-icon size="20">
							<QuestionFilled />
						</el-icon>
					</el-tooltip>-->
				</div>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="签约主体(乙方)" required class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-select
					:disabled="isDetail"
					v-model="props.item.second_party"
					filterable
					placeholder="请选择签订主体公司"
				>
					<el-option v-for="s in secondPartyOptions" :key="s.value" :label="s.label" :value="s.value" />
				</el-select>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="合同始止日期" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item required>
					<el-date-picker
						:disabled="isDetail"
						v-model="contractDateRange"
						type="daterange"
						unlink-panels
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						value-format="YYYY-MM-DD"
						style="width:100px"
					/>
				</el-form-item>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="客户名称(甲方)" required class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item label required :rules="options.rules.customer_name" prop="customer.name">
					<!-- 可编辑时用选择器 -->
					<el-select
						v-model="props.item.customer.name"
						filterable
						placeholder="请输入客户名称"
						@visible-change="customerVisibleChanged"
						@change="customerChanged"
						:disabled="isDetail"
					>
						<el-option
							v-for="s in customerList"
							:key="s.id"
							:label="s.name + '&nbsp;&nbsp;&nbsp;' + s.officer"
							:value="s.name"
						/>
					</el-select>
				</el-form-item>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="签约客户代表" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.customer.officer" placeholder="请输入客户代表" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="客户联系地址" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input v-model="props.item.customer.addr" placeholder="请输入客户联系地址" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="客户联系电话" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input :value="hiddenPhone" placeholder="请输入客户联系电话" :disabled="true" />
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="合同金额" class="form-label-in-descriptions" label-width="130" />
				</template>
				<div class="element-between">
					<el-input-number
						v-model="props.item.amount"
						placeholder="请输入合同金额"
						style="width: 130px;"
						:disabled="true"
						:controls="false"
						:precision="2"
					/>
					<el-tooltip placement="top">
						<template #content>根据选定产品自动计算</template>
						<el-icon size="20">
							<QuestionFilled />
						</el-icon>
					</el-tooltip>
				</div>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item required label="合同编号" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item label prop="code" required :rules="options.rules.code">
					<el-input v-model="props.item.code" placeholder="请输入合同编号" :disabled="isDetail" />
				</el-form-item>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="付款方式" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-radio-group v-model="props.item.pay_type" :disabled="isDetail">
					<el-radio value="前付" size="large">前付</el-radio>
					<el-radio value="后付" size="large">后付</el-radio>
				</el-radio-group>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="押金" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input-number
					v-model="props.item.cash_pledge"
					style="width: 150px;"
					placeholder="请输入押金"
					:disabled="isDetail"
				/>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="备注信息" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-input
					type="textarea"
					v-model="props.item.description"
					placeholder="填写描述"
					:disabled="isDetail"
				></el-input>
			</el-descriptions-item>
		</el-descriptions>

		<!-- 签订信息 -->
		<el-descriptions size="large" :column="2" border>
			<template #title>
				<div style="font-size: 18px;margin:10px 0 0 15px">签订信息</div>
			</template>
			<el-descriptions-item>
				<template #label>
					<el-form-item required label="合同签订人" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-form-item label required :rules="options.rules.sign_name" prop="sign_name">
					<el-select
						v-model="props.item.sign_name"
						filterable
						placeholder="请选择合同签订人"
						@visible-change="userVisibleChanged"
						style="min-width:240px"
						:disabled="isDetail"
					>
						<el-option
							v-for="s in userList"
							:key="s.id"
							:label="s.name + '&nbsp;&nbsp;&nbsp;' + s.dept_name"
							:value="s.name"
						/>
					</el-select>
				</el-form-item>
			</el-descriptions-item>

			<el-descriptions-item>
				<template #label>
					<el-form-item label="合同双签时间" class="form-label-in-descriptions" label-width="130" />
				</template>
				<el-date-picker
					:disabled="isDetail"
					v-model="props.item.sign_time"
					type="month"
					placeholder="选择双签月份"
					value-format="YYYY-MM"
					style="min-width:240px"
				/>
			</el-descriptions-item>
		</el-descriptions>

		<!-- 订单信息 -->
		<el-descriptions>
			<template #title>
				<div style="font-size: 18px;margin:10px 0 0 15px" class="element-between">
					<span>订单信息</span>
					<el-tooltip placement="bottom">
						<template #content>选定产品后可以在产品基础上的某些数据进行二次编辑,如: 单价、规格、单位等</template>
						<el-icon size="20">
							<QuestionFilled />
						</el-icon>
					</el-tooltip>
					<el-button
						:disabled="isDetail"
						type="success"
						plain
						icon="Plus"
						size="large"
						style="margin-left:10px"
						@click="handleAddProduct"
					>添加产品</el-button>
				</div>
			</template>
		</el-descriptions>

		<!-- 订单表格 -->
		<el-table border scrollbar-always-on :data="props.item.sku_list" style="width: 100%">
			<el-table-column prop="name" label="产品名称" width="150px" fixed="left" />
			<el-table-column prop label="规格">
				<template #default="scope">
					<el-form-item>
						<el-tooltip class="item" effect="dark" :content="scope.row.attributes" placement="top-start">
							<el-input v-model="scope.row.attributes" :disabled="true" />
						</el-tooltip>
					</el-form-item>
				</template>
			</el-table-column>
			<el-table-column prop label="产品标签" width="100px">
				<template #default="scope">
					<el-select v-model="scope.row.label" filterable placeholder="请指定产品标签" :disabled="isDetail">
						<el-option v-for="s in productLabelOptions" :key="s.label" :label="s.label" :value="s.value" />
					</el-select>
				</template>
			</el-table-column>
			<el-table-column prop label="产品来源" width="100px">
				<template #default="scope">
					<el-select v-model="scope.row.source" filterable placeholder="请指定产品来源" :disabled="isDetail">
						<el-option
							v-for="s in productSourceOptions"
							:key="s.label"
							:label="s.label"
							:value="s.value"
						/>
					</el-select>
				</template>
			</el-table-column>
			<el-table-column prop label="销售单价" width="100px">
				<template #default="scope">
					<el-form-item
						label
						:prop="('sku_list.'+scope.$index+'.price')"
						requried
						:rules="options.rules.product_price"
					>
						<el-input-number
							v-model="scope.row.price"
							placeholder="请输入单价"
							:controls="false"
							:disabled="isDetail"
							:precision="2"
						/>
					</el-form-item>
				</template>
			</el-table-column>
			<el-table-column prop label="单位" width="100px">
				<template #default="scope">
					<el-form-item
						label
						:prop="('sku_list.'+scope.$index+'.unit')"
						requried
						:rules="options.rules.product_unit"
					>
						<el-input v-model="scope.row.unit" placeholder="请输入单位" :disabled="isDetail" />
					</el-form-item>
				</template>
			</el-table-column>
			<el-table-column prop label="数量" width="100px">
				<template #default="scope">
					<el-form-item
						label
						:prop="('sku_list.'+scope.$index+'.count')"
						requried
						:rules="options.rules.product_count"
					>
						<el-input-number v-model="scope.row.count" :min="1" :disabled="isDetail" />
					</el-form-item>
				</template>
			</el-table-column>
			<!-- 时长 -->
			<el-table-column prop label="时长" width="120px">
				<template #default="scope">
					<div style="display: flex; align-items: center; gap: 4px">
						<el-input-number
							v-model="scope.row.duration.amount"
							:min="0"
							:controls="false"
							:disabled="isDetail"
							style="width: 50px"
						/>
						<el-select
							v-model="scope.row.duration.unit"
							:disabled="isDetail"
							style="width: 45px"
							popper-class="duration-unit-dropdown"
						>
							<el-option v-for="s in durationOptions" :key="s.label" :label="s.label" :value="s.value" />
						</el-select>
					</div>
				</template>
			</el-table-column>
			<el-table-column prop="total_price" label="小计" width="100px" />
			<el-table-column prop label="备注信息">
				<template #default="scope">
					<el-form-item>
						<el-input v-model="scope.row.description" :disabled="isDetail" />
					</el-form-item>
				</template>
			</el-table-column>
			<el-table-column fixed="right" label="操作" width="80px">
				<template #default="scope">
					<el-button link type="warning" @click="deleteTableRow(scope.$index)" :disabled="isDetail">删除</el-button>
				</template>
			</el-table-column>
		</el-table>

		<!-- 选择产品表单 -->
		<el-drawer v-model="productDialogVisible" size="60%" :with-header="false">
			<fs-page>
				<fs-crud ref="crudRef" v-bind="crudBinding">
					<template #actionbar-right>
						<el-button type="primary" plain @click="handleSelectProduct" size="large">确认选择</el-button>
					</template>
				</fs-crud>
			</fs-page>
		</el-drawer>

		<!-- 选择客户表单 -->
		<!-- <el-drawer v-model="customerDialogVisible" size="50%" :with-header="false">
			<fs-page>
				<fs-crud ref="crudRef" v-bind="crudBinding">
					<template #actionbar-right>
						<el-button type="primary" plain @click="handleSelectProduct" size="large">确认选择</el-button>
					</template>
				</fs-crud>
			</fs-page>
		</el-drawer>-->
	</div>
</template>

<script setup>
import { ref,onBeforeMount,reactive,watch,toRefs,markRaw,defineProps,computed } from 'vue'
import { useFs } from '@fast-crud/fast-crud';
import * as api from '/@/api/contract/contract';
import * as userApi from '/@/views/system/api/user.ts';
import * as customerApi from '/@/views/customer/customer/api.ts';
import * as areaApi from '/@/views/system/areas/api.ts';
import { createCrudOptions } from '/@/views/contract/product/crud';

const { crudBinding, crudRef, crudExpose,selectProducts } = useFs({ createCrudOptions });

// 接收参数
const props = defineProps({
    item: {
      type: Object,
    },
    method: {
      type: String
    }
})

const data = reactive({
  secondPartyOptions:[{
    value: '杭州星哉科技有限公司',
    label: '杭州星哉科技有限公司',
  }],
  durationOptions:[
    {
    value: 'year',
    label: '年',
    },
    {
    value: 'season',
    label: '季',
    },
    {
    value: 'month',
    label: '月',
    },
    {
    value: 'day',
    label: '日',
    },
  ],
  productLabelOptions:[
    {
    value: '新签',
    label: '新签',
    },
    {
    value: '续签',
    label: '续签',
    },
  ],
  productSourceOptions:[
    {
    value: '自行开发',
    label: '自行开发',
    },
    {
    value: '公司渠道',
    label: '公司渠道',
    },
    {
    value: '合作伙伴',
    label: '合作伙伴',
    },
  ],
  contractList:[],
  customerList:[],
  userList:[],
  options: {
    rules: {
      name: [
        {required: true, message: '请输入合同名称', trigger: 'blur'}
      ],
      code: [
        {required: true, message: '请输入合同编号', trigger: 'blur'}
      ],
      customer_name: [
        {required: true, message: '请输入客户名称', trigger: 'blur'}
      ],
      sign_name: [
        {required: true, message: '请输入签订人', trigger: 'blur'}
      ],
      product_unit: [
        {required: true, message: '请输入单位', trigger: 'blur'}
      ],
      product_price: [
        {required: true, message: '请输入单价', trigger: 'blur'}
      ],
      product_count: [
        {required: true, message: '请输入数量', trigger: 'blur'}
      ],
      product_total_price: [
        {required: true, message: '请输入小计', trigger: 'blur'}
      ],
    }
  }
})


const productDialogVisible = ref(false)
const isDetail = computed(() => props.method === 'Detail')

const contractAmount = computed(() => {
  if (!Array.isArray(props.item.sku_list)) return 0;
  return props.item.sku_list.reduce((sum, item) => {
    return sum + (Number(item.total_price) || 0);
  }, 0);
});

watch(
  () => props.item.sku_list,
  (newList) => {
    newList.forEach((item) => {
      const price = Number(item.price) || 0
      const count = Number(item.count) || 0
      const duration = Number(item.duration.amount) || 0
      item.total_price = (price * count * duration).toFixed(2); 
    })
  },
  { deep: true, immediate: true }
)

watch(
  () => props.item.parent_code,
  (newVal) => {
    if (newVal) {
      props.item.type = '续签'
    } else {
      props.item.type = '新签'
    }
  },
  { immediate: true }
)

watch(contractAmount, (newVal) => {
  props.item.amount = newVal;
}, { immediate: true });

const contractDateRange = computed({
  get() {
    return [props.item.start_time, props.item.end_time]
  },
  set([start, end]) {
    props.item.start_time = start
    props.item.end_time = end
  }
})


const hiddenPhone = computed(() => {
  const phone = props.item.customer.phone  || '';
  return phone.replace(/(\d{3})\d{4}(\d{0,4})/,  '$1****$2'); 
});

const customerChanged = async (name) => {
  const selected = data.customerList.find(item => item.name === name);
  if (!selected) return;

  props.item.customer_name = selected.name;
  props.item.customer = { ...selected }; // 浅拷贝避免污染原始数据
  let provinceName = '';
  let cityName = '';
  let districtName = '';

  if (selected.province) {
    const res = await areaApi.GetLevelAllAreasList({ level: 1, code: selected.province });
    provinceName = res?.data?.[0]?.name || '';
  }
  if (selected.city) {
    const res = await areaApi.GetLevelAllAreasList({ level: 2, code: selected.city });
    cityName = res?.data?.[0]?.name || '';
  }

  if (selected.district) {
    const res = await areaApi.GetLevelAllAreasList({ level: 3, code: selected.district });
    districtName = res?.data?.[0]?.name || '';
  }
  // 拼接完整地址
  props.item.customer.addr = [provinceName, cityName, districtName].filter(Boolean).join('') || '';
};


const handleAddProduct = () => {
  productDialogVisible.value = true
}

// 删除行的方法
const deleteTableRow = (index) => {
	props.item.sku_list.splice(index, 1);
};

const handleSelectProduct = () => {
	selectProducts.value.forEach(element => {
    // 创建深拷贝，确保每个产品实例都是独立的
    const productCopy = JSON.parse(JSON.stringify(element));
    productCopy.count = 1
    productCopy.price = Number(productCopy.price)
    productCopy.duration = {
      unit: "month",
      amount: 1
    }

    props.item.sku_list.push(productCopy)
  });
  productDialogVisible.value = false
}

// 选择框展开调用接口查询数据
const visibleChanged = async(visible) => {
  // 展开的时候去查询items
  if (visible) {
    // 调用查询列表的函数
    const response = await api.GetList({ limit: 10000 });
    data.contractList = response.data
  }
}

const customerVisibleChanged = async(visible) => {
  if (visible) {
    const response = await customerApi.GetList({ limit: 10000 });
    data.customerList = response.data
  }
}

const userVisibleChanged = async(visible) => {
  if (visible) {
    const response = await userApi.GetList({ limit: 10000,user_type:0 });
    data.userList = response.data
  }
}

onBeforeMount(async()=>{
  if (props.method != "Detail"){
    crudExpose.doRefresh();
    // 修改actionbar按钮的显示状态
    crudBinding._value.actionbar.buttons.add.show = false;
    crudBinding._value.rowHandle.buttons.edit.show = false;
    crudBinding._value.rowHandle.buttons.remove.show = false;
  }
  if (props.method != "Create") {
    const customerObj = await customerApi.GetObj(props.item.customer.id)
    data.customerList = [customerObj.data]
    await customerChanged(props.item.customer_name)
  }
})

const { options,secondPartyOptions,contractList,customerList,userList,productLabelOptions,productSourceOptions,durationOptions } = toRefs(data);
</script>
<style scoped>
.el-form .el-form-item:last-of-type {
	margin-bottom: 2px !important;
}

/* label 样式调整 */
::v-deep(.form-label-in-descriptions .el-form-item__label) {
	font-size: 14px;
}

/* 控制左边 label 的统一宽度 */
::v-deep(.el-descriptions__label) {
	width: 160px !important;
}

:deep(.my-label) {
	background: var(--el-color-success-light-9) !important;
}

.element-between {
	display: flex;
	flex-direction: row;
	align-items: center;
}
</style>
