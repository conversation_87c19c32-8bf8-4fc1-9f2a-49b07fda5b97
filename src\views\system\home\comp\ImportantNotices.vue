<template>
  <tiny-card custom-class="stat-cards">
      <h1 class="dashboard-sub-title">重要通知</h1>
      <!-- 通知内容区域 -->
      <div>
        <div v-if="notices.length"> 
          <tiny-card v-for="notice in notices" :key="notice.id" :title="notice.title">
            <p>{{ notice.content  }}</p>
          </tiny-card>
        </div>
        <div v-else class="no-data">暂无新通知</div>
      </div>
  </tiny-card>
</template>
<script setup lang="ts">
import { ref } from 'vue';


interface NoticeTYpe {
  id: string;
  title: string;
  content: string;
}
const notices= ref<Array<NoticeTYpe>>([]);
</script>
<style scoped>
.dashboard-sub-title {
  font-size: 16px;
  margin-bottom: 20px;
}
/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px); /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important; /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
.no-data {
  padding: 20px;
  text-align: center;
  color: #909399;
}

</style>