server {
    listen       80;
    server_name localhost;
    client_max_body_size 100M;
    # 配置前端静态文件目录
    location / {
        index index.html index.htm;
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location ~ ^/api/ {
        proxy_http_version 1.1;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Nginx-Proxy true;
        set_real_ip_from 0.0.0.0/0;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_connect_timeout 600s;
        proxy_read_timeout 600s;
        proxy_send_timeout 600s;
        real_ip_header X-Forwarded-For;
        rewrite ^/api/(.*)$ /$1 break;  #重写
        proxy_pass http://chaos-django.dev.svc.cluster.local:8000/; # 设置代理服务器的协议和地址
     }
 }