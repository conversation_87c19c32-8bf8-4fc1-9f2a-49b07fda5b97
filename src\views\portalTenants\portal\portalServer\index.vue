<template>
  <div class="container-list">
    <div class="contain" v-if="!baseResourceIsLoading">
      <div class="contain-head" v-if="filterOptions.belong_ticket_id === ''">
        <span>{{ props.instanceType }}</span>
        <hr />
        <div class="contain-img">
          <img v-if="setCollapse" src="/@/assets/img/collapse.png" alt="collapse" @click="collapse" />
          <img v-if="!setCollapse" src="/@/assets/img/expand.png" alt="expand" @click="extend" />
        </div>
        <div class="contain-text">
          {{
            setCollapse
              ? "收起"
              : "展开"
          }}
        </div>
      </div>
      <tiny-form :model="filterOptions" label-position="right" label-width="100px" class="filter-form" size="small"
        v-if="filterOptions.belong_ticket_id === ''">
        <tiny-row :flex="true" justify="center" class="col">
          <tiny-col :span="4" label-width="100px">
            <tiny-form-item label="BMC_IP">
              <!-- <tiny-input v-model="filterOptions.baremetal_node_id" placeholder="请输入BMC_IP" :clearable="true"></tiny-input> -->
              <tiny-select v-model="filterOptions.baremetal_node_id" clearable filterable no-match-text="暂无数据">
                <tiny-option v-for="item in allBaremetalNodeData" :key="item.ironic_hyper_id" :label="item.name" :value="item.ironic_hyper_id" :icon="TinyIconDesktopView">
                </tiny-option>
              </tiny-select>
            </tiny-form-item>
          </tiny-col>
          <tiny-col :span="4" label-width="100px">
            <tiny-form-item label="名称">
              <tiny-input v-model="filterOptions.name" placeholder="请输入名称" :clearable="true"></tiny-input>
            </tiny-form-item>
          </tiny-col>
          <tiny-col :span="4" label-width="100px">
            <tiny-form-item label="内网IP">
              <tiny-input v-model="filterOptions.ipaddr" placeholder="请输入内网IP" :clearable="true"></tiny-input>
            </tiny-form-item>
          </tiny-col>
        </tiny-row>

        <tiny-row v-if="setCollapse" :flex="true" justify="start" class="col">
          <tiny-col v-if="setCollapse" :span="4">
            <tiny-form-item label="状态">
              <tiny-select v-model="filterOptions.status" :options="tenantPortalServerStatus"
                :clearable="true"></tiny-select>
            </tiny-form-item>
          </tiny-col>
          <tiny-col v-if="setCollapse && (isShowAccountFilter && loginUserType === 0)" :span="4">
            <tiny-form-item label="租户">
              <tiny-select v-model="filterOptions.account_id" :searchable="true" :show-empty-image="true"
                :clearable="true" :allow-copy="true">
                <tiny-option v-for="item in allAccountsData" :key="item.id" :label="item.company" :value="item.id">
                </tiny-option>
              </tiny-select>
            </tiny-form-item>
          </tiny-col>
          <tiny-col v-if="setCollapse" :span="4">
            <tiny-form-item label="地域">
              <tiny-select v-model="filterOptions.node" :searchable="true" :show-empty-image="true"
                :clearable="true" :allow-copy="true">
                <tiny-option v-for="item in allNodes" :key="item.value" :label="item.label" :value="item.value">
                </tiny-option>
              </tiny-select>
            </tiny-form-item>
          </tiny-col> 
        </tiny-row>
        <tiny-row v-if="setCollapse" :flex="true" justify="start" class="col">
        <tiny-col :span="4" label-width="100px">
            <tiny-form-item>
              <div class="search-btn">
                <tiny-button type="primary" @click="reloadGrid">
                  搜索
                </tiny-button>
                <tiny-button @click="handleFormReset">
                  重置
                </tiny-button>
              </div>
            </tiny-form-item>
          </tiny-col>
          </tiny-row>
      </tiny-form>
      <div class="bottom-line">
        <hr />
      </div>

      <div>
        <!-- 其他组件不显示全屏按钮及主列表页的样式 -->
        <tiny-grid ref="portalServerGrid" :fetch-data="fetchDataOption" :pager="pagerConfig" :loading="loading" :expand-config="expandConfigData"
          size="medium" :auto-resize="true" fixd>
          <template #toolbar>
            <tiny-grid-toolbar>
              <template #buttons>
                <div class="btn">
                  <tiny-button type="primary" @click="clickCreateBT" style="margin-right:10px"
                    v-if="auth('tenant:baremetal:Create')">
                    创建主机
                  </tiny-button>
                  <tiny-button type="primary" @click="clickApplyCreateTenantBaremetalTicket" style="margin-right:10px"
                  v-if="loginUserType === 1">
                  申请主机
                </tiny-button>
                  <tiny-dropdown split-button type="primary" :disabled="true"
                    v-if="filterOptions.belong_ticket_id === '' && false">
                    更多操作
                    <template #dropdown>
                      <tiny-dropdown-menu>
                        <tiny-dropdown-item>开机</tiny-dropdown-item>
                        <tiny-dropdown-item>重启</tiny-dropdown-item>
                        <tiny-dropdown-item>关机</tiny-dropdown-item>
                        <tiny-dropdown-item>过期时间</tiny-dropdown-item>
                      </tiny-dropdown-menu>
                    </template>
                  </tiny-dropdown>
                </div>
              </template>
            </tiny-grid-toolbar>
          </template>

          <tiny-grid-column type="selection" width="40"></tiny-grid-column>
          <tiny-grid-column type="expand" width="0">
            <template #default="{ row }">
              <tiny-grid :data="row.ops_softwares" align="center">
                <tiny-grid-column title="软件名称">
                <template #default="{row}">
                  {{ row.software_options.name }}
                </template>
              </tiny-grid-column>
              <tiny-grid-column title="描述">
                <template #default="{row}">
                  {{ row.software_options.description }}
                </template>
              </tiny-grid-column>
                <tiny-grid-column title="版本">
                  <template #default="{row}">
                    {{ row.software_options.current_version }}
                  </template>
                </tiny-grid-column>
                <tiny-grid-column field="status" title="状态"></tiny-grid-column>
                <tiny-grid-column title="操作" width="160">
                  <template #default="{ row }">
                    <!-- handleRetryOPServerSoftwareInstallTask -->
                    <tiny-button type="text" size="mini" @click="handleRetryOPServerSoftwareInstallTask(row)" v-if="auth('tenant:baremetal:SoftwareInstallRetry')">重试</tiny-button>
                    <tiny-button type="text" size="mini" @click="openShowWorkerLogs(row)" :disabled="!row.scheduletask_work_id">日志</tiny-button>
                  </template>
                </tiny-grid-column>
              </tiny-grid>
            </template>
          </tiny-grid-column>
          <tiny-grid-column field="name" title="ID/名称" align="left" width="100" :sortable="true" :filter="nameFilter">
            <template #default="{ row }">
              <div class="id-cell">
                <tiny-link :underline="false" type="primary" :disabled="loginUserType ? true : false" @click="toOPServerDetail(row.instance_id)">
                  <span class="id-text">{{ row.instance_id.slice(0, 8) }}</span>
                </tiny-link>

                <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.id)"></tiny-link>
              </div>
              <p>{{ row.name }}</p>
            </template>
          </tiny-grid-column>
          <tiny-grid-column field="description" title="描述" align="center" :sortable="true"></tiny-grid-column>
          <tiny-grid-column field="image_name" title="镜像" align="center">
            <template #default="{ row }">
              <show-image-name :desc="row.image_name || 'unknown'" />
            </template>
          </tiny-grid-column>
          <tiny-grid-column field="ipaddr" title="内网IP" align="center" :sortable="true"></tiny-grid-column>
          <tiny-grid-column field="extr_public_ipv4" title="公网IP" align="center" :sortable="true"></tiny-grid-column>
          <tiny-grid-column field="instance_type" title="类型" align="center"></tiny-grid-column>
          <tiny-grid-column field="flavor_name" title="规格" align="center" :sortable="true"></tiny-grid-column>
          <tiny-grid-column field="status" title="状态" align="center">
            <template #default="{ row }">
              <tiny-icon-busy :class="getStatusClass(row.status)"></tiny-icon-busy>
              <span>
                {{ dictionary('tenantPortalServerStatus', row.status) || row.status }}
              </span>
            </template>
          </tiny-grid-column>
          <tiny-grid-column field="is_reachable" title="Ping" align="center">
            <template #default="{ row }">
              <div v-if="row.is_reachable">
                <!-- 共用同一套风格 -->
                <tiny-icon-busy :class="getStatusClass('ACTIVE')" v-if="row.is_reachable"></tiny-icon-busy>
                <span>
                  {{ '可达' }}
                </span>
              </div>
              <div v-else>
                <tiny-icon-busy :class="getStatusClass('UNKNOWN')" v-if="row.is_reachable === false"></tiny-icon-busy>
                <span>
                  {{ '不可达' }}
                </span>
              </div>
              
            </template>
          </tiny-grid-column>
          <tiny-grid-column field="baremetal_node_id" title="裸机节点" align="center" :sortable="true"
            v-if="loginUserType === 0">
            <template #default="{ row }">
              <tiny-link :underline="false" type="primary" :disabled="loginUserType ? true : false || getBaremetalName(row.baremetal_node_id) ? false : true" @click="toOPIronHypervisiorDetail(row.baremetal_node_id)">{{ getBaremetalName(row.baremetal_node_id) || row.baremetal_node_id }}</tiny-link>
            </template>
          </tiny-grid-column>

          <tiny-grid-column field="create_datetime" title="创建时间" align="center" :sortable="true">
            <template #default="{ row }">
              {{ formattedDateTime(row.create_datetime) }}
            </template>
          </tiny-grid-column>
          <tiny-grid-column field="expire_at" title="过期时间" align="center" :sortable="true"></tiny-grid-column>
          <tiny-grid-column field="node" title="区域" align="center"></tiny-grid-column>
          <tiny-grid-column field="sync_time" title="同步时间" align="center" :sortable="true">
            <template #default="{ row }">
              {{ formatNow(row.sync_time) }}
            </template>
          </tiny-grid-column>
          <tiny-grid-column title="操作" align="center">
            <template #default="{ row }">
              <tiny-dropdown type="primary" trigger="click">
                操作
                <template #dropdown>
                  <tiny-dropdown-menu>
                    <tiny-dropdown-item @click="opencreateServerMsgDialogBox(row.create_msg)">调度日志</tiny-dropdown-item>
                    <tiny-dropdown-item @click="handleExpandSoftwares(row)">软件安装日志</tiny-dropdown-item>
                    <tiny-dropdown-item @click="openShowUpdateIPv4(row)" v-if="auth('tenant:baremetal:Update')"
                      divided>记录公网</tiny-dropdown-item>
                      
                      <tiny-dropdown-item @click="confirmSwitchLockServer(row)"
                      v-if="auth('tenant:baremetal:SwitchLockServer')">{{row.is_lock ? "解锁" : "锁定"}}实例</tiny-dropdown-item>
                    <tiny-dropdown-item @click="openShowUpdatBelongTicketId(row)"
                      v-if="auth('tenant:baremetal:Update')">关联订单</tiny-dropdown-item>
                    <tiny-dropdown-item @click="openShowUpdateExpireAt(row)"
                      v-if="auth('tenant:baremetal:Update')">过期时间</tiny-dropdown-item>
                    <tiny-dropdown-item @click="confirmUpdateServertoCmdbHost(row)"
                      v-if="row.instance_type === '裸金属' && auth('tenant:baremetal:Update')">同步主机管理</tiny-dropdown-item>

                    <tiny-dropdown-item divided @click="openShowServerNameDesc(row)"
                      v-if="auth('tenant:baremetal:updateServerNameOrDescription') && (row.status === 'ACTIVE' || row.status === 'SHUTOFF')">编辑实例</tiny-dropdown-item>
                    <tiny-dropdown-item @click="openShowRebuildServer(row)"
                      v-if="auth('tenant:baremetal:RebuildServer') && (row.status === 'ACTIVE' || row.status === 'SHUTOFF')">重建实例</tiny-dropdown-item>
                    <tiny-dropdown-item @click="confirmStartServer(row)"
                      v-if="auth('tenant:baremetal:StartServer') && row.status === 'SHUTOFF'">开机</tiny-dropdown-item>
                    <tiny-dropdown-item @click="confirmRebootServer(row)" style="color: #f76360"
                      v-if="auth('tenant:baremetal:Rebooterver') && row.status === 'ACTIVE'">重启</tiny-dropdown-item>
                    <tiny-dropdown-item @click="confirmStopServer(row)" style="color: #f76360"
                      v-if="auth('tenant:baremetal:StopServer') && row.status === 'ACTIVE'">关机</tiny-dropdown-item>
                    <tiny-dropdown-item @click="openShowDelete(row)" v-if="auth('tenant:baremetal:Delete')"
                      style="color: #f76360" divided>强制删除</tiny-dropdown-item>
                    <!-- <tiny-dropdown-item @click="openShowSoftDelete(row)" :disabled="true">软删除</tiny-dropdown-item> -->
                  </tiny-dropdown-menu>
                </template>
              </tiny-dropdown>
            </template>
          </tiny-grid-column>
        </tiny-grid>
      </div>
      <tiny-modal v-model="isShowUpdateIPv4" title="配置自定义公网" message="窗口内容" show-footer @confirm="updateExtrPublicIpv4">
        <tiny-form ref="updateExtrPublicIpv4Valid" :model="updateExtrPublicIpv4Data" :rules="updateExtrPublicIpv4Rules"
          :inline="false" label-position="left" :label-align="true">
          <tiny-form-item label="公网IP" prop="extr_public_ipv4">
            <tiny-ip-address v-model="updateExtrPublicIpv4Data.extr_public_ipv4"
              style="width: 40%; margin-right: 10px;" clearable></tiny-ip-address>
              <span
              style="display: block; color: #f0ad4e; border-radius: 0px; font-size: 12px">此操作用于记录在防火墙内已关联的主机公网IP，后期回收主机时需要查询。<span style="color: #d9534f; font-size: 14px;">【请确认后再进行关联操作，谨慎操作！！！】</span></span>
          </tiny-form-item>
          
        </tiny-form>
      </tiny-modal>
      <tiny-modal v-model="isShowBelongTicketId" title="关联订单" message="窗口内容" show-footer width="80%"
        @confirm="updateBelongTicketId">
        <tiny-form ref="updateBelongTicketIdValid" :model="updateBelongTicketIdData" :rules="updateBelongTicketIdRules"
          :inline="false" label-position="left" :label-align="true">
          <tiny-form-item label="选择订单" prop="belong_ticket_id">
            <selectTicketForm v-model:current-select-ticket-id="updateBelongTicketIdData.belong_ticket_id" />
          </tiny-form-item>
        </tiny-form>
      </tiny-modal>
      <tiny-dialog-box v-model:visible="isShowExpireAt" title="配置过期时间" width="40%" :is-form-reset="false">
        <tiny-form ref="updateExpireAtValid" :model="updateExpireAtData" :rules="updateExpireAtRules" :inline="false"
          label-position="left" :label-align="true">
          <tiny-form-item label="过期时间" prop="expire_at">
            <tiny-date-picker v-model="updateExpireAtData.expire_at" :picker-options="pickerOptions" type="datetime"
              placeholder="请选择过期日期" default-time="23:59:59" style="width: 40%; margin-right: 10px"
              value-format="yyyy-MM-dd HH:mm:ss"></tiny-date-picker>
          </tiny-form-item>
        </tiny-form>
        <template #footer>
          <tiny-button type="primary" @click="updateExpireAt"> 确定 </tiny-button>
          <tiny-button @click="isShowExpireAt = false"> 取消 </tiny-button>
        </template>
      </tiny-dialog-box>
      <tiny-modal v-model="isShowUpdateServerNameDesc" title="编辑实例" message="编辑实例" show-footer width="50%"
        @confirm="confirmUpdateServerNameOrDescription">
        <tiny-form :model="updateServerNameDescData" :inline="false" label-position="left" :label-align="true">
          <tiny-form-item label="实例名称" prop="name">
            <tiny-input v-model="updateServerNameDescData.name" placeholder="请输入实例名称" :maxlength="50" show-word-limit
              style="width: 50%; margin-right: 10px"></tiny-input>
          </tiny-form-item>
          <tiny-form-item label="实例描述" prop="description">
            <tiny-input v-model="updateServerNameDescData.description" placeholder="请输入实例描述信息" :maxlength="127"
              show-word-limit style="width: 50%; margin-right: 10px"></tiny-input>
          </tiny-form-item>
        </tiny-form>
      </tiny-modal>
      <el-dialog v-model="createServerMsgVisibility" title="查看日志" @close="createServerMsgVisibility = false" destroy-on-close>
        <AnsibleXtermlogViewer :ws-config="{ url: '', isAnsiColorFixed: true, protocols: 'singleMsgs'}" :message="createServerMsgData"/>
      </el-dialog>
          
      <el-dialog v-model="isShowWorkerLogsVisible" title="软件安装日志" @close="isShowWorkerLogsVisible = false" destroy-on-close>
        <AnsibleXtermlogViewer :ws-config="{ url: getOrigin(scheduletaskWorkerId), isAnsiColorFixed: true, protocols: 'ws' }" />
    </el-dialog>
      <tiny-dialog-box v-model:visible="isShowRebuildServer" title="重新构建实例" width="80%" :is-form-reset="false">
        <tiny-form ref="rebuildServerDataValid" :model="rebuildServerData" :inline="false"
          :rules="rebuildServerDataRules" label-position="left" :label-align="true">
          <tiny-form-item label="镜像" prop="image_id">
            <SelectImageForm v-model:current-select-image-id="rebuildServerData.image_id" />
          </tiny-form-item>
          <tiny-form-item label="实例名称" prop="name">
            <tiny-input v-model="rebuildServerData.name" placeholder="请输入实例名称" :maxlength="50" show-word-limit
              style="width: 40%; margin-right: 10px"></tiny-input>
          </tiny-form-item>
          <tiny-form-item label="实例描述" prop="description">
            <tiny-input v-model="rebuildServerData.description" placeholder="请输入实例描述信息" :maxlength="127" show-word-limit
              style="width: 40%; margin-right: 10px"></tiny-input>
          </tiny-form-item>
        </tiny-form>
        <template #footer>
          <tiny-button type="primary" @click="confirmRebuildServer"> 确定 </tiny-button>
          <tiny-button @click="isShowRebuildServer = false"> 取消 </tiny-button>
        </template>
      </tiny-dialog-box>
      <tiny-modal v-model="isShowDeleteModal" :title="updateDeleteData.is_soft_delete === true ? '软删除' : '强制删除'"
        message="窗口内容" width="30%" show-footer status="warning">
        <template #footer>
          <tiny-button type="danger" @click="updateDelete" :disabled="isShowDeleteButtonVisible">确认删除</tiny-button>
          <!-- <tiny-button style="margin-left: 12px">保存</tiny-button> -->
        </template>
        <tiny-form :model="updateDeleteData" :inline="false" label-position="left" :label-align="true">
          <div style="margin-bottom: 20px;">
            确定要删除
            <span style="margin-left: 10px; margin-right: 10px;">
              <tiny-tag type="danger" size="medium">
                内:{{ updateDeleteData.ipaddr }}
                <span v-if="updateDeleteData.row.extr_public_ipv4">
                  |公: {{ updateDeleteData.row.extr_public_ipv4 }}
                </span>
              </tiny-tag>
            </span>
            主机吗？
          </div>
          <span
            style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px; margin-bottom: 20px;">输入完整的主机内网IP后，方可删除此主机;若标签内为空，无需输入则可直接删除。</span>
          <tiny-form-item label="主机内网IP" prop="input_ipaddr">
            <tiny-input v-model="updateDeleteData.input_ipaddr" placeholder="请输入完整主机内网IP"></tiny-input>
          </tiny-form-item>
        </tiny-form>
      </tiny-modal>
    </div>
  </div>
</template>
<script lang="ts" setup name="portalServerIndex">
import { ref, reactive, toRefs, onMounted, watch } from 'vue';
import {
  Grid as TinyGrid,
  GridColumn as TinyGridColumn,
  GridToolbar as TinyGridToolbar,
  Form as TinyForm,
  FormItem as TinyFormItem,
  Input as TinyInput,
  Button as TinyButton,
  Row as TinyRow,
  Col as TinyCol,
  Select as TinySelect,
  Pager as TinyPager,
  Dropdown as TinyDropdown,
  DropdownMenu as TinyDropdownMenu,
  DropdownItem as TinyDropdownItem,
  Link as TinyLink,
  DialogBox as TinyDialogBox,
  Modal as TinyModal,
  IpAddress as TinyIpAddress,
} from '@opentiny/vue';
import { iconCopy, IconBusy, IconDesktopView } from '@opentiny/vue-icon';
import {
  GetList, DelObj, UpdateObj, SoftDeleteOpenstackServer, updateServerNameOrDesc,
  startServer, stopServer, rebootServer, rebuildServer, updateServerExpireAt,
  updateServerToOPCmdbHost, retryOPServerSoftwareInstallTask,
} from '/@/api/tenant/opServer';
import { GetListByIds as accountGetListByIds } from '/@/api/tenant/account';
import { GetListByIds as baremetalNodeGetListByIds } from '/@/api/tenant/opIronicHypervisor';
import { useRouter } from 'vue-router';
import ShowImageName from '/@/views/portalTenants/components/showImageName.vue';
import { dictionary, getLabelByKeyValue } from '/@/utils/dictionary';
import { formatNow, formattedDateTime } from '/@/utils/formatTime';
import { copyText } from '/@/utils/copyText';
import SelectTicketForm from '/@/views/ticket/ticket/comp/selectTicketForm.vue';
import SelectImageForm from '/@/views/portalTenants/portal/portalServer/createComp/form/selectImageForm.vue';
import AnsibleXtermlogViewer from '/@/components/myXterm/AnsibleXtermlogViewer.vue';
import { pickerOptions } from '/@/utils/pickDatetimeOptions';
import { auth } from '/@/utils/authFunction';
import { useUserInfo } from '/@/stores/userInfo';
import { toOPIronHypervisiorDetail, toOPServerDetail } from '/@/router/intervalRouterTo/tenant';

const userStore = useUserInfo();

// 图标组件初始化
const TinyIconCopy = iconCopy();
const TinyIconBusy = IconBusy();
const TinyIconDesktopView = IconDesktopView();
// 用于区分是否为后台用户和前台用户
const loginUserType = userStore.userInfos.user_type;

const getOrigin = (worker_id: string) => {
  let origin = '';
  if (import.meta.env.MODE == 'development') {
		origin = 'ws://127.0.0.1:8000';
	} else {
		// 拿到当前域名
		let temp = window.location.origin + '/api';
		// 把http换成ws
		origin = temp.replaceAll('http', 'ws');
	}
	const websocketUrl = `${origin}/work/${worker_id}/`;
  return websocketUrl;
};

// 表格展开行配置
const expandConfigData = {
  expandAll: false, // 默认展开所有行
  trigger: 'cell', // 触发方式 default（点击按钮触发）,cell（点击单元格触发）,row（点击行触发）
  expandRowKeys: [], // 默认展开指定行（需要有 row-id）
  accordion: true, // 对于同一级的节点，每次只能展开一个
  showIcon: false // 配置是否显示展开图标
}

// 所有openstack地域节点
const nodeValueField = ref('value');
const allNodes = dictionary('operator_cmdb:host:area_node', undefined);


const props = defineProps({
  belong_ticket_id: {
    type: String,
    required: false,
    default: '',
  },
  selectedAccountId: {
    type: String,
    required: false,
    default: '',
  },
  instanceType: {
    type: String,
    required: false,
    default: '',
  }
});

const emit = defineEmits(['update:activeHostCount', 'update:allHostCount']);

// 枚举列数据获取
const tenantPortalServerStatus = dictionary('tenantPortalServerStatus', undefined)

// 定义状态分类及其对应的CSS类名
const statusClassify = {
  success: ['ACTIVE', 'BUILD', 'SHELVED', 'SHELVED_OFFLOADED'],
  warning: [
    'PAUSED', 'RESCUED', 'RESCUE', 'RESIZE', 'RESIZED', 'VERIFY_RESIZE', 'SUSPENDED', 'STOPPED',
    'HARD_REBOOT', 'PASSWORD', 'MIGRATING', 'UNKNOWN',
  ],
  danger: ['DELETED', 'SOFT_DELETED', 'ERROR', 'REBUILD']
};

const getStatusClass = (status: string) => {
  // 成功类型
  if (statusClassify.success.includes(status)) {
    return 'status-success'
  } else if (statusClassify.danger.includes(status)) {
    return 'status-danger'
  } else if (statusClassify.warning.includes(status)) {
    return 'status-warning'
  } else {
    return 'status-danger'
  }
}


const getBaremetalName = (nodeId: string) => {
  return getLabelByKeyValue('ironic_hyper_id', nodeId, 'name', allBaremetalNodeData)
}


// 路由
const router = useRouter();


// 展开主机与软件安装任务数据
const expandSoftwareInstallData = ref([]);
const handleExpandSoftwares = (row: any) => {
  expandSoftwareInstallData.value = row.ops_softwares
  portalServerGrid.value?.setRowExpansion(row);
};


let isShowUpdateIPv4 = ref(false);
let updateExtrPublicIpv4Data = ref({
  extr_public_ipv4: '',
  row: {},
});
const openShowUpdateIPv4 = (row: any) => {
  isShowUpdateIPv4.value = true;
  updateExtrPublicIpv4Data.value.row = row;
  updateExtrPublicIpv4Data.value.extr_public_ipv4 = row.extr_public_ipv4;
}

const updateExtrPublicIpv4Rules = ref({
  extr_public_ipv4: [
    { required: false, message: '请输入公网IP', trigger: 'blur' }
  ],
});
const updateExtrPublicIpv4Valid = ref();
const updateExtrPublicIpv4 = async () => {
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    let updatedRow = updateExtrPublicIpv4Data.value.row
    // @ts-ignore
    updatedRow.extr_public_ipv4 = updateExtrPublicIpv4Data.value.extr_public_ipv4
    const response = await UpdateObj(updatedRow);
    TinyModal.message({
      message: response.msg,
      status: 'success',
    });
  } catch (error) {
    TinyModal.message({
      message: '更新失败',
      status: 'error',
    });
  }
};

let isShowBelongTicketId = ref(false);
let updateBelongTicketIdData = ref({
  belong_ticket_id: '',
  row: {},
});
const openShowUpdatBelongTicketId = (row: any) => {
  if (row.belong_ticket_id) {
    updateBelongTicketIdData.value.belong_ticket_id = row.belong_ticket_id;
  } else {
    updateBelongTicketIdData.value.belong_ticket_id = props.belong_ticket_id;
  }
  isShowBelongTicketId.value = true;
  updateBelongTicketIdData.value.row = row;
}

const updateBelongTicketIdRules = ref({
  belong_ticket_id: [
    { required: false, message: '请输入主机归属订单ID', trigger: 'blur' }
  ],
});
const updateBelongTicketIdValid = ref();
const updateBelongTicketId = async () => {
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    let updatedRow = updateBelongTicketIdData.value.row;
    // @ts-ignore
    updatedRow.belong_ticket_id = updateBelongTicketIdData.value.belong_ticket_id;
    const response = await UpdateObj(updatedRow);
    TinyModal.message({
      message: response.msg,
      status: 'success',
    });
    reloadGrid();
  } catch (error) {
    TinyModal.message({
      message: '更新失败',
      status: 'error',
    });
  }
};


let isShowExpireAt = ref(false);
let updateExpireAtData = ref({
  expire_at: '',
  server_id: '',
});
const openShowUpdateExpireAt = (row: any) => {
  isShowExpireAt.value = true;
  updateExpireAtData.value.server_id = row.id;
  updateExpireAtData.value.expire_at = row.expire_at;
}

const updateExpireAtRules = ref({
  expire_at: [
    { required: true, message: '请选择时间', trigger: 'blur' }
  ],
});
const updateExpireAtValid = ref();
const updateExpireAt = async () => {
  const valid = await updateExpireAtValid.value.validate();
  if (!valid) {
    TinyModal.message({
      message: '验证失败，请确认数据',
      status: 'error',
    });
    return;
  }
  try {
    // 显示加载状态
    state.loading = true;

    // 发起更新请求
    const response = await updateServerExpireAt(updateExpireAtData.value.server_id, {
      expire_at: updateExpireAtData.value.expire_at,
    });

    // 根据响应内容显示不同的消息提示
    if (response.data.is_warning) {
      TinyModal.message({
        message: response.msg,
        status: 'warning',
        messageClosable: true,
        duration: 10000, // 10秒后自动关闭，可手动关闭
      });
    } else {
      TinyModal.message({
        message: response.msg,
        status: 'success',
      });
    }

    // 重新加载表格数据
    reloadGrid();
  } catch (error) {
    console.error('Error during 更新过期时间错误:', error); // 调试信息
    TinyModal.message({
      message: '更新失败',
      status: 'error',
    });
  } finally {
    // 确保无论成功还是失败都重置加载状态
    state.loading = false;
    isShowExpireAt.value = false;
  }
};


// 软删除、强制删除确认按钮
let isShowDeleteModal = ref(false);
let updateDeleteData = ref({
  'input_ipaddr': '',
  'is_soft_delete': true,
  'ipaddr': '',
  'server_id': '',
  'row': {
    extr_public_ipv4: '',
    name: '',
  },
});
const openShowDelete = (row: any) => {
  isShowDeleteModal.value = true;
  updateDeleteData.value.is_soft_delete = false;
  updateDeleteData.value.server_id = row.id;
  updateDeleteData.value.ipaddr = row.ipaddr;
  updateDeleteData.value.row = row;
}

// const openShowSoftDelete = (row: any) => {
//   isShowDeleteModal.value = true;
//   updateDeleteData.value.is_soft_delete = true;
//   updateDeleteData.value.server_id = row.id;
//   updateDeleteData.value.ipaddr = row.ipaddr;
// }

const updateDelete = async () => {
  // 所有验证通过后
  try {
    if (updateDeleteData.value.is_soft_delete) {
      let server_id = updateDeleteData.value.server_id
      const response = await SoftDeleteOpenstackServer(server_id);
      TinyModal.message({
        message: response.msg,
        status: 'success',
      });
    } else {
      let server_id = updateDeleteData.value.server_id
      const response = await DelObj(server_id);
      TinyModal.message({
        message: response.msg,
        status: 'success',
      });
      isShowDeleteModal.value = false;
      reloadGrid();
    }
  } catch (error) {
    TinyModal.message({
      message: '更新失败',
      status: 'error',
    });
    isShowDeleteModal.value = false;
  }
};

const isShowDeleteButtonVisible = ref(true);

// 监听 server_name 和 input_server_name 的变化
watch(
  () => [updateDeleteData.value.ipaddr, updateDeleteData.value.input_ipaddr],
  ([ipaddr, input_ipaddr]) => {
    // 如果 server_name 等于 input_server_name，则显示删除按钮
    isShowDeleteButtonVisible.value = ipaddr !== input_ipaddr;
  },
  { immediate: true } // 立即执行一次比较
);

const isShowWorkerLogsVisible = ref(false);
const scheduletaskWorkerId = ref('');

const openShowWorkerLogs = (row: any) => {
  isShowWorkerLogsVisible.value = true;
  console.error('row----->', row)
  scheduletaskWorkerId.value = row.scheduletask_work_id || '';
}

// 点击创建按钮
const clickCreateBT = () => {
  router.push(`/portalTenants/portal/portalServer/create`)
}
// 点击申请创建裸机按钮
const clickApplyCreateTenantBaremetalTicket = () => {
  router.push(`/portalTenants/portal/portalServer/applyTenantBaremetalServerTicket`)
}

const confirmSwitchLockServer = (row: any) => {
  TinyModal.confirm(`您确定要【${row.is_lock ? "解锁" : "锁定"}】【${row.ipaddr}|${row.extr_public_ipv4 || row.name}】实例吗？`).then(async (res: string) => {
    if (res === 'confirm') {
      state.loading = true;
      try {
        const response = await UpdateObj(
          {
            "id": row.id,
            "is_lock": !row.is_lock,
            "instance_id": row.instance_id,
          });
        TinyModal.message({
          message: response.msg,
          status: 'success',
        });
        reloadGrid();
      } catch (error) {
        TinyModal.message({
          message: '锁定状态转换失败',
          status: 'error',
        });
      }
      state.loading = false;
    }
  })
}

const confirmStartServer = (row: any) => {
  TinyModal.confirm(`您确定要开机【${row.ipaddr}|${row.extr_public_ipv4 || row.name}】实例吗？`).then(async (res: string) => {
    if (res === 'confirm') {
      state.loading = true;
      try {
        const response = await startServer(row.id);
        TinyModal.message({
          message: response.msg,
          status: 'success',
        });
        reloadGrid();
      } catch (error) {
        TinyModal.message({
          message: '开机任务调度失败',
          status: 'error',
        });
      }
      state.loading = false;
    }
  })
}

const confirmStopServer = (row: any) => {
  TinyModal.confirm(`您确定要关机【${row.ipaddr}|${row.extr_public_ipv4 || row.name}】实例吗？`).then(async (res: string) => {
    if (res === 'confirm') {
      state.loading = true;
      try {
        const response = await stopServer(row.id);
        TinyModal.message({
          message: response.msg,
          status: 'success',
        });
        reloadGrid();
      } catch (error) {
        TinyModal.message({
          message: '关机任务调度失败',
          status: 'error',
        });
      }
      state.loading = false;
    }
  })
}

const confirmRebootServer = (row: any) => {
  TinyModal.confirm(`您确定要重启主机【${row.ipaddr}|${row.extr_public_ipv4 || row.name}】实例吗？锁定后通过平台部分操作需解锁。`).then(async (res: string) => {
    if (res === 'confirm') {
      state.loading = true;
      try {
        const response = await rebootServer(row.id, { reboot_type: 'HARD' });
        TinyModal.message({
          message: response.msg,
          status: 'success',
        });
        reloadGrid();
      } catch (error) {
        TinyModal.message({
          message: '重启任务调度失败',
          status: 'error',
        });
      }
      state.loading = false;
    }
  })
}

const confirmUpdateServertoCmdbHost = (row: any) => {
  TinyModal.confirm(`您确定要将主机【${row.ipaddr}|${row.extr_public_ipv4 || row.name}】同步到运维主机信息内吗？`).then(async (res: string) => {
    if (res === 'confirm') {
      state.loading = true;
      try {
        const response = await updateServerToOPCmdbHost(row.id);
        // 根据响应内容显示不同的消息提示
        if (response.data.is_warning) {
          TinyModal.message({
            message: response.msg,
            status: 'warning',
            messageClosable: true,
            duration: 10000, // 10秒后自动关闭，可手动关闭
          });
        } else {
          TinyModal.message({
            message: response.msg,
            status: 'success',
          });
        }

      } catch (error) {
        TinyModal.message({
          message: '同步失败',
          status: 'error',
        });
      } finally {
        reloadGrid();
        state.loading = false;
      }
    }
  })
}


const handleRetryOPServerSoftwareInstallTask = (row: any) => {
  TinyModal.confirm(`您确定要重试吗？`).then(async (res: string) => {
    if (res === 'confirm') {
      state.loading = true;
      try {
        const response = await retryOPServerSoftwareInstallTask(row.id);
        // 根据响应内容显示不同的消息提示
        if (response.code  !== 200) {
          TinyModal.message({
            message: response.msg,
            status: 'warning',
            messageClosable: true,
            duration: 10000, // 10秒后自动关闭，可手动关闭
          });
        } else {
          TinyModal.message({
            message: response.msg,
            status: 'success',
          });
        }

      } catch (error) {
        TinyModal.message({
          message: '创建任务失败',
          status: 'error',
        });
      } finally {
        reloadGrid();
        state.loading = false;
      }
    }
  })
}



let isShowUpdateServerNameDesc = ref(false);
let updateServerNameDescData = ref({
  id: '',
  name: '',
  description: '',
});
const openShowServerNameDesc = (row: any) => {
  updateServerNameDescData.value.id = row.id;
  updateServerNameDescData.value.name = row.name;
  updateServerNameDescData.value.description = row.description;
  isShowUpdateServerNameDesc.value = true;
}

const confirmUpdateServerNameOrDescription = async () => {
  try {
    state.loading = true;
    let updatedRow = updateBelongTicketIdData.value.row;
    // @ts-ignore
    updatedRow.belong_ticket_id = updateBelongTicketIdData.value.belong_ticket_id;
    const response = await updateServerNameOrDesc(updateServerNameDescData.value.id, {
      name: updateServerNameDescData.value.name,
      description: updateServerNameDescData.value.description,
    });
    TinyModal.message({
      message: response.msg,
      status: 'success',
    });
    reloadGrid();
  } catch (error) {
    TinyModal.message({
      message: '更新主机信息失败',
      status: 'error',
    });
  }
  state.loading = false;
};


let isShowRebuildServer = ref(false);
let rebuildServerData = reactive({
  id: '',
  image_id: '',
  name: '',
  description: '',
  row: {},
});

const openShowRebuildServer = (row: any) => {
  isShowRebuildServer.value = true;
  rebuildServerData.id = row.id;
  rebuildServerData.image_id = row.image_id;
  rebuildServerData.name = row.name;
  rebuildServerData.description = row.description;
  rebuildServerData.row = row;
}

const rebuildServerDataRules = {
  image_id: [
    { required: true, message: '请选择镜像', trigger: 'blur' }
  ],
};

const rebuildServerDataValid = ref();

const confirmRebuildServer = async () => {
  try {
    const valid = await rebuildServerDataValid.value.validate();
    if (!valid) {
      TinyModal.message({
        message: '验证失败，请确认数据',
        status: 'error',
      });
      return;
    }
    // @ts-ignore
    TinyModal.confirm(`您确定要重启主机【${rebuildServerData.row.ipaddr}|${rebuildServerData.row.extr_public_ipv4 || rebuildServerData.row.name}】吗？`).then(async (res: string) => {
      if (res === 'confirm') {
        state.loading = true;
        try {
          const response = await rebuildServer(rebuildServerData.id, {
            name: rebuildServerData.name,
            description: rebuildServerData.description,
            image_id: rebuildServerData.image_id,
          });
          TinyModal.message({
            message: response.msg,
            status: 'success',
          });
          reloadGrid();
        } catch (error) {
          console.error('Error during rebuildServer:', error); // 调试信息
          TinyModal.message({
            message: '重新构建任务调度失败',
            status: 'error',
          });
        } finally {
          state.loading = false;
          isShowRebuildServer.value = false;
        }
      }
    });
  } catch (error) {
    console.error('Error during validation:', error); // 调试信息
  }
};


// 初始化请求数据
interface FilterOptions {
  image_name: string;
  ipaddr: string;
  name: string;
  extr_public_ipv4: string;
  status: string;
  flavor_name: string;
  project_id: string;
  account_id: string;
  belong_ticket_id: string;
  baremetal_node_id: string;
  instance_type: string;
  node: string;
}

// 创建结果展示
const createServerMsgVisibility = ref<boolean>(false);
let createServerMsgData = ref<string | undefined>('');
const opencreateServerMsgDialogBox = (dialogMsg: any) => {
  createServerMsgData.value = dialogMsg || '暂无数据';
  createServerMsgVisibility.value = true;
}


const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input,default,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})


// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    image_name: '',
    ipaddr: '',
    name: '',
    extr_public_ipv4: '',
    status: '',
    flavor_name: '',
    project_id: '',
    account_id: '',
    belong_ticket_id: props.belong_ticket_id,
    baremetal_node_id: '',
    instance_type: props.instanceType,
    node: '',
  },
});

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    total: 10,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

let tableData = ref([]);

const portalServerGrid = ref();
const { loading, filterOptions } = toRefs(state);

// 使用 watch 监听 filterOptions 的变化
watch(() => ({ ...state.filterOptions }), (newFilterOptions: any, oldFilterOptions: any) => {
  // ts-ignore
  const hasChanged = Object.keys(newFilterOptions).some(key => newFilterOptions[key] !== oldFilterOptions[key]);
  if (hasChanged) {
    // 如果有任何查询条件发生变化，则加载数据
    reloadGrid();
  }
}, { deep: true }); // 需要深度监听对象的变化

// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  console.error(queryParmas)
  state.loading = true;

  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;
    // 假设每个主机对象有一个 status 字段，'active' 表示活动主机
    // @ts-ignore
    const activeHostCount = data.filter(item => item.status === 'ACTIVE').length;
    emit('update:activeHostCount', activeHostCount);
    emit('update:allHostCount', data.length);
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
});

function reloadGrid() {
  portalServerGrid?.value.handleFetch();
}

const handleFormReset = () => {
  state.filterOptions = {
    image_name: '',
    ipaddr: '',
    name: '',
    extr_public_ipv4: '',
    status: '',
    flavor_name: '',
    project_id: '',
    account_id: '',
    belong_ticket_id: props.belong_ticket_id,
    baremetal_node_id: '',
    instance_type: props.instanceType,
    node: ''
  };
  reloadGrid();
}

const setCollapse = ref(true);

function collapse() {
  setCollapse.value = false;
}

function extend() {
  setCollapse.value = true;
}


let allAccountsData: any[] = [];
const getAllAccounts = async () => {
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    if (loginUserType === 0) {
      const response = await accountGetListByIds({ is_all: true });
      allAccountsData = response.data
    }
  } catch (error) {
    TinyModal.message({
      message: '创建失败',
      status: 'error',
    });
  }
}


let allBaremetalNodeData: any[] = [];
const getAllBaremetalNodes = async () => {
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    const response = await baremetalNodeGetListByIds({ is_all: true });
    allBaremetalNodeData = response.data
  } catch (error) {
    TinyModal.message({
      message: '创建失败',
      status: 'error',
    });
  }
}

const baseResourceIsLoading = ref(true);

const isShowAccountFilter = ref<boolean>(true);


// 监听 selectedAccountId 的变化 
watch(
  () => props.selectedAccountId, 
  (newVal, oldVal) => {
    if (newVal) {
      filterOptions.value.account_id = newVal;
      isShowAccountFilter.value = false;
    }
  },
  {
    immediate: true // 可选：是否在初始化时立即执行一次 
  }
);

onMounted(async () => {
  try {
    await Promise.all([
      // 获取裸机节点
      getAllBaremetalNodes(),
      // 取消openstack项目过滤
      getAllAccounts(),
    ])
    // 所有异步操作完成后，设置加载状态为 false
    baseResourceIsLoading.value = false;
  } catch {
    baseResourceIsLoading.value = false;
  }

});
</script>

<style scoped lang="less">
.container-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 20px);
  overflow-x: hidden;
  overflow-y: auto;
}

/* Tiny Vue 组件搜索栏修改圆角 */
:deep(.tiny-input__inner) {
  border-radius: 6px;
  /* 设置圆角大小 */
}

:deep(.tiny-button) {
  border-radius: 16px;
  /* 设置圆角大小 */
}

.line {
  height: 1px;
  color: rgb(213, 213, 213);
  background-color: rgb(213, 213, 213);
  border: 0;
}

.contain {
  flex: 1 1 auto;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);

  .contain-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 10px 0 10px;

    hr {
      .line();

      width: 86%;
      margin: 0 20px;
    }

    span {
      color: #1a1818;
      font-size: 16px;
    }
  }

  .contain-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 52px;
  }

  .contain-img {
    position: relative;
    display: flex;
    cursor: pointer;

    img:hover {
      background: whitesmoke;
    }
  }

  .contain-text {
    padding-left: 10px;
    color: #4e5969;
    font-size: 14px;
    cursor: pointer;
  }
}

.bottom-line {
  padding-bottom: 8px;

  hr {
    .line();

    width: 96%;
    margin: 0 auto;
  }
}

.filter-form {
  margin: 10px 0;
}

.col {
  width: 96%;
}

:deep(.tiny-grid) {

  &-header__column,
  &-body__column {

    &.col__selection,
    &.col__radio {
      padding: 0 8px 0 8px;

      &+th,
      +td {
        padding-left: 0;
      }
    }
  }
}

:deep(.tiny-pager) {
  float: right;
}

:deep(.tiny-grid-button__wrapper) {
  width: 100%;
}

:deep(.tiny-form-item__label) {
  color: #494747;
  font-weight: normal;
}

:deep(.tiny-grid-header__column) {
  height: 35px;
  color: rgb(139, 137, 137);
  background-color: #f5f6f7;
}

.operation {
  color: #5e7ce0;
}

.btn {
  display: flex;
  margin-left: 10px;
  width: 100%;
}

.search-btn {
  display: flex;

  button {
    height: 34px;
  }
}

.id-cell {
  display: flex;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    /* 根据需要调整宽度 */
  }
}

.status-success {
  fill: #52c41a;
  margin-right: 8px;
}

.status-danger {
  fill: #C7000B;
  margin-right: 8px;
}

.status-warning {
  fill: #FA9841;
  margin-right: 8px;
}
</style>
