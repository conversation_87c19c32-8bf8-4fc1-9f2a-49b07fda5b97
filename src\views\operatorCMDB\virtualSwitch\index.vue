<template>
  <fs-page>
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <!-- 可以在这里添加自定义的操作按钮 -->
    </fs-crud>
  </fs-page>
</template>

<script lang="ts" setup name="virtualSwitch">
import { onMounted } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});
</script>
