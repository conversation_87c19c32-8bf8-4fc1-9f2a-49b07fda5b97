stages:
  - docker
  - deploy-dev
  - deploy-prod

variables:
  FF_SCRIPT_SECTIONS: 1   # 避免多行命令被折叠
  image_repo: "harbor.hzxingzai.cn/dev/chaos-web-frontend"
  chart_repo: "oci://harbor.hzxingzai.cn/charts/chaos-web"
  chart_version: "0.1.1"

cache:
  paths:
    - ./node_modules

image: harbor.hzxingzai.cn/tools/docker:helm-kubectl

# docker打包
docker_build:
  stage: docker
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev"'
  script:
    - image_tag=$(TZ=Asia/Shanghai date +"%Y%m%d%H%M%S")-${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - |
      if [ -z $CI_COMMIT_TAG ]; then
          echo "使用 ${image_tag} 作为镜像tag";
      else
          echo "使用 ${CI_COMMIT_TAG} 作为镜像tag";
          image_tag=${CI_COMMIT_TAG}
      fi
    - echo "image_tag=${image_tag}"  > vars.env
    - echo "准备构建容器 ---> ${image_repo}:${image_tag}"
    - dockerd-entrypoint.sh &
    - sleep 30
    - echo "开始构建"
    - docker login --username=${HARBOR_USER} --password="${HARBOR_PASSWD}" harbor.hzxingzai.cn
    - docker pull ${image_repo}:latest || true
    - docker buildx build . --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from ${image_repo}:latest --tag ${image_repo}:${image_tag} --tag ${image_repo}:latest --file release/dockerfile
#    - docker build --cache-from ${image_repo}:latest --tag ${image_repo}:${image_tag} --tag ${image_repo}:latest .
#    - docker build --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from ${image_repo}:latest --tag ${image_repo}:${image_tag} --tag ${image_repo}:latest -f release/dockerfile
    - docker images
    - docker push ${image_repo}:${image_tag}
    - docker push ${image_repo}:latest
  tags:
    - dev-k8s
  artifacts:
    reports:
      dotenv: vars.env       # 需要传递给后续阶段的变量

# 部署到Dev环境
deploy-dev:
  image: harbor.hzxingzai.cn/tools/python-helm:0.1
  stage: deploy-dev
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev"'
  script:
    - echo "现在开始部署！${image_repo}:${image_tag}"
    - git clone https://devops:<EMAIL>/deploy/deploy_files.git && cd deploy_files/chaos
    # 备份
    - cp values/dev_web_values.yaml values_backup/dev_web_values-$(TZ=Asia/Shanghai date +"%Y%m%d%H%M%S").yaml
    # 修改镜像
    - yq -y -i ".image.repository = \"$image_repo\"" values/dev_web_values.yaml
    - yq -y -i ".image.tag = \"$image_tag\"" values/dev_web_values.yaml
    # 部署
    - helmfile --environment dev apply
    # 提交修改
    - git config --global user.name "devops"
    - git config --global user.email "<EMAIL>"
    - git config pull.rebase false
    - git add .
    - git fetch && git merge -m "merge before push"
    - git commit -a -m "update deploy files"
    - git push
  environment:
    name: development
    url: http://chaos.dev.hzxingzai.cn/
  tags:
    - dev-k8s

# 部署到Prod环境
deploy-prod:
  image: harbor.hzxingzai.cn/tools/python-helm:0.1
  stage: deploy-prod
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev"'
  script:
    - echo "现在开始部署！${image_repo}:${image_tag}"
    - git clone https://devops:<EMAIL>/deploy/deploy_files.git && cd deploy_files/chaos
    # 备份
    - cp values/prod_web_values.yaml values_backup/prod_web_values-$(TZ=Asia/Shanghai date +"%Y%m%d%H%M%S").yaml
    # 修改镜像
    - yq -y -i ".image.repository = \"$image_repo\"" values/prod_web_values.yaml
    - yq -y -i ".image.tag = \"$image_tag\"" values/prod_web_values.yaml
    # 部署
    - helmfile --environment prod apply
    # 提交修改
    - git config --global user.name "devops"
    - git config --global user.email "<EMAIL>"
    - git config pull.rebase false
    - git add .
    - git fetch && git merge -m "merge before push"
    - git commit -a -m "update deploy files"
    - git push
  environment:
    name: production
    url: http://chaos.hzxingzai.cn/
  tags:
    - prod-k8s
  when: manual   # 手动触发
