import * as api from '../../../api/contract/contract';
import { FsButton } from "@fast-crud/fast-crud";
import {
  UserPageQuery,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict
} from '@fast-crud/fast-crud';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const exportRequest = async (query: UserPageQuery) => {
		return await api.exportData(query);
	};

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        editRequest,
        exportRequest
      },
      toolbar: {
        buttons: {
          // 刷新按钮
          export:{
            show: false,
          },
          //查询按钮
          search:{
              show: false,
          },
          // 刷新按钮
          refresh:{
            show: false,
          },
          // 紧凑模式
          compact:{
            show: false,
          },
          columns:{
            show: true,
            ...FsButton,
            circle: true,
            buttonProps: {
              plain: true,
            },
            icon: 'setting',
            order:1,  // 列排序号，数字越小越靠前排列。 默认值为1, 当配置0或负数则排到最前面，配置2则排到最后面
          },
        }
      },
      actionbar: {
        buttons: {
          add: {
            show: false,
            plain: false,
          },
          export: {
            show: auth('contract:contract:Export'),
            text: "导出",//按钮文字
            title: "导出",//鼠标停留显示的信息
            click() {
              return exportRequest(crudExpose.getSearchFormData())
            }
          }
        }
      }, 
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            link: true,
            type: 'primary',
            show: false,
          },
          edit: {
            link: true,
            type: 'primary',
            show: false,
          },
          remove: {
            link: true,
            type: 'danger',
            show: false,
          },
          viewLog: {
                      type: 'primary',
                      text: '查看日志',
                      link: true,
                      show: auth('system:auditLog:GetResourceLogs'),
                      click(context) {
                        router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
                      }
                    },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        // _index: {
        //   title: '序号',
        //   form: { show: false },
        //   column: {
        //     type: 'index',
        //     align: 'center',
        //     width: '70px',
        //     columnSetDisabled: true, //禁止在列设置中选择
        //   },
        // },
        name: {
          title: '合同名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 150,
            fixed: 'left',
          },
          form: {
            component: {
              placeholder: '请输入合同名称',
            }
          }
        },
        customer_name: {
          title: '客户名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 150,
          },
          form: {
            component: {
              placeholder: '请输入客户名称',
            }
          }
        },
        sign_name: {
          title: '合同签订人',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请输入签订人',
            }
          }
        },
        status: {
          title: '合同状态',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('oamanage:contract:status', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请选择合同状态',
            }
          }
        },
        sign_time: {
          title: '合同双签时间',
          search: {
            show: true,
            component: {
              name: 'el-date-picker',
              props: {
                type: 'month', // 只选择月份
                placeholder: '选择签订日期',
                valueFormat: 'YYYY-MM',
              }
            }
          },
          type: 'month',
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请选择双签时间',
            }
          }
        },
        contract_life: {
          title: '合同有效时间',
          key: 'contract_life', // 虚拟字段，实际数据来自start_time和end_time 
          column: { minWidth: 150,}
        },
        type: {
          title: '合同类型',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('oamanage:product:label', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请选择合同类型',
            }
          }
        },
        amount: {
          title: '合同金额',
          type: 'number',
          search: {
            show: true,
          },
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请输入合同金额',
            }
          }
        },
        code: {
          title: '合同流程编号',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请输入编号',
            }
          }
        },
        applicant: {
          title: '合同申请人',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            component: {
              placeholder: '请输入合同申请人',
            }
          }
        },
      },
    },
  };
};
