<!-- eslint-disable no-unused-vars -->
<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
      <template #cell-rowHandle-middle="scope">
        <el-divider direction="vertical" />
      </template>
			<!-- <template #cell_url="scope">
				<el-tag size="small">{{ scope.row.url }}</el-tag>
			</template> -->
		</fs-crud>

		<!-- <permission ref="rolePermission"></permission> -->
		<PermissionComNew v-model:drawerVisible="drawerVisible" :roleId="roleId" :roleName="roleName" @drawerClose="handleDrawerClose" />
    <SelectRoleRows v-model:rowPermDialogVisible="rowPermDialogVisible" :roleId="roleId" :roleName="roleName" @dialogClose="handleDialogClose" />
    <UsersInfo v-model:usersInfoDrawerDialogVisible="usersInfoDrawerDialogVisible" :roleId="roleId" :roleName="roleName" @dialogClose="handleUsersInfoDialogClose" />
	</fs-page>
</template>

<script lang="ts" setup name="role">
import {ref, onMounted} from 'vue';

import { GetPermission } from '/@/views/system/api/role';
import { useExpose, useCrud } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import PermissionComNew from './components/PermissionComNew/index.vue';
import SelectRoleRows from './components/SelectRoleRows/index.vue';
import UsersInfo from './components/UsersInfo/index.vue';

// eslint-disable-next-line no-unused-vars
import _ from "lodash-es";
import { handleColumnPermission } from "/@/utils/columnPermission";
let drawerVisible = ref(false);
let rowPermDialogVisible = ref(false);
let usersInfoDrawerDialogVisible = ref(false);


let roleId = ref();
let roleName = ref('Unknown');

const rolePermission = ref();
// crud组件的ref
const crudRef = ref();
// crud 配置的ref
const crudBinding = ref();


const handleDrawerOpen = (row: any) => {
	roleId.value = row.id;
	roleName.value = row.name;
	drawerVisible.value = true;
};

const handlePerm = (row: any) => {
  roleId.value = row.id;
	roleName.value = row.name;
  rowPermDialogVisible.value = true;
};


const handleUsersInfo = (row: any) => {
  roleId.value = row.id;
	roleName.value = row.name;
  usersInfoDrawerDialogVisible.value = true;
};


const handleDrawerClose = () => {
	drawerVisible.value = false;
};

const handleDialogClose = () => {
	rowPermDialogVisible.value = false;
};

const handleUsersInfoDialogClose = () => {
	usersInfoDrawerDialogVisible.value = false;
};

const { crudExpose } = useExpose({ crudRef, crudBinding });

// 你的crud配置
const { crudOptions } = createCrudOptions({ crudExpose, rolePermission, handlePerm, handleDrawerOpen, handleUsersInfo});

// 初始化crud配置
// @ts-ignore
// eslint-disable-next-line no-unused-vars
const { resetCrudOptions } = useCrud({
  crudExpose,
  crudOptions,
  context: {},
});

// 页面打开后获取列表数据
onMounted( async () => {

  // eslint-disable-next-line no-unused-vars
  const newOptions = await handleColumnPermission(GetPermission, crudOptions)


  //重置crudBinding
  resetCrudOptions(newOptions);
	crudExpose.doRefresh();
});

// defineExpose(rolePermission);
</script>
