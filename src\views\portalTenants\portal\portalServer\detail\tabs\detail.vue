<template>
  <div class="detail-base-cards">
    <div>
      <tiny-layout :cols="24">
        <tin-row :gutter="40">
        <!--- 左侧信息区---->
          <tiny-col :span="12">
            <tiny-card title="网络信息" class="detial-sub-cards" :auto-width="true">
              <tiny-layout :cols="24">
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    网络
                  </tiny-col>
                  <tiny-col :span="16">
        {{ detailInstance.net_name }} | {{ detailInstance.ipaddr }}
                  </tiny-col>
                </tiny-row>
              </tiny-layout>
            </tiny-card>
            <tiny-card title="配置信息" class="detial-sub-cards" :auto-width="true">
              <tiny-layout :cols="24">
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    云主机类型名称
                  </tiny-col>
                  <tiny-col :span="16">
                      {{ detailInstance.flavor_name }}
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    内存
                  </tiny-col>
                  <tiny-col :span="16">
                      {{ Math.round(detailInstance.ram / 1024) }} GiB
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    CPU核数
                  </tiny-col>
                  <tiny-col :span="16">
                      {{ detailInstance.vcpus }}
                  </tiny-col>
                </tiny-row>
              </tiny-layout>
            </tiny-card >
            <tiny-card title="镜像信息" class="detial-sub-cards" :auto-width="true">
              <tiny-layout :cols="24">
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    镜像名称
                  </tiny-col>
                  <tiny-col :span="16">
                      {{ detailInstance.image_name }}
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    镜像ID
                  </tiny-col>
                  <tiny-col :span="16">
                    <tiny-link :underline="false" type="primary" :disabled="true">{{ detailInstance.image_id }}</tiny-link>
                  </tiny-col>
                </tiny-row>
              </tiny-layout>
            </tiny-card>
            <tiny-card title="安全组信息" class="detial-sub-cards" :auto-width="true">
              <tiny-layout :cols="24">
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    名称
                  </tiny-col>
                  <tiny-col :span="16">
                    <div v-for="sec_group in detailInstance.security_groups" :key="sec_group.name">
                      <tiny-link :underline="false" type="primary" :disabled="true"> {{ sec_group.name }} </tiny-link>
                    </div>
                  </tiny-col>
                </tiny-row>
              </tiny-layout>
            </tiny-card>
            <tiny-card title="秘钥信息" class="detial-sub-cards" :auto-width="true">
              <tiny-layout :cols="24">
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    名称
                  </tiny-col>
                  <tiny-col :span="16">
                    <tiny-link :underline="false" type="primary" :disabled="true"> {{ detailInstance.key_name }} </tiny-link>
                  </tiny-col>
                </tiny-row>
              </tiny-layout>
            </tiny-card>
          </tiny-col>
           <!--- 右侧信息区 ---->
          <tin-col :span="12">
            <tiny-card title="基础信息" class="detial-sub-cards" :auto-width="true">
              <tiny-layout :cols="24">
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    过期时间
                  </tiny-col>
                  <tiny-col :span="16">
                    {{ detailInstance.expire_at }}
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    同步时间
                  </tiny-col>
                  <tiny-col :span="16">
                    {{ detailInstance.sync_time }}
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    运行时间
                  </tiny-col>
                  <tiny-col :span="16">
                    {{ detailInstance.launched_at }}
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    所属项目
                  </tiny-col>
                  <tiny-col :span="16">
                    {{ detailInstance.project_id }}
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    所属账号
                  </tiny-col>
                  <tiny-col :span="16">
                    {{ detailInstance.project_id }}
                  </tiny-col>
                </tiny-row>
                <tiny-row :gutter="20">
                  <tiny-col :span="8">
                    所属工单
                  </tiny-col>
                  <tiny-col :span="16">
                    {{ detailInstance.belong_ticket_id }}
                  </tiny-col>
                </tiny-row>
              </tiny-layout>
            </tiny-card>
          </tin-col>
        </tin-row>
      </tiny-layout>
  </div>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from 'vue';
import type { DetailInstance } from '/@/views/interface/tenant/opServer';
import { TinyCard, TinyLayout, TinyRow, TinyCol, } from '@opentiny/vue';


defineProps({
  detailInstance: {
    type: Object as PropType<DetailInstance>,
    required: true,
  },
});

</script>
<style scopd>
/* 卡片内容限制 */
.detail-base-cards {
  margin: 16px;
  box-sizing: border-box;
  box-shadow: none;
}
.detial-sub-cards {
  line-height: 2.4;
  margin-bottom: 16px;
}
</style>