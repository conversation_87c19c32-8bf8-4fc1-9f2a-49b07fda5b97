<template>
  <tiny-card custom-class="stat-cards" type="text">
    <h1 class="dashboard-sub-title">使用量概况</h1>
    <tiny-layout>
      <tiny-row :gutter="10" flex>
        <tiny-col v-for="metric in metrics" :key="metric.label"  :span="8">
          <div class="metric-card">
            <tiny-progress 
              type="dashboard"
              :stroke-width="12"
              :percentage="metric.percentage" 
              :color="metric.colors"
              :format="metric.format" 
            />
            <span class="metric-label">{{ metric.label  }}</span>
          </div>
        </tiny-col>
      </tiny-row>
    </tiny-layout>
  </tiny-card>
</template>
 
<script setup lang="ts">
import { computed } from 'vue'
import { TinyCard, TinyProgress, TinyRow, TinyCol, TinyLayout } from '@opentiny/vue'
import type { PropType } from 'vue'
 
const props = defineProps({
  quota: {
    type: Object as PropType<{ cores: number; instances: number; ram: number }>,
    required: true 
  },
  usage: {
    type: Object as PropType<{ cores: number; instances: number; ram: number }>,
    required: true 
  }
})

// 动态颜色配置 
const cpuColors = [
  { color: '#8EC5FC', percentage: 20 },
  { color: '#409EFF', percentage: 40 },
  { color: '#3375B9', percentage: 60 },
  { color: '#254F75', percentage: 80 },
  { color: '#162D47', percentage: 100 }
];
 
const instanceColors = [
  { color: '#FFE7C4', percentage: 20 },
  { color: '#FFC77C', percentage: 40 },
  { color: '#FFA726', percentage: 60 },
  { color: '#F57C00', percentage: 80 },
  { color: '#E65100', percentage: 100 }
];
 
const ramColors = [
  { color: '#C8E6C9', percentage: 20 },
  { color: '#81C784', percentage: 40 },
  { color: '#4CAF50', percentage: 60 },
  { color: '#388E3C', percentage: 80 },
  { color: '#1B5E20', percentage: 100 }
];
 
const metrics = computed(() => [
  {
    label: 'vCPU核数',
    percentage: calculatePercentage(props.usage.cores,  props.quota.cores), 
    colors: cpuColors,
    format: () => `${props.usage.cores}  / ${props.quota.cores  || '∞'}`
  },
  {
    label: '实例数量',
    percentage: calculatePercentage(props.usage.instances,  props.quota.instances), 
    colors: instanceColors,
    format: () => `${props.usage.instances}  / ${props.quota.instances  || '∞'}`
  },
  {
    label: '内存(GB)',
    percentage: calculatePercentage(props.usage.ram,  props.quota.ram), 
    colors: ramColors,
    format: () => `${props.usage.ram}  / ${props.quota.ram  || '∞'}`
  },
  // 其他指标配置 
])
 
const calculatePercentage = (used: number, total: number) => 
  total ? Math.min(Math.round((used  / total) * 100), 100) : 100 
</script>
 
<style scoped>
.dashboard-sub-title {
  font-size: 16px;
  margin-bottom: 20px;
}
/* 组件特定样式 */
.metric-card {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.metric-label {
  margin-top: 12px;
  font-size: 16px;
  letter-spacing: 0.5px;
  font-weight: bolder;
}
/* 卡片内容限制 */
.stat-cards {
  width: calc(100% - 32px); /* 补偿margin */
  max-width: 100%;
  margin: 16px;
  padding-right: 16px !important; /* 覆盖行内样式 */
  box-sizing: border-box;
  box-shadow: none;
}
</style>