export interface DetailInstance {
  id: string;
  instance_id: string;
  instance_type: string;
  name: string;
  description: string;
  compute_host: string;
  net_name: string;
  ipaddr: string;
  image_id: string;
  image_name: string;
  key_name: string;
  flavor_id: string;
  flavor_name: string;
  vcpus: number;
  disk: number;
  ram: number;
  security_groups: any;
  status: string;
  vm_status: string;
  host_id: string;
  hostname: string;
  private_v4: string;
  public_v4: string;
  baremetal_node_id: string;
  create_msg: string;
  create_datetime: string;
  project_id: string;
  is_reachable: boolean;
  last_check_reachable_time: string;
  belong_ticket_id: string;
  launched_at: string;
  sync_time: string;
  expire_at: string;
}
