<template>
  <div class="detail-base-cards">
    <el-descriptions :column="3" size="large">
      <template #title>
        <div class="instance-id-wrapper">
          <span>实例ID：{{detailInstance.instance_id}}</span>
          
            <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(detailInstance.instance_id)"></tiny-link>
          <span style="padding-left: 20px; padding-right:20px;">|</span>
            <tiny-link type="primary" @click="router.go(-1)">返回</tiny-link>
        </div>
        
      </template>
      <template #extra>
        <el-button type="primary" :disabled="true">操作</el-button>
      </template>
      <el-descriptions-item label="名称" label-class-name="desc-customer-label" class-name="desc-customer-content">{{ detailInstance.name }}</el-descriptions-item>
      <el-descriptions-item label="描述" label-class-name="desc-customer-label" class-name="desc-customer-content">{{ detailInstance.description }}</el-descriptions-item>
      <el-descriptions-item label="状态" label-class-name="desc-customer-label" class-name="desc-customer-content">
        <el-tag size="small">{{ detailInstance.status }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="创建于" label-class-name="desc-customer-label" class-name="desc-customer-content">
        {{ detailInstance.create_datetime }}
      </el-descriptions-item>
      <el-descriptions-item label="区域" label-class-name="desc-customer-label" class-name="desc-customer-content">
        {{ detailInstance.node }}
      </el-descriptions-item>
      <el-descriptions-item label="裸机节点" label-class-name="desc-customer-label" class-name="desc-customer-content">
        <el-link type="primary" :underline="false" @click="toOPIronHypervisiorDetail(detailInstance.baremetal_node_id)">{{ detailInstance.baremetal_node_id || '-' }}</el-link>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script lang="ts" setup>
import { PropType } from 'vue';
import type { DetailInstance } from '/@/views/interface/tenant/opServer';
import {useRouter} from 'vue-router';

import { iconCopy, iconConmentRefresh } from '@opentiny/vue-icon';
import { copyText } from '/@/utils/copyText';
import { toOPIronHypervisiorDetail } from '/@/router/intervalRouterTo/tenant'

const router = useRouter();
const TinyIconCopy = iconCopy();
const TinyIconConmentRefresh = iconConmentRefresh();

const props = defineProps({
  detailInstance: {
    type: Object as PropType<DetailInstance>,
    required: true,
  },
});
</script>
<style lang="less" scoped>
.instance-id-wrapper {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  max-width: 100%;
}


/* 卡片内容限制 */
.detail-base-cards {
  margin: 16px;
  box-sizing: border-box;
  box-shadow: none;
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #909399;
}
/* Tiny Vue 组件搜索栏修改圆角 */
:deep(.tiny-input__inner) {
  border-radius: 6px;
  /* 设置圆角大小 */
}

:deep(.tiny-button) {
  border-radius: 16px;
  /* 设置圆角大小 */
}

:deep(.desc-customer-label) {
  color: #909399;
  font-weight: bolder;
}
:deep(.desc-customer-content) {
  color: #000;
}
</style>

