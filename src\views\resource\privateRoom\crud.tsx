import * as api from './api';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict,

} from '@fast-crud/fast-crud';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';
import { verifyPhone } from '/@/utils/toolsValidate';
import { useRouter } from 'vue-router';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  const validatePhone = async (rule: object, value: string) => {
    if (!verifyPhone(value)) {
      throw new Error('手机号有误')
    }
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('resource:privateRoom:Create'),
            plain: true,
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 280,
        buttons: {
          view: {
            type: 'primary',
            link: true,
            show: auth('resource:privateRoom:Retrieve')
          },
          edit: {
            type: 'primary',
            link: true,
            show: auth('resource:privateRoom:Update')
          },
          remove: {
            type: 'danger',
            link: true,
            show: auth('resource:privateRoom:Delete')
          },
          viewLog: {
            type: 'primary',
            text: '查看日志',
            link: true,
            show: auth('system:auditLog:GetResourceLogs'),
            click(context) {
              router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
            }
          },
        },
      },
      pagination: {
        show: true
      },
      table: {
        rowKey: 'id',
        // border: false,
      },
      form: {
        labelWidth: 100,
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        machine_room: {
          title: '机房',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/machine_room/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            // cache: true,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择机房',
            }
          },
        },
        name: {
          title: '包间名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '包间名称为必填项'},
              {min: 1, max: 255, message: '最小: 1, 最大: 255', trigger: 'blur'}
            ],
            component: {
              placeholder: '请输入包间名称',
            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
            ],
            component: {
              placeholder: '请输入描述',
            }
          }
        },
        private_room_type: {
          title: '包间类型',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('private_room:private_room_type', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '包间名称为必填项'},
            ],
            component: {
              placeholder: '请输入包间名称',
            }
          }
        },
        block_sn: {
          title: '楼号',
          search: {
            show: false,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '楼号为必填项'},
            ],
            component: {
              placeholder: '请输入楼号',
            }
          }
        },
        floor_sn: {
          title: '楼层',
          search: {
            show: false,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '楼层为必填项'},
            ],
            component: {
              placeholder: '请输入楼层',
            }
          }
        },
        in_service_date: {
          title: '正式入使用时间',
          search: {
            show: false,
          },
          type: 'date',
          column: {
            minWidth: 90,
            show: true,
          },
          form: {
            component: {
              placeholder: '请选择',
              //输入输出值格式化
              valueFormat: "YYYY-MM-DD",
            }
          }
        },
        status: {
          title: '状态',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('private_room:status', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '包间状态为必填项'},
            ],
            component: {
              placeholder: '请输入包间状态',
            }
          }
        },
        manager_name: {
          title: '责任人名称',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '责任人必填项'},
              {max: 63, min: 2, message: '输入有误', trigger: 'blur'},
            ],
            component: {
              placeholder: '请输入责任人名称',
            }
          }
        },
        manager_phone: {
          title: '责任人电话',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '责任人电话必填项'},
              {validator: validatePhone, message: '手机号有误', trigger: 'blur'},
            ],
            component: {
              placeholder: '请输入责任人电话',
            }
          }
        },
        manager_email: {
          title: '责任人邮箱',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '责任人邮箱非必填项'},
              {type: 'email', message: '请准确输入邮箱', trigger: 'blur'},
            ],
            component: {
              placeholder: '请输入邮箱',
            }
          }
        },
        out_of_service_date: {
          title: '解约时间',
          search: {
            show: false,
          },
          type: 'date',
          column: {
            minWidth: 90,
            show: true
          },
          form: {
            component: {
              placeholder: '请选择',
              //输入输出值格式化
              valueFormat: "YYYY-MM-DD",
            }
          }
        },
        planed_service_date: {
          title: '规划开始时间',
          search: {
            show: false,
          },
          type: 'date',
          column: {
            minWidth: 90,
            show: true,
          },
          form: {
            component: {
              placeholder: '请选择',
              //输入输出值格式化
              valueFormat: "YYYY-MM-DD",
            }
          }
        },
        build_service_date: {
          title: '建设开始时间',
          search: {
            show: false,
          },
          type: 'date',
          column: {
            minWidth: 90,
            show: true
          },
          form: {
            component: {
              placeholder: '请选择',
              //输入输出值格式化
              valueFormat: "YYYY-MM-DD",
            }
          }
        },
        last_inspection_date: {
          title: '最近一次检修时间',
          search: {
            show: false,
          },
          type: 'datetime',
          column: {
            minWidth: 90,
            show: true
          },
          form: {
            component: {
              placeholder: '请选择',
              //输入输出值格式化
              valueFormat: "YYYY-MM-DD HH:mm:ss",
            }
          }
        },
      },
    },
  };
};
