<template>
  <div class="export-container">
    <slot></slot>
  </div>
</template>
 
<script lang="ts">
import { defineComponent } from 'vue';
import { ElLoading, ElMessage } from 'element-plus';
import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
 
export default defineComponent({
  name: 'Export2PDF',
  props: {
    fileName: {
      type: String,
      default: 'export.pdf' 
    },
    scale: {
      type: Number,
      default: 1.5 
    },
    backgroundColor: {
      type: String,
      default: '#FFFFFF'
    },
    targetClass: {
      type: String,
      default: 'export-container'
    }
  },
  setup(props) {
    const exportAsPDF = async () => {
      try {
        // 1. Handle all ECharts instances 
        const charts = Array.from(document.querySelectorAll('[data-echarts-instance]')); 
        await Promise.all(charts.map(chart  => {
          const instance = (chart as any).__echarts__;
          return new Promise(resolve => {
            instance.on('finished',  resolve);
            instance.resize(); 
          });
        }));
 
        // 2. Get target element 
        const target = document.querySelector(`.${props.targetClass}`)  as HTMLElement;
        if (!target) throw new Error('Export container not found');
 
        // 3. Temporarily modify styles for complete capture 
        const originalStyles = {
          overflow: target.style.overflow, 
          height: target.style.height  
        };
        target.style.overflow  = 'visible';
        target.style.height  = 'auto';
 
        // 4. Generate screenshot 
        const canvas = await html2canvas(target, {
          scale: props.scale, 
          useCORS: true,
          allowTaint: true,
          backgroundColor: props.backgroundColor, 
          logging: false,
          scrollY: 0,
          windowHeight: target.scrollHeight  
        });
 
        // 5. Restore original styles 
        target.style.overflow  = originalStyles.overflow; 
        target.style.height  = originalStyles.height; 
 
        // 6. Create PDF with custom size to fit content 
        const imgWidth = 595.28; // A4 width in points (210mm)
        const imgHeight = (canvas.height  * imgWidth) / canvas.width; 
        
        // Create PDF with calculated height 
        const pdf = new jsPDF({
          orientation: imgHeight > imgWidth ? 'portrait' : 'landscape',
          unit: 'pt',
          format: [imgWidth, imgHeight + 40] // Add some padding 
        });
 
        pdf.addImage( 
          canvas.toDataURL('image/jpeg',  0.95),
          'JPEG',
          20, 20, imgWidth - 40, imgHeight 
        );
 
        // 7. Save file 
        pdf.save(props.fileName); 
        ElMessage.success(' 导出成功');
      } catch (error) {
        console.error(' 导出失败:', error);
        ElMessage.error(` 导出失败: ${(error as Error).message}`);
      } finally {
        ElLoading.service().close(); 
      }
    };
 
    // Expose method to parent component 
    return {
      exportAsPDF 
    };
  }
});
</script>
 
<style scoped>
.export-container {
  width: 100%;
  position: relative;
}
</style>