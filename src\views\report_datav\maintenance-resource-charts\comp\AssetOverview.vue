<template>
  <tiny-card class="asset-overview-card">
    <header class="card-header">
      <h2 class="card-title">资产概述</h2>
    </header>
    
    <div class="stats-container">
      <div class="stats-grid">
        <div 
          v-for="(item, index) in statsItems" 
          :key="index"
          class="stat-item"
        >
          <ChartStatistics 
            :data="currentData[item.key]"
            :config="item.config" 
          />
        </div>
        
        <!-- 添加空div保持最后一行对齐（当总数不是4的倍数时） -->
        <div 
          v-for="i in placeholderCount" 
          :key="'placeholder-'+i"
          class="stat-item placeholder"
        ></div>
      </div>
    </div>
  </tiny-card>
</template>
 
<script setup lang="ts">
import { TinyCard } from '@opentiny/vue'
import ChartStatistics from '/@/components/chaosCharts/chartStatistics.vue' 
import { PropType, ref, watchEffect, computed } from 'vue'
 
interface AssetOverviewData {
  total_maintenance: number 
  resolved_count: number 
  resolved_rate: number 
  reported_count: number 
  reported_rate: number 
  in_progress_count: number 
  in_progress_rate: number 
}

interface StatItem {
  key: keyof AssetOverviewData
  config: {
    name: string
    unit: string 
    tooltip?: string 
    rate?: number
  }
}
 
const props = defineProps({
  data: {
    type: Object as PropType<AssetOverviewData>,
    required: true,
    default: () => ({
      total_maintenance: 0,
      resolved_count: 0,
      resolved_rate: 0,
      reported_count: 0,
      reported_rate: 0,
      in_progress_count: 0,
      in_progress_rate: 0
    })
  }
})
 
const currentData = ref<AssetOverviewData>(props.data) 
 
const statsItems = computed<StatItem[]>(() => [
  { 
    key: 'total_maintenance', 
    config: { 
      name: '维修总量', 
      unit: '个',
      tooltip: '所有维修记录总数'
    } 
  },
  { 
    key: 'resolved_count', 
    config: { 
      name: '已解决', 
      unit: '个',
      rate: currentData.value.resolved_rate,
      tooltip: `已解决维修记录，占比${currentData.value.resolved_rate}%`
    } 
  },
  { 
    key: 'reported_count', 
    config: { 
      name: '已上报', 
      unit: '个',
      rate: currentData.value.reported_rate,
      tooltip: `已上报维修记录，占比${currentData.value.reported_rate}%`
    } 
  },
  { 
    key: 'in_progress_count', 
    config: { 
      name: '进行中', 
      unit: '个',
      rate: currentData.value.in_progress_rate,
      tooltip: `进行中维修记录，占比${currentData.value.in_progress_rate}%`
    } 
  }
])
 
// 计算需要多少个占位元素（确保总数为4的倍数）
const placeholderCount = computed(() => {
  const remainder = statsItems.value.length % 4 
  return remainder > 0 ? 4 - remainder : 0 
})
 
watchEffect(() => {
  currentData.value = props.data  
})
</script>
 
<style scoped>
.asset-overview-card {
  width: calc(100% - var(--card-margin) * 2);
  margin: var(--card-margin);
  padding: var(--card-padding);
  height: 320px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}
 
.card-header {
  margin-bottom: 24px;
}
 
.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}
 
.stats-container {
  width: 100%;
}
 
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}
 
.stat-item {
  min-width: 0; /* 防止内容溢出 */
}
 
.placeholder {
  visibility: hidden; /* 隐藏占位元素 */
}
 
/* 响应式调整 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
 
@media (max-width: 992px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
 
@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .asset-overview-card {
    --card-padding: 16px;
    --card-margin: 8px;
  }
  
  .card-header {
    margin-bottom: 16px;
  }
}
</style> 