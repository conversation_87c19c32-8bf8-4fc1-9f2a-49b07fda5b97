<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #actionbar-left>
				<el-button v-if="auth('contract:payment:Create')" type="primary" plain @click="addPayment">添加</el-button>
			</template>
			<template #cell_pre_payment_amount="{ row }">
				<span>
					{{
					(() => {
					let total = (row.payment_list || []).reduce((sum, item) => {
					return sum + (item.payment_data_list || []).reduce((subSum, payData) => {
					return subSum + Number(payData.payment_amount || 0);
					}, 0);
					}, 0);
					// 判断是否需要加入 cash_pledge
					if (row.cash_pledge && row.cashple_pay_time) {
					total += Number(row.cash_pledge || 0);
					}
					return total.toFixed(2);
					})()
					}}
				</span>
			</template>
			<template #cell_pre_payment_time="{ row }">
				<span>
					{{
					(() => {
					const times = (row.payment_list || []).flatMap(item =>
					(item.payment_data_list || []).map(payData => payData.payment_time)
					).filter(Boolean); // 过滤空值
					if (times.length === 0) return '-';
					return times.sort((a, b) => new Date(b) - new Date(a))[0];
					})()
					}}
				</span>
			</template>

			<template #cell_payment_time="{ row }">
				<span v-if="row.payment_time">{{ formatDate(row.payment_time) }}</span>
				<span v-else>-</span>
			</template>
			<template #cell-rowHandle-left="scope">
				<el-button
					v-if="auth('contract:payment:Retrieve')"
					type="primary"
					link
					@click="viewPayment(scope.row)"
				>详情</el-button>
				<el-button
					v-if="auth('contract:payment:Update')"
					type="primary"
					link
					@click="editPayment(scope.row)"
				>编辑</el-button>
			</template>
		</fs-crud>
		<el-drawer
			v-model="paymentDialogVisible"
			:with-header="false"
			@open="isRemount = !isRemount"
			size="75%"
		>
			<!-- <template #header="{ close, titleId, titleClass }">
				<el-text tag="b" :id="titleId" :class="titleClass">{{ method=="Update"?"更新":"创建" }}合同</el-text>
			</template>-->
			<CreateOrEditFrame
				:key="isRemount"
				:method="method"
				@submitHandler="submit"
				:item="item"
				resourceType="payment"
			>
				<template #header>
					<div
						v-if="method !== 'Detail'"
						style="font-size: 25px;margin:10px 0 10px 15px"
					>{{ method=="Update"?"更新":"创建" }}回款</div>
					<div v-else style="font-size: 25px;margin:10px 0 10px 15px">回款详情</div>
				</template>
			</CreateOrEditFrame>
		</el-drawer>
	</fs-page>
</template>

<script setup>
import { ref,onMounted,reactive,toRefs } from 'vue'
import * as api from '/@/api/contract/payment';
import { ElMessage } from 'element-plus'
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { useRoute } from 'vue-router';
import CreateOrEditFrame from '../components/CreateOrEditFrame.vue';
import { auth } from '/@/utils/authFunction';
import dayjs from 'dayjs';

const router = useRoute();
const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
const paymentDialogVisible = ref(false);
const isRemount = ref(false)
const method = ref("Create")
const data = reactive({
  item:{}
})

const formatDate = (datetime) => {
    return dayjs(datetime).format('YYYY-MM-DD')
};

const submit = (row) => {
  api.SaveObj(row).then((response) => {
    if (response.code === 2000) {
      crudExpose.doRefresh();
		} else {
			ElMessage.error(response.msg);
		}
  })
  paymentDialogVisible.value = false;
}

const addPayment = () => {
  method.value = "Create"
  paymentDialogVisible.value = true;
}

const viewPayment = async(row) => {
  method.value = "Detail"
  await api.GetObj(row.id).then((response) => {
		if (response.code === 2000) {
      data.item = response.data
		} else {
			ElMessage.error(response.msg);
		}
  });
  paymentDialogVisible.value = true;
}

const editPayment = async(row) => {
  method.value = "Update"
  await api.GetObj(row.id).then((response) => {
		if (response.code === 2000) {
      data.item = response.data
		} else {
			ElMessage.error(response.msg);
		}
  });
  paymentDialogVisible.value = true;
}

// 页面打开后获取列表数据
onMounted(() => {
	const requestQuery = router.query;
	if (requestQuery.code) {
		crudExpose.doSearch({ form: { contract_code: requestQuery.code } });
	} else {
		crudExpose.doRefresh();
	}
});

const { item } = toRefs(data);
</script>
<style scoped>
</style>