
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/network";

class NetworkAPI {
  /**
   * 获取网络分页列表
   *
   * @param queryParams 查询参数
   * @returns 网络分页结果
   */
  static getPage(queryParams: NetworkPageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取网络表单数据
   *
   * @param id 网络ID
   * @returns 网络表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 获取网络列表
   *
   * @returns 网络列表
   */
  static getList(project_name?: string) {
    let url = `${BASE_URL}/list/`
    if (project_name) {
      url += `?project_name=${project_name}`;
    }
    return request({
      url: url,
      method: "get",
    });
  }

}

export default NetworkAPI;

/**
 * 网络查询参数
 */
export interface NetworkPageQuery extends PageQuery {
  search?: string;    // keywords
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}

/**
 * 网络分页数组中的单个对象
 */
export interface NetworkPageVO {
  net_id: string;
  name: string;
  project_name: string;
  mtu: number;
  status: string;
  availability_zones: string;
  is_admin_state_up: number;
  is_router_external: number;
  is_shared: number;
  is_default: number;
  provider_network_type: string;
  provider_physical_network: string;
  provider_segmentation_id: number;
}

/**
 * 网络表单
 */
export interface NetworkForm {
  net_id: string;
  name: string;
  project_name: string;
  mtu: number;
  status: string;
  availability_zones: string;
  is_admin_state_up: number;
  is_router_external: number;
  is_shared: number;
  is_default: number;
  provider_network_type: string;
  provider_physical_network: string;
  provider_segmentation_id: number;
}

/**
 * 网络List元素
 */
export interface NetworkListEle {
  net_id: string;
  name: string;
}