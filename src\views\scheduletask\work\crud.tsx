import * as api from '../../../api/scheduletask/work';
import { UserPageQuery, AddReq, CreateCrudOptionsProps, CreateCrudOptionsRet } from '@fast-crud/fast-crud';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};

	return {
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
			},
			actionbar: {
				buttons: {
					add: {
						show: false,
						plain: true,
						type: 'primary',
					},
				},
      },
      header: {
        buttons: {
          search:{
            show: false,
          },
        }
      },
      toolbar: {
        buttons: {
          //查询按钮
          columns:{
            show: false,
          },
          // 刷新按钮
          export:{
            show: false,
          },
          //查询按钮
          search:{
              show: false,
          },
          // 刷新按钮
          refresh:{
            show: false,
          },
          // 紧凑模式
          compact:{
            show: false,
          },
        }
      },
			search: {
				col: { span: 6 },
				padding: 0,
			},
			rowHandle: {
				//固定右侧
				fixed: 'right',
				width: 200,
				buttons: {
					view: {
						link: true,
						type: 'primary',
						show: true,
					},
					edit: {
						link: true,
						type: 'primary',
						show: false,
					},
					remove: {
						link: true,
						type: 'danger',
						show: false,
					},
				},
			},
			pagination: {
				show: true,
			},
			table: {
				rowKey: 'id',
      },
			columns: {
				template_name: {
					title: '模板名称',
					search: {
						show: true,
						// col:{span:8}
					},
					type: 'input',
					column: {
						width: 150,
						fixed: 'left',
					},
					form: {
						show: false,
						component: {
							placeholder: '输入模板名称',
						},
					},
				},
				host_list: {
					title: '主机列表',
					type: 'input',
					column: {
						minWidth: 100,
					},
				},
				params: {
					title: '运行时参数',
					type: 'input',
					column: {
						minWidth: 100,
					},
				},
				// stdout_path: {
				// 	title: '日志路径',
				// 	type: 'input',
				// 	column: {
				// 		minWidth: 200,
				// 	},
				// },
				create_datetime: {
					title: '创建时间',
					type: 'input',
					column: {
						width: 100,
					},
        },
        creator_name: {
					title: '创建人',
					type: 'input',
					search: {
						show: true,
						// col:{span:8}
					},
					column: {
						width: 100,
					},
					form: {
						component: {
							placeholder: '请输入创建人',
						},
					},
				},
				description: {
					title: '描述',
					type: 'input',
					search: {
						show: true,
						// col:{span:8}
					},
					column: {
						width: 150,
						fixed: 'right',
					},
					form: {
						component: {
							placeholder: '请输入描述',
						},
					},
				},
				status: {
					title: '状态',
					type: 'input',
					column: {
						width: 60,
						fixed: 'right',
					},
					form: {
						component: {
							placeholder: '状态匹配',
						},
					},
				},
			},
		},
	};
};
