import { request } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/operatorcmdb/host/';



export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}

export function GetListByIds(query: UserPageQuery) {
	console.error(query)
	return request({
		url: apiPrefix + 'get_list_by_ids/',
		method: 'get',
		params: query,
	});
}

export function GetObj(id: InfoReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

export function GetObjHumanInfo(id: InfoReq) {
	return request({
		url: apiPrefix + id + '/get_host_human_info/',
		method: 'get',
	});
}

export function GetMetrics() {
	return request({
		url: apiPrefix  + 'get_metrics/',
		method: 'get',
	});
}

export function AddObj(obj: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

export function UpdateObj(obj: EditReq) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export function DelObj(id: DelReq) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

export function DelObjs(obj: any) {
	return request({
		url: apiPrefix + 'multiple_delete/',
		method: 'delete',
		data: obj,
	});
}

// 远程执行job
export function RemoteExecJob(obj: any) {
	return request({
		url: 'http://opa.hzxingzai.cn/api/job/remote_execute/',
		method: 'post',
		data: obj,
		headers: {
			'token': import.meta.env.VITE_REMOTE_API_TOKEN,
		}
	});
}


