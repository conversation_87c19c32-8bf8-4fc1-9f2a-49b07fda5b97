<template>
	<fs-page>
		<fs-crud ref="crudRef" v-bind="crudBinding">
			<template #actionbar-left>
				<el-button
					type="primary"
					plain
					@click="dialogVisible = true"
					v-if="auth('scheduletask:template:addProject')"
				>添加项目</el-button>
			</template>
			<template #cell_params="scope">
				<template v-for="envObj in scope.row.params">
					<el-tag type="info" style="text-align: left;">{{ envObj.key }} -- {{ envObj.value }}</el-tag>
				</template>
			</template>
			<template #cell-rowHandle-left="scope">
				<el-button
					type="primary"
					link
					@click="editTemplate(scope.row)"
					v-if="auth('scheduletask:template:edit')"
				>编辑</el-button>
			</template>
		</fs-crud>
		<div>
			<!-- 文件上传 -->
			<el-dialog v-model="dialogVisible" title="上传项目文件" center>
				<el-upload
					class="upload-demo"
					drag
					v-model:file-list="fileList"
					:auto-upload="false"
					action
					multiple
				>
					<el-icon class="el-icon--upload">
						<upload-filled />
					</el-icon>
					<div class="el-upload__text">
						Drop file here or
						<em>click to upload</em>
					</div>
					<template #tip>
						<div class="el-upload__tip" style="color:red">
							<el-alert type="warning" :closable="false">
								<template #default>
									1. 仅支持上传.zip文件格式
									<br />2. 单个ansible项目至少包含roles目录、playbook.yml文件
								</template>
							</el-alert>
						</div>
					</template>
				</el-upload>
				<template #footer>
					<div class="dialog-footer-center">
						<el-button @click="dialogVisible = false">取消</el-button>
						<el-button type="primary" @click="handleConfirm">确认</el-button>
					</div>
				</template>
			</el-dialog>
		</div>
		<div>
			<!-- 自定义更新方法 -->
			<el-dialog v-model="editDialogVisible" title="编辑" center>
				<!-- 显示当前行数据 -->
				<el-form :model="editForm" ref="editFormRef" label-width="60px" :rules="rules">
					<el-form-item label="模板名称">
						<el-input v-model="editForm.name"></el-input>
					</el-form-item>
					<el-form-item label="项目路径">
						<el-input disabled v-model="editForm.project_path"></el-input>
					</el-form-item>
					<el-form-item label="描述">
						<el-input type="textarea" v-model="editForm.description"></el-input>
					</el-form-item>
					<el-form-item label="参数列表">
						<el-table border scrollbar-always-on :data="editForm.params" style="width: 100%" height="200">
							<el-table-column prop="key" label="Key">
								<template #default="scope">
									<el-form-item
										label
										:prop="('params.'+scope.$index+'.key')"
										requried
										:rules="rules.key"
										style="top:22px"
									>
										<el-input v-model="scope.row.key" placeholder="请输入变量名" />
									</el-form-item>
								</template>
							</el-table-column>
							<el-table-column prop="value" label="Value">
								<template #default="scope">
									<el-form-item label prop>
										<el-input v-model="scope.row.value" placeholder="请输入默认变量值" />
									</el-form-item>
								</template>
							</el-table-column>
							<el-table-column prop="desc" label="备注">
								<template #default="scope">
									<el-form-item label prop>
										<el-input v-model="scope.row.desc" placeholder="请输入备注" />
									</el-form-item>
								</template>
							</el-table-column>
							<el-table-column fixed="right" width="140px">
								<template #header>
									<el-button link type="primary" @click="addTableRow">添加</el-button>
								</template>
								<template #default="scope">
									<el-button link type="warning" @click="deleteTableRow(scope.$index)">删除</el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-form-item>
				</el-form>
				<template #footer>
					<div class="dialog-footer-center">
						<el-button @click="editDialogVisible = false">取消</el-button>
						<el-button type="primary" @click="submit">确认</el-button>
					</div>
				</template>
			</el-dialog>
		</div>
	</fs-page>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, reactive, toRefs } from 'vue';
import { useFs } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { ElMessage } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import * as api from '/@/api/scheduletask/template';
import { auth } from '/@/utils/authFunction';

const { crudBinding, crudRef, crudExpose } = useFs({ createCrudOptions });
const data = reactive({
	fileList: [],
	fileTypeList: ['zip', 'tar.gz'],
	editForm: {},
	rules: {
		key: [{ required: true, message: '请输入变量名', trigger: 'blur' }],
	},
});

const dialogVisible = ref(false);
const editDialogVisible = ref(false);
let editFormRef = ref(null);

// 添加行的方法
const addTableRow = () => {
	const newRow = {
		key: '',
		value: '',
		desc: '',
	};
	// 在列表后面添加数据
	data.editForm.params.unshift(newRow);
};

// 删除行的方法
const deleteTableRow = (index) => {
	data.editForm.params.splice(index, 1);
};

const editTemplate = (row) => {
	data.editForm = row; // 将当前行数据填充到编辑表单
	editDialogVisible.value = true;
};

const submit = () => {
	editFormRef.value.validate((valid) => {
		if (valid) {
			api.UpdateObj(data.editForm).then((response) => {
				if (response.code === 2000) {
					editDialogVisible.value = false;
					ElMessage.success(response.msg);
				} else {
					ElMessage.error(response.msg);
				}
			});
		} else {
			ElMessage.warning('请完善表单数据');
		}
	});
};

const handleConfirm = () => {
	if (data.fileList.length === 0) {
		ElMessage.warning('请上传文件');
		return;
	}
	for (var i = 0; i < data.fileList.length; i++) {
		const isValid = data.fileList[i].name.endsWith('.zip');
		if (!isValid) {
			ElMessage.error('该文件格式错误：' + data.fileList[i].name);
			return;
		}
	}
	api.AddObj(data.fileList.map((item) => item.raw)).then((response) => {
		if (response.code === 2000) {
			crudExpose.doRefresh();
			ElMessage.success(response.msg);
		} else {
			ElMessage.error(response.msg);
		}
	});
	console.log('上传的文件列表:', data.fileList);
	dialogVisible.value = false;
};

// 页面打开后获取列表数据
onMounted(() => {
	crudExpose.doRefresh();
});

onUnmounted(() => {});

const { fileList, editForm, rules } = toRefs(data);
</script>

<style scoped>
/* 居中底部按钮 */
.dialog-footer-center {
	display: flex;
	justify-content: center;
	gap: 10px;
}
.el-form .el-form-item:last-of-type {
	margin-bottom: 14px !important;
}
</style>