<template>
  <fs-page>
    <div class="page-header">
      <div class="header-controls">
        <div class="current-date-container">
          <span class="current-date">
            <img :src="idcMachineIcon" class="date-icon" />
            <span class="date-text">租户：</span>
            <el-select v-model="selectedAccount" @change="handleAccountChange" size="small"
  style="width: 120px; margin-right: 15px;" :disabled="reportId !== undefined">
  <el-option 
    v-for="item in tenantAccountOptions" 
    :key="item.id"  
    :label="item.company"  
    :value="item.id" 
  />
</el-select>
          </span>
        </div>

        <!-- 将按钮组放在最后并使用flex spacer推送到右侧 -->
        <div class="flex-spacer"></div>

        <div class="button-group">
          <el-button type="primary" size="small" @click="isShowChartDescription = !isShowChartDescription">
            <img :src="descriptionIcon" class="button-icon" />
            {{ isShowChartDescription ? "隐藏" : "显示" }}详细说明
          </el-button>
          <el-button size="small" type="primary" @click="openSaveDialog" :disabled="reportId !== undefined">
            <img :src="saveIcon" class="button-icon" />
            保存
          </el-button>
          <el-button type="primary" size="small" @click="exportPDF">
            <img :src="pdfIcon" class="button-icon" />
            导出PDF
          </el-button>
        </div>
      </div>
    </div>

    <Export2PDF ref="pdfExporter" :file-name="`资源统计_${selectedAccountName}_${currentDate}.pdf`" :scale="1.5"
      background-color="#F5F5F5">
      <div class="export-content">
        <!-- Header will be included in PDF -->
        <div class="pdf-header">
          <h2>资源统计报表</h2>
          <div class="pdf-subheader">
            <span class="current-date">
              <img :src="idcMachineIcon" class="date-icon" />
              <span class="date-text">名称：{{ selectedAccountName }}</span>
            </span>
            <span class="current-date">
              <img :src="dateIcon" class="date-icon" />
              <span class="date-text">日期：{{ currentDate }}</span>
            </span>
          </div>
        </div>

        <div class="resource-overview-container">
          <ChartDescription :data="descriptions" />
        </div>

        <div class="resource-overview-container">
          <ChartDescription :data="chartDataDescriptions" :config="{ name: '详细说明' }" v-if="isShowChartDescription" />
        </div>
        <div class="resource-overview-container">
          <UsageOverview :quota="customerResourceStaticalData.project_quota_set_and_usage.quota_set" :usage="customerResourceStaticalData.project_quota_set_and_usage.usage" />
        </div>
        <div class="resource-overview-container">
          <ResourceStatical :data="customerResourceStaticalData.server_statical" />
        </div>
      </div>
      <PortalServerIndex :selected-account-id="selectedAccount" v-if="selectedAccount" />
    </Export2PDF>

    <!-- 保存报表的对话框 -->
    <el-dialog v-model="dialogVisible" title="保存报表" width="600px" :close-on-click-modal="false"
      @closed="handleDialogClosed">
      <el-form ref="reportForm" :model="addReportData" :rules="addReportFormRules" label-width="120px">
        <el-form-item label="报表名称" prop="name">
          <el-input v-model="addReportData.name" placeholder="请输入报表名称" clearable />
        </el-form-item>

        <el-form-item label="报表分类" prop="category">
          <el-select v-model="addReportData.category" placeholder="请选择分类" style="width: 100%">
            <el-option v-for="item in categoryData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="isSaving" @click="handleSave">
          确认
        </el-button>
      </template>
    </el-dialog>
  </fs-page>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, Ref, reactive, } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { GetCustomerResoureStaticalCharts, GetObj, AddObj, } from '/@/api/report_datav/report';
import { GetListByIds } from '/@/api/tenant/account';
import { dictionary } from '/@/utils/dictionary';
import ResourceStatical from './comp/ResourceStatical.vue';
import UsageOverview from './comp/UsageOverview.vue';
import ChartDescription from '/@/components/chaosCharts/chartDescription.vue';
import PortalServerIndex from '/@/views/portalTenants/portal/portalServer/index.vue';
import Export2PDF from '/@/components/export2PDF/index.vue';
import dateIcon from '/@/assets/img/date.svg';
import idcMachineIcon from '/@/assets/img/idc-machine.svg';
import descriptionIcon from '/@/assets/img/description.svg';
import pdfIcon from '/@/assets/img/pdf.svg';
import saveIcon from '/@/assets/img/save.svg';


const route = useRoute();
const reportId: Ref<string> = ref(route.params.reportId as string);

const pdfExporter = ref();
const selectedAccount = ref('');
const selectedAccountName = ref('');

interface TenantAccountIdName {
  id: string;
  company: string;
}

const tenantAccountOptions = ref<TenantAccountIdName[]>([]);

// 监听 AccounntId ，确保初始化时显示正确的名称 
watch(tenantAccountOptions, (newOptions) => {
  if (newOptions.length  > 0) {
    // Only set default account if not viewing a report 
    if (!reportId.value)  {
      if (selectedAccount.value)  {
        const selected = newOptions.find(item  => item.id  === selectedAccount.value); 
        if (selected) {
          selectedAccountName.value  = selected.company; 
        }
      } else if (newOptions.length  > 0) {
        // Default to first account if no selection 
        selectedAccount.value  = newOptions[0].id;
        selectedAccountName.value  = newOptions[0].company;
      }
    }
  }
}, { immediate: true });

// Format current date as YYYY-MM-DD 
const currentDate = ref('');
// 修改后的日期格式化函数 
const formatDate = (dateString?: string) => {
  const date = dateString ? new Date(dateString) : new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  currentDate.value = `${year}-${month}-${day}`;
};

const exportPDF = () => {
  pdfExporter.value.exportAsPDF();
};

const descriptions = [
  '目前仅展示已对接到云平台(OpenStack)自动化管理的资源。',
  '主机 = 裸金属 + 云主机。',
];

const isShowChartDescription = ref(false);
const chartDataDescriptions = [
  '配额使用量概况：查询OpenStack中的配额及已使用的配额信息（三个核心指标：vCPU核数、实例数量、内存(GB)）。',
  '资产概况->主机：该账号下所有的主机数量（包含裸金属机器和云主机）。',
  '资产概况->裸金属：该账号下所有的裸金属数量。',
  '资产概况->云主机：该账号下所有的云主机数量。',
  '资产概况->运行中：该账号下所有运行中的主机数量。',
  '资产概况->GPU数量：该账号下所有主机的GPU数量。',
  '资产概况->即将过期：该账号下近15天内即将到期的所有主机数量。',
  '资产概况->已过期：该账号下近15天内已到期的所有主机数量。',
  '资产概况->近期创建：该账号下近7天内创建的所有主机数量。',
];


let customerResourceStaticalData = ref({
  project_quota_set_and_usage: {
    quota_set: {cores: 0, instances: 0, ram: 0,},
    usage: {cores: 0, instances: 0, ram: 0,},
  },
  server_statical: {
    total: 0, 
    virtual_host_total: 0,
    baremetal_total: 0,
    running: 0, 
    coming_expire: 0, 
    had_expired: 0, 
    coming_created: 0, 
    gpu_counts: 0, 
  },

})


// 报表数据
const addReportData = reactive({
  name: '资源统计',
  code: 'customer-resource-statical-charts',
  category: '租户',
  source_data: customerResourceStaticalData.value,
  path_name: 'customerReportResourceStaticalDetail',
  selected_data: {
    selected_account: '',
    selected_account_name: '',
  }
});
const categoryData = ref(dictionary('report_datav:report:category', undefined));
// 加载状态 
const isSaving = ref(false);
const reportForm = ref();
// 对话框控制 
const dialogVisible = ref(false);
// 表单验证规则 
const addReportFormRules = ref({
  name: [
    { required: true, message: '请输入报表名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到255个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择报表分类', trigger: 'change' }
  ],
})
// 对话框关闭时的处理
const handleDialogClosed = () => {
  // 正确调用方式：在表单引用上调用clearValidate
  if (reportForm.value) {
    reportForm.value.clearValidate()
  }
}

// 数据保存到报表内
const saveReport = async () => {
  // 更新源数据
  addReportData.source_data = customerResourceStaticalData.value;
  addReportData.selected_data.selected_account = selectedAccount.value;
  addReportData.selected_data.selected_account_name = selectedAccountName.value;
  const response = await AddObj(addReportData);
  if (response.code === 2000) {
    ElMessage.success(' 报表保存成功')

  } else {
    ElMessage.error(response.message || '保存失败')
  }
  isSaving.value = false;
  dialogVisible.value = false;
}
// 打开对话框 
const openSaveDialog = () => {
  // 确保每次打开对话框时都是新鲜的表单 
  dialogVisible.value = true;
  addReportData.name = addReportData.name + '_' + selectedAccountName.value + '_' + currentDate.value;
}
// 处理保存 
const handleSave = () => {
  reportForm.value.validate((valid: any) => {
    if (valid) {
      saveReport();
    } else {
      ElMessage.warning(' 请填写完整的报表信息!!!')
    }
  })
}

const getData = async (accountId = '') => {
  try {
    const { data } = await GetCustomerResoureStaticalCharts({ 'account_id': accountId });
    customerResourceStaticalData.value = data;
  } catch (error) {
    ElMessage.error('数据加载失败');
  }
}


const getReportData = async (reportId: string) => {
  try {
    const { data } = await GetObj(reportId);
    customerResourceStaticalData.value  = data.source_data; 
    
    if (data?.selected_data) {
      const { selected_account, selected_account_name } = data.selected_data; 
      
      // Set the account from report data 
      selectedAccount.value  = selected_account;
      selectedAccountName.value  = selected_account_name;
      
      // Find if the account exists in tenant options 
      const accountExists = tenantAccountOptions.value.some( 
        item => item.id  === selected_account 
      );
      
      if (!accountExists) {
        // If account doesn't exist in options, add it 
        tenantAccountOptions.value.push({ 
          id: selected_account,
          company: selected_account_name 
        });
      }
    }
 
    // Set date from report or current time 
    formatDate(data?.create_datetime);
    
    // Fetch data for the selected account 
    if (selectedAccount.value)  {
      await getData(selectedAccount.value); 
    }
  } catch (error) {
    ElMessage.error(' 数据加载失败');
  }
};

const handleAccountChange = (value: string) => {
  const selected = tenantAccountOptions.value.find(item  => item.id  === value);
  if (selected) {
    selectedAccountName.value  = selected.company; 
    getData(value);
  }
};

const getTenantAccountIdNameData = async () => {
  try {
    const { data } = await GetListByIds({ is_all: true });
    tenantAccountOptions.value  = data;
    
    // 默认选择第一个账号 
    if (!reportId.value) {
      if (data.length > 0) {
        selectedAccount.value  = data[0].id;
        selectedAccountName.value  = data[0].company;
      }
      
    }
  } catch (error) {
    ElMessage.error(' 租户账号数据加载失败');
  }
};


// 监听 reportId 的变化 
watch(reportId, (newReportId) => {
  if (newReportId) {
    getReportData(newReportId);
  } else {
    // Reset to default when not viewing a report 
    if (tenantAccountOptions.value.length  > 0) {
      selectedAccount.value  = tenantAccountOptions.value[0].id; 
      selectedAccountName.value  = tenantAccountOptions.value[0].company; 
    }
    getData(selectedAccount.value); 
    formatDate();
  }
}, { immediate: true });


onMounted(() => {
  // 初始化时根据是否有reportId决定如何设置日期 
  if (reportId.value !== undefined) {
    formatDate();
  }
  getTenantAccountIdNameData();
})

</script>

<style scoped>
.page-header {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  margin: 10px;
  padding: 12px 15px;
}

.header-controls {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  /* Prevent wrapping */
}

.account-name {
  margin-left: 5px;
  color: #666;
  font-size: 14px;
}

.flex-spacer {
  flex-grow: 1;
}

.button-group {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.button-group .el-button {
  margin-left: 10px;
  display: flex;
  align-items: center;
}

.button-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}

.current-date-container {
  display: flex;
  align-items: center;
  margin-right: 15px;
  min-width: 160px;
  /* Ensure enough space for date */
}

.current-date {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.date-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  flex-shrink: 0;
  /* Prevent icon from shrinking */
}

.date-text {
  white-space: nowrap;
}

.export-btn {
  margin-left: auto;
  flex-shrink: 0;
  /* Prevent button from shrinking */
}

.pdf-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.pdf-subheader {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.resource-overview-container {
  display: flex;
  gap: 16px;
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-controls {
    flex-wrap: wrap;
  }

  .current-date-container {
    order: 1;
    width: 100%;
    margin: 10px 0;
  }

  .export-btn {
    margin-left: 0;
    width: 100%;
  }
}
</style>