import * as api from './api';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict,
} from '@fast-crud/fast-crud';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('resource:physicalAsset:generalConsumable:Create'),
            plain: true,
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 280,
        buttons: {
          view: {
            type: 'primary',
            link: true,
            /* eslint-disable-next-line no-dupe-keys */
            show: auth('resource:physicalAsset:generalConsumable:Retrieve')
          },
          edit: {
            type: 'primary',
            link: true,
            show: auth('resource:physicalAsset:generalConsumable:Update')
          },
          remove: {
            link: true,
            type: 'danger',
            show: auth('resource:physicalAsset:generalConsumable:Delete')
          },
          viewLog: {
            type: 'primary',
            text: '查看日志',
            link: true,
            show: auth('system:auditLog:GetResourceLogs'),
            click(context) {
              router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
            }
          },
        },
      },
      pagination: {
        show: true
      },
      table: {
        rowKey: 'id',
      },
      form: {
        labelWidth: 100,
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        machine_type: {
          title: '设备类型',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('physical_asset:general_consumable:machine_type', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '设备类型为必填项'},
            ],
            component: {
              placeholder: '请输入设备类型',
            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
            ],
            component: {
              placeholder: '请输入描述',
            }
          }
        },
        machine_room: {
          title: '机房',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/machine_room/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            // cache: true,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择机房',
            }
          },
        },
        private_room: {
          title: '包间',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/private_room/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            // cache: true,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择包间',
            }
          },
        },
        manufacturer: {
          title: '厂商',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '厂商为必填项'},
              {min: 1, max: 255, message: '最小: 1, 最大: 255', trigger: 'blur'}
            ],
            component: {
              placeholder: '请输入厂商',
            }
          }
        },
        belong: {
          title: '资产归属',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('physical_asset:belong', undefined)
          }),
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '资产归属为必填项'},
            ],
            component: {
              placeholder: '请选择资产归属',
            }
          }
        },
        specs: {
          title: '规格',
          search: {
            show: false,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '规格为必填项'},
            ],
            component: {
              placeholder: '请输入规格',
            }
          }
        },
        count: {
          title: '数量',
          search: {
            show: false,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '数量为必填项'},
            ],
            component: {
              placeholder: '请输入数量',
              step: 1
            }
          }
        },
      },
    },
  };
};
