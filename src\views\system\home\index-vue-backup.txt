<template>
	<div class="home-container">
    <div>开发中......</div>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div>
          <el-statistic
            group-separator=","
            :precision="2"
            value="100"
            title="当前用户"
          ></el-statistic>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- <div style="width:800px;height:800px" ref="geoChinaRef"></div> -->
    <!-- <div>
				<div style="height: 100%;width: 100%;" ref="geoChinaRef"></div>
    </div> -->
		<!-- <el-row :gutter="15" class="home-card-three">
			<el-col :xs="24" :sm="14" :md="14" :lg="16" :xl="16" class="home-media">
				<div class="home-card-item">
          <div style="width:100%;height:100%" ref="geoChinaRef"></div>
					<div style="height: 100%" ref="homeBarRef"></div>
				</div>
			</el-col>
		</el-row> -->
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, defineComponent, onMounted, ref, watch, nextTick, onActivated } from 'vue';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import chinaAreaData from '/@/utils/geoAtlasJson/chinaArea.json';


let global: any = {
	homeChartOne: null,
	homeChartTwo: null,
	homeCharThree: null,
	dispose: [null, '', undefined],
  homeChartChinaGeo: null,
};

export default defineComponent({
	name: 'home',
	setup() {
		const homeLineRef = ref();
		const homePieRef = ref();
		const homeBarRef = ref();
    const geoChinaRef = ref();
		const storesTagsViewRoutes = useTagsViewRoutes();
		const storesThemeConfig = useThemeConfig();
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);
		const state = reactive({
			homeThree: [
				{
					icon: 'iconfont icon-yangan',
					label: '浅粉红',
					value: '2.1%OBS/M',
					iconColor: '#F72B3F',
				},
			],
			myCharts: [],
			charts: {
				theme: '',
				bgColor: '',
				color: '#303133',
			},
		});

    // 地图
		const initChinaGeoChart = () => {
			if (!global.dispose.some((b: any) => b === global.homeChartChinaGeo)) global.homeChartChinaGeo.dispose();
			global.homeChartChinaGeo = <any>echarts.init(geoChinaRef.value, state.charts.theme);
      echarts.registerMap('china', chinaAreaData);
			// 配置地图选项
      const option = {
        tooltip: {},
        visualMap: {
          min: 500000,
          max: 38000000,
          // min: 0,
          // max: 2500,
          calculable: true,
          text: ['High', 'Low'], // 文本，默认为数值文本
          seriesIndex: [0, 1], // 控制多个 series
        },
        geo: {
          map: 'china',
          roam: true,
          label: {
            emphasis: {
              show: false,
            },
          },
          itemStyle: {
            normal: {
              areaColor: '#323c48',
              borderColor: '#111',
            },
            emphasis: {
              areaColor: '#2a333d',
            },
          },
        },
        series: [
          {
            type: 'map',
            geoIndex: 0,
            map: 'china',
            data: [
              { name: '北京市', value: 120000000 },
              { name: '天津市', value: 11000000 },
              // ... 其他数据
            ],
          },
        ],
      };
			(<any>global.homeChartChinaGeo).setOption(option);
			(<any>state.myCharts).push(global.homeChartChinaGeo);
		};
		
		// 批量设置 echarts resize
		const initEchartsResizeFun = () => {
			nextTick(() => {
				for (let i = 0; i < state.myCharts.length; i++) {
					setTimeout(() => {
						(<any>state.myCharts[i]).resize();
					}, i * 1000);
				}
			});
		};
		// 批量设置 echarts resize
		const initEchartsResize = () => {
			window.addEventListener('resize', initEchartsResizeFun);
		};
		// 页面加载时
		onMounted(() => {
			initEchartsResize();
		});
		// 由于页面缓存原因，keep-alive
		onActivated(() => {
			initEchartsResizeFun();
		});
		// 监听 vuex 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
		watch(
			() => isTagsViewCurrenFull.value,
			() => {
				initEchartsResizeFun();
			}
		);
		// 监听 vuex 中是否开启深色主题
		watch(
			() => themeConfig.value.isIsDark,
			(isIsDark) => {
				nextTick(() => {
					state.charts.theme = isIsDark ? 'dark' : '';
					state.charts.bgColor = isIsDark ? 'transparent' : '';
					state.charts.color = isIsDark ? '#dadada' : '#303133';
          
          setTimeout(() => {
						initChinaGeoChart();
					}, 1000);

				});
			},
			{
				deep: true,
				immediate: true,
			}
		);
		return {
			homeLineRef,
			homePieRef,
			homeBarRef,
      geoChinaRef,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
$homeNavLengh: 8;
.home-container {
	overflow: hidden;
  
}
</style>


######## 2025-02-14 backup index.vue Start
<template>
	<div class="home-container">
    <div style="margin: 20px; font-size: 60px;">数据概览</div>
    <div style="margin: 20px;">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div>
          <el-statistic
            group-separator=","
            :precision="0"
            value="2"
            title="机房总数"
          ></el-statistic>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div>
          <el-statistic
            group-separator=","
            :precision="0"
            value="2"
            title="包间总数"
          ></el-statistic>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
    <!-- <div style="width:800px;height:800px" ref="geoChinaRef"></div> -->
    <!-- <div>
				<div style="height: 100%;width: 100%;" ref="geoChinaRef"></div>
    </div> -->
		<!-- <el-row :gutter="15" class="home-card-three">
			<el-col :xs="24" :sm="14" :md="14" :lg="16" :xl="16" class="home-media">
				<div class="home-card-item">
          <div style="width:100%;height:100%" ref="geoChinaRef"></div>
					<div style="height: 100%" ref="homeBarRef"></div>
				</div>
			</el-col>
		</el-row> -->
	</div>
</template>

<script lang="ts">
import { toRefs, reactive, defineComponent, onMounted, ref, watch, nextTick, onActivated } from 'vue';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import chinaAreaData from '/@/utils/geoAtlasJson/chinaArea.json';


let global: any = {
	homeChartOne: null,
	homeChartTwo: null,
	homeCharThree: null,
	dispose: [null, '', undefined],
  homeChartChinaGeo: null,
};

export default defineComponent({
	name: 'home',
	setup() {
		const homeLineRef = ref();
		const homePieRef = ref();
		const homeBarRef = ref();
    const geoChinaRef = ref();
		const storesTagsViewRoutes = useTagsViewRoutes();
		const storesThemeConfig = useThemeConfig();
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);
		const state = reactive({
			homeThree: [
				{
					icon: 'iconfont icon-yangan',
					label: '浅粉红',
					value: '2.1%OBS/M',
					iconColor: '#F72B3F',
				},
			],
			myCharts: [],
			charts: {
				theme: '',
				bgColor: '',
				color: '#303133',
			},
		});

    // 地图
		const initChinaGeoChart = () => {
			if (!global.dispose.some((b: any) => b === global.homeChartChinaGeo)) global.homeChartChinaGeo.dispose();
			global.homeChartChinaGeo = <any>echarts.init(geoChinaRef.value, state.charts.theme);
      echarts.registerMap('china', chinaAreaData);
			// 配置地图选项
      const option = {
        tooltip: {},
        visualMap: {
          min: 500000,
          max: 38000000,
          // min: 0,
          // max: 2500,
          calculable: true,
          text: ['High', 'Low'], // 文本，默认为数值文本
          seriesIndex: [0, 1], // 控制多个 series
        },
        geo: {
          map: 'china',
          roam: true,
          label: {
            emphasis: {
              show: false,
            },
          },
          itemStyle: {
            normal: {
              areaColor: '#323c48',
              borderColor: '#111',
            },
            emphasis: {
              areaColor: '#2a333d',
            },
          },
        },
        series: [
          {
            type: 'map',
            geoIndex: 0,
            map: 'china',
            data: [
              { name: '北京市', value: 120000000 },
              { name: '天津市', value: 11000000 },
              // ... 其他数据
            ],
          },
        ],
      };
			(<any>global.homeChartChinaGeo).setOption(option);
			(<any>state.myCharts).push(global.homeChartChinaGeo);
		};
		
		// 批量设置 echarts resize
		const initEchartsResizeFun = () => {
			nextTick(() => {
				for (let i = 0; i < state.myCharts.length; i++) {
					setTimeout(() => {
						(<any>state.myCharts[i]).resize();
					}, i * 1000);
				}
			});
		};
		// 批量设置 echarts resize
		const initEchartsResize = () => {
			window.addEventListener('resize', initEchartsResizeFun);
		};
		// 页面加载时
		onMounted(() => {
			initEchartsResize();
		});
		// 由于页面缓存原因，keep-alive
		onActivated(() => {
			initEchartsResizeFun();
		});
		// 监听 vuex 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
		watch(
			() => isTagsViewCurrenFull.value,
			() => {
				initEchartsResizeFun();
			}
		);
		// 监听 vuex 中是否开启深色主题
		watch(
			() => themeConfig.value.isIsDark,
			(isIsDark) => {
				nextTick(() => {
					state.charts.theme = isIsDark ? 'dark' : '';
					state.charts.bgColor = isIsDark ? 'transparent' : '';
					state.charts.color = isIsDark ? '#dadada' : '#303133';
          
          setTimeout(() => {
						initChinaGeoChart();
					}, 1000);

				});
			},
			{
				deep: true,
				immediate: true,
			}
		);
		return {
			homeLineRef,
			homePieRef,
			homeBarRef,
      geoChinaRef,
			...toRefs(state),
		};
	},
});
</script>

<style scoped lang="scss">
$homeNavLengh: 8;
.home-container {
	overflow: hidden;
}
</style>
######## 2025-02-14 backup index.vue End