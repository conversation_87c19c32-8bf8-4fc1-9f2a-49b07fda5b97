import { request } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/tenants/tenant-hisec-natserver-policy/';
export function GetList(query: UserPageQuery) {
  return request({
    url: apiPrefix,
    method: 'get',
    params: query,
  });
}
export function GetObj(id: InfoReq) {
  return request({
    url: apiPrefix + id,
    method: 'get',
  });
}

export function AddObj(obj: AddReq) {
  return request({
    url: apiPrefix,
    method: 'post',
    data: obj,
  });
}

export function UpdateObj(obj: EditReq) {
  return request({
    url: apiPrefix + obj.id + '/',
    method: 'put',
    data: obj,
  });
}

export function DelObj(id: DelReq) {
  return request({
    url: apiPrefix + id + '/',
    method: 'delete',
    data: { id },
  });
}


export function DelObjs(obj: any) {
  return request({
    url: apiPrefix + 'multiple_delete/',
    method: 'delete',
    data: obj,
  });
}


export function createHisecNatServerPolicy(obj: any) {
  return request({
    url: apiPrefix + 'create_hisec_nat_server_policy/',
    method: 'post',
    data: obj,
  });
}


export function getPublicIPChildrenList(query: UserPageQuery) {
  return request({
    url: apiPrefix + 'public_ip_children_list/',
    method: 'get',
    params: query,
  });
}
