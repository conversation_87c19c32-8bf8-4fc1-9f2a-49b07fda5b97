import * as api from '/@/api/ticket/task';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict
} from '@fast-crud/fast-crud';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  return {
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('resource:machineRoom:Create'),
            plain: true,
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 280,
        buttons: {
          view: {
            type: 'primary',
            link: true,
            show: auth('resource:machineRoom:Retrieve')
          },
          edit: {
            type: 'primary',
            link: true,
            show: auth('resource:machineRoom:Update')
          },
          remove: {
            type: 'danger',
            link: true,
            show: auth('resource:machineRoom:Delete')
          },
          viewLog: {
            type: 'primary',
            text: '查看日志',
            link: true,
            show: auth('system:auditLog:GetResourceLogs'),
            click(context) {
              router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
            }
          },
        },
      },
      pagination: {
        show: true,
        'default-page-size': 10,
        'default-current': 1,
      },
      table: {
        rowKey: 'id',
      },
      form: {
        labelWidth: 100,
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        name: {
          title: '任务名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '名称必填项' },
              { max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' }
            ],
            component: {
              placeholder: '请输入任务名称',
            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'textarea',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: false, message: '任务描述必填项'},
            ],
            component: {
              placeholder: '请输入任务描述',
            }
          }
        },
        step_index: {
          title: '步骤编号',
          search: {
            show: true,
          },
          type: 'number',
          column: {
            minWidth: 90,
          },
          form: {
            show: false,
            rules: [
              // 表单校验规则
              {required: false, message: '当前步骤必填项'},
            ],
            component: {
              placeholder: '请输入当前步骤',
            }
          }
        },
        form_data: {
          title: '表单数据',
          search: {
            show: false,
          },
          type: 'textarea',
          column: {
            minWidth: 90,
          },
          form: {
            show: true,
            rules: [
              // 表单校验规则
              {required: false, message: '表单数据必填项'},
            ],
            component: {
              placeholder: '请输入表单数据',
            }
          }
        },
        async_worker: {
          title: '异步任务',
          search: {
            show: false,
          },
          type: 'textarea',
          column: {
            minWidth: 90,
          },
          form: {
            show: true,
            rules: [
              // 表单校验规则
              {required: false, message: '异步任务必填项'},
            ],
            component: {
              placeholder: '请输入异步任务',
            }
          }
        },
        async_worker_result: {
          title: '异步任务结果',
          search: {
            show: false,
          },
          type: 'textarea',
          column: {
            minWidth: 90,
          },
          form: {
            show: false,
            rules: [
              // 表单校验规则
              {required: false, message: '异步任务结果必填项'},
            ],
            component: {
              placeholder: '请输入异步任务结果',
            }
          }
        },
        status: {
          title: '状态',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            show: true,
            rules: [
              // 表单校验规则
              {required: false, message: '状态必填项'},
            ],
            component: {
              placeholder: '请输入状态',
            }
          }
        },
        comment: {
          title: '备注',
          search: {
            show: false,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            show: true,
            rules: [
              // 表单校验规则
              {required: false, message: '备注必填项'},
            ],
            component: {
              placeholder: '请输入备注',
            }
          }
        },
        ticket: {
          title: '工单',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/ticket/ticket/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            // cache: true,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                {required: true, message: '工单必填项'},
              ],
              placeholder: '请选择工单',
            }
          },
        },
      },
    },
  };
};
