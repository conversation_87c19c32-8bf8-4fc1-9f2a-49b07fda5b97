<template>
  <div class="network-topology">
    <div class="topology-header">
      <h3>网络拓扑图</h3>
      <div class="topology-controls">
        <div class="legend">
          <span class="legend-item">
            <span class="legend-color switch"></span>
            交换机
          </span>
          <span class="legend-item">
            <span class="legend-color physical-port"></span>
            物理端口
          </span>
          <span class="legend-item">
            <span class="legend-color trunk-port"></span>
            聚合端口
          </span>
        </div>
        <div class="status-legend">
          <span class="status-item">
            <span class="status-color active"></span>
            活跃
          </span>
          <span class="status-item">
            <span class="status-color inactive"></span>
            非活跃
          </span>
        </div>
      </div>
    </div>
    <div id="container" class="topology-container"></div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { Badge, BaseBehavior, ExtensionCategory, Graph, GraphEvent, Label, Rect, register } from '@antv/g6';

import {exampleData} from './networkExample';


onMounted(() => {
const statusColors = {
  active: '#17BEBB',    // 活跃状态 - 绿色
  inactive: '#B7AD99',  // 非活跃状态 - 灰色
  online: '#17BEBB',    // 兼容原有数据
  busy: '#E36397',      // 兼容原有数据
  offline: '#B7AD99',   // 兼容原有数据
};

const nodeTypeColors = {
  switch: '#2078B4',           // 交换机 - 蓝色
  'physical-port': '#52C41A',  // 物理端口 - 绿色
  'trunk-port': '#FA8C16',     // 聚合端口 - 橙色
};

const DEFAULT_LEVEL = 'detailed';

/**
 * Draw a chart node with different ui based on the zoom level.
 */
class ChartNode extends Rect {
  get data() {
    return this.context.model.getElementDataById(this.id).data;
  }

  get level() {
    return this.data.level || DEFAULT_LEVEL;
  }

  getLabelStyle() {
    const text = this.data.text || this.data.name || 'Unknown';
    const labelStyle =
      this.level === 'overview'
        ? {
            fill: '#fff',
            fontSize: 20,
            fontWeight: 600,
            textAlign: 'center',
            transform: [['translate', 0, 0]],
          }
        : {
            fill: nodeTypeColors[this.data.type] || '#2078B4',
            fontSize: 14,
            fontWeight: 400,
            textAlign: 'left',
            transform: [['translate', -65, -15]],
          };
    return { text, ...labelStyle };
  }

  getKeyStyle(attributes) {
    return {
      ...super.getKeyStyle(attributes),
      fill: this.level === 'overview' ? statusColors[this.data.status] : '#fff',
    };
  }

  getPositionStyle(attributes) {
    if (this.level === 'overview') return false;

    // 根据节点类型显示不同的信息
    let text = '';
    if (this.data.type === 'switch') {
      text = `${this.data.vendor || 'Unknown'} | IP: ${this.data.ip || 'N/A'}`;
    } else if (this.data.type === 'physical-port') {
      text = `Speed: ${this.data.speed || 'Unknown'} | State: ${this.data.state || 'Unknown'}`;
    } else if (this.data.type === 'trunk-port') {
      text = `Members: ${this.data.member_count || 0} | State: ${this.data.state || 'Unknown'}`;
    } else {
      text = this.data.position || this.data.type || 'Unknown';
    }

    return {
      text,
      fontSize: 8,
      fontWeight: 400,
      textTransform: 'uppercase',
      fill: '#343f4a',
      textAlign: 'left',
      transform: [['translate', -65, 0]],
    };
  }

  drawPositionShape(attributes, container) {
    const positionStyle = this.getPositionStyle(attributes);
    this.upsert('position', Label, positionStyle, container);
  }

  getStatusStyle(attributes) {
    if (this.level === 'overview') return false;
    return {
      text: this.data.status,
      fontSize: 8,
      textAlign: 'left',
      transform: [['translate', 40, -16]],
      padding: [0, 4],
      fill: '#fff',
      backgroundFill: statusColors[this.data.status],
    };
  }

  drawStatusShape(attributes, container) {
    const statusStyle = this.getStatusStyle(attributes);
    this.upsert('status', Badge, statusStyle, container);
  }

  getPhoneStyle(attributes) {
    if (this.level === 'overview') return false;

    // 显示详细信息
    let text = '';
    if (this.data.type === 'switch') {
      text = `SN: ${this.data.details?.sn || 'N/A'}`;
    } else if (this.data.type === 'physical-port') {
      text = `MAC: ${this.data.details?.mac_address || 'N/A'}`;
    } else if (this.data.type === 'trunk-port') {
      const members = this.data.details?.members || [];
      text = `Members: ${members.slice(0, 2).join(', ')}${members.length > 2 ? '...' : ''}`;
    } else {
      text = this.data.phone || '';
    }

    return {
      text,
      fontSize: 8,
      fontWeight: 300,
      textAlign: 'left',
      transform: [['translate', -65, 20]],
    };
  }

  drawPhoneShape(attributes, container) {
    const style = this.getPhoneStyle(attributes);
    this.upsert('phone', Label, style, container);
  }

  render(attributes = this.parsedAttributes, container = this) {
    super.render(attributes, container);

    this.drawPositionShape(attributes, container);

    this.drawStatusShape(attributes, container);

    this.drawPhoneShape(attributes, container);
  }
}

/**
 * Implement a level of detail rendering, which will show different details based on the zoom level.
 */
class LevelOfDetail extends BaseBehavior {
  prevLevel = DEFAULT_LEVEL;
  levels = {
    ['overview']: [0, 0.6],
    ['detailed']: [0.6, Infinity],
  };

  constructor(context, options) {
    super(context, options);
    this.bindEvents();
  }

  update(options) {
    this.unbindEvents();
    super.update(options);
    this.bindEvents();
  }

  updateZoomLevel = async (e) => {
    if ('scale' in e.data) {
      const scale = e.data.scale;
      const level = Object.entries(this.levels).find(([key, [min, max]]) => scale > min && scale <= max)?.[0];
      if (level && this.prevLevel !== level) {
        const { graph } = this.context;
        graph.updateNodeData((prev) => prev.map((node) => ({ ...node, data: { ...node.data, level } })));
        await graph.draw();
        this.prevLevel = level;
      }
    }
  };

  bindEvents() {
    const { graph } = this.context;
    graph.on(GraphEvent.AFTER_TRANSFORM, this.updateZoomLevel);
  }

  unbindEvents() {
    const { graph } = this.context;
    graph.off(GraphEvent.AFTER_TRANSFORM, this.updateZoomLevel);
  }

  destroy() {
    this.unbindEvents();
    super.destroy();
  }
}

register(ExtensionCategory.NODE, 'chart-node', ChartNode);
register(ExtensionCategory.BEHAVIOR, 'level-of-detail', LevelOfDetail);

// 处理数据格式，适配你的API数据结构
const processData = (apiData) => {
  return {
    nodes: apiData.data.nodes,
    edges: apiData.data.edges
  };
};

const data = processData(exampleData);

    const graph = new Graph({
      container: 'container',
      data,
      node: {
        type: 'chart-node',
        style: (data) => {
          const nodeType = data.data?.type || 'unknown';
          const status = data.data?.status || 'inactive';

          // 根据节点类型设置不同的样式
          let size, fill;
          switch (nodeType) {
            case 'switch':
              size = [180, 80];
              fill = statusColors[status] || '#2078B4';
              break;
            case 'physical-port':
              size = [140, 50];
              fill = statusColors[status] || '#52C41A';
              break;
            case 'trunk-port':
              size = [160, 60];
              fill = statusColors[status] || '#FA8C16';
              break;
            default:
              size = [150, 60];
              fill = statusColors[status] || '#B7AD99';
          }

          return {
            labelPlacement: 'center',
            lineWidth: 1,
            ports: [{ placement: 'top' }, { placement: 'bottom' }],
            radius: 4,
            shadowBlur: 10,
            shadowColor: '#e0e0e0',
            shadowOffsetX: 3,
            size,
            stroke: '#C0C0C0',
            fill: fill,
          };
        },
      },
      edge: {
        type: 'polyline',
        style: (data) => {
          const edgeStatus = data.data?.status || 'inactive';

          // 根据连接状态设置不同的样式
          let stroke, lineWidth, lineDash;
          switch (edgeStatus) {
            case 'active':
              stroke = '#52C41A';
              lineWidth = 2;
              lineDash = [];
              break;
            case 'inactive':
              stroke = '#B7AD99';
              lineWidth = 1;
              lineDash = [5, 5];
              break;
            default:
              stroke = '#C0C0C0';
              lineWidth = 1;
              lineDash = [];
          }

          return {
            router: {
              type: 'orth',
            },
            stroke,
            lineWidth,
            lineDash,
            endArrow: {
              type: 'triangle',
              size: 6,
              fill: stroke,
            },
          };
        },
      },
      layout: {
        type: 'dagre',
        rankdir: 'TB', // 从上到下布局
        align: 'UL',   // 左上对齐
        nodesep: 50,   // 节点间距
        ranksep: 80,   // 层级间距
      },
      autoFit: 'view',
      behaviors: [
        'level-of-detail',
        'zoom-canvas',
        'drag-canvas',
        'drag-node',
        'click-select',
        'hover-activate'
      ],
    });

    // 添加事件监听器
    graph.on('node:click', (e) => {
      const nodeData = e.target.data;
      console.log('节点点击:', nodeData);

      // 可以在这里添加节点点击的处理逻辑
      // 比如显示详细信息弹窗等
    });

    graph.on('node:mouseenter', (e) => {
      const node = e.target;
      graph.updateNodeData([{
        id: node.id,
        style: {
          ...node.style,
          shadowBlur: 20,
          shadowColor: '#1890ff',
        }
      }]);
    });

    graph.on('node:mouseleave', (e) => {
      const node = e.target;
      graph.updateNodeData([{
        id: node.id,
        style: {
          ...node.style,
          shadowBlur: 10,
          shadowColor: '#e0e0e0',
        }
      }]);
    });

    graph.render();
  
});
</script>

<style scoped>
.network-topology {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.topology-header {
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.topology-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.topology-controls {
  display: flex;
  gap: 32px;
  align-items: center;
}

.legend {
  display: flex;
  gap: 16px;
  align-items: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  display: inline-block;
}

.legend-color.switch {
  background-color: #2078B4;
}

.legend-color.physical-port {
  background-color: #52C41A;
}

.legend-color.trunk-port {
  background-color: #FA8C16;
}

.status-legend {
  display: flex;
  gap: 16px;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.status-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.status-color.active {
  background-color: #17BEBB;
}

.status-color.inactive {
  background-color: #B7AD99;
}

.topology-container {
  flex: 1;
  width: 100%;
  background: white;
  border-radius: 8px;
  margin: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

#container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
</style>
