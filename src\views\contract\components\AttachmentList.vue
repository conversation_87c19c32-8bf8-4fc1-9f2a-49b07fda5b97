<template>
	<div class="attachment-container">
		<span
			v-for="(item, index) in props.resultList"
			:key="index"
			class="attachment-tag"
			style="margin-left:10px"
		>
			<el-tag size="large" style class="tag-with-dropdown" type="info" effect="plain">
				<span class="tag-text">{{ item.name }}</span>
				<el-dropdown trigger="click" placement="bottom-end" style="margin-left:15px">
					<el-icon class="dropdown-icon" size="large">
						<MoreFilled />
					</el-icon>
					<template #dropdown>
						<el-dropdown-menu>
							<!-- 条件渲染：如果不是压缩文件，显示查看 -->
							<el-dropdown-item v-if="!isArchive(item.name)" @click="filePreview(item)">查看</el-dropdown-item>
							<el-dropdown-item @click="download(item)">下载</el-dropdown-item>
							<el-dropdown-item divided @click="emitDelete(item.id)" :disabled="props.method === 'Detail'">
								<span style="color: red">删除</span>
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</el-tag>
		</span>

		<OnlyOffice v-model="DialogVisible" :documentDict="documentDict"></OnlyOffice>

		<!-- 图片预览 -->
		<el-drawer v-model="ImageDialogVisible" :show-close="true" size="60%">
			<template #header="{ close, titleId, titleClass }">
				<h4 :id="titleId" :class="titleClass">图片预览</h4>
			</template>
			<el-image
				style="width: 100%"
				:src="editFileUrl"
				fit="contain"
				:preview-src-list="[editFileUrl]"
				:initial-index="0"
			>
				<template #placeholder>
					<div style="text-align: center; padding: 30px;">加载中...</div>
				</template>
				<template #error>
					<div style="text-align: center; padding: 30px;">加载失败</div>
				</template>
			</el-image>
		</el-drawer>
	</div>
</template>

<script setup>
import { defineProps, defineEmits,ref,reactive,toRefs } from 'vue'
import * as api from '/@/api/contract/accessory';
import OnlyOffice from '/@/components/onlyoffice/OnlyOffice.vue';
import { ElMessage } from 'element-plus'

// 接收 props 和 emits
const props = defineProps({
  resultList: {
    type: Array,
    required: true
  },
  method: {
    type: String,
    default: "Detail"
  }
})

const data = reactive({
  documentDict:{url:""}
})

const editFileUrl = ref("")
const editFileExt = ref("")
const DialogVisible = ref(false)
const ImageDialogVisible = ref(false); // 控制图片预览弹窗
const emit = defineEmits(['delete'])

// 判断是否为压缩文件
const isArchive = (filename) => {
  const archiveExtensions = ['zip', 'tar', 'gz', 'rar', '7z']
  const ext = filename.split('.').pop()?.toLowerCase()
  return ext && archiveExtensions.includes(ext);
}

// 判断是不是图片
const isImageFile = (filename) => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  const ext = filename.split('.').pop()?.toLowerCase();
  return ext && imageExtensions.includes(ext);
};

// 判断是不是文档
const isDocumentFile = (filename) => {
  const documentExtensions = ['docx', 'xlsx', 'pptx', 'pdf','doc','txt','html','xls','csv','ppt'];
  const ext = filename.split('.').pop()?.toLowerCase();
  return ext && documentExtensions.includes(ext);
};

const getLink = async (id) => {
  await api.GetLink(id).then((response)=>{
      if (response.code === 2000) {
        editFileUrl.value = response.data
      } else {
        ElMessage.error(response.msg);
      }
    })
}

// 触发事件
const download = async (row) => {
  try {
    await getLink(row.id)
    // 或者通过 <a> 标签触发下载
    const link = document.createElement('a')
    link.href = editFileUrl.value
    link.download = row.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (err) {
    ElMessage.error('下载失败:',err)
  }
}

const filePreview = async (row) => {
    await getLink(row.id);
    editFileExt.value = row.name.split('.').pop()?.toLowerCase(); 
    if (isImageFile(row.name)) {
      ImageDialogVisible.value = true;
    } else if (isDocumentFile(row.name)) {
      data.documentDict.url = editFileUrl.value
      data.documentDict.fileType = editFileExt.value
      data.documentDict.title = row.name
      data.documentDict.key = row.id
      DialogVisible.value = true
    } else {
      ElMessage.warning('暂不支持预览该类型文件');
    }
}
const emitDelete = (id) => emit('delete', id)

const { documentDict } = toRefs(data);
</script>

<style scoped>
.el-tag--large {
	height: 40px;
	padding: 0 11px;
	--el-icon-size: 16px;
}
</style>
