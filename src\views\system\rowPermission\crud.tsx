import * as api from './api';
// import * as OPlog from '/@/views/system/log/operationLog/crud'
import {createCrudOptions as operationLogCreateCrudOptions} from '/@/views/system/log/operationLog/crud';
import {
  dict,
  UserPageQuery,
  // AddReq,
  DelReq,
  EditReq,
  // useFs,
  // compute,
  // CreateCrudOptionsProps,
  CreateCrudOptionsRet
} from '@fast-crud/fast-crud';
import { dictionary } from '/@/utils/dictionary';
// import { successMessage } from '/@/utils/message';
import { auth } from "/@/utils/authFunction";
import { modelsDataDict } from '../../shardDict/shard-dict';

// const opop = OPlog.createCrudOptions
// const { OPLogCrudBinding, OPLogcrudRef, OPLogcrudExpose } = useFs({ opop });


export const createCrudOptions = function (): CreateCrudOptionsRet {
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  // const addRequest = async ({ form }: AddReq) => {
  //   return await api.AddObj(form);
  // };

  return {
    crudOptions: {
      request: {
        pageRequest,
        // addRequest,
        editRequest,
        delRequest,
      },
      actionbar: {
        buttons: {
          add: {
            show: false,
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 200,
        buttons: {
          view: {
            show: false,
          },
          edit: {
            iconRight: 'Edit',
            type: 'text',
            show: auth('area:Update')
          },
          remove: {
            iconRight: 'Delete',
            type: 'text',
            show: auth('area:Delete')
          },
        },
      },
      pagination: {
        show: true,
      },
      table: {
        rowKey: 'id',
      },
      columns: {
        _index: {
          title: '序号',
          form: { show: false },
          column: {
            type: 'index',
            align: 'center',
            width: '70px',
            columnSetDisabled: true, //禁止在列设置中选择
          },
        },
        row_origin: {
          title: "数据来源",
          search: { show: true },
          type: "dict-select",
          dict: modelsDataDict,
          form: {
						rules: [
							// 表单校验规则
							{
								required: true,
								message: '必填项',
							},
						],
						component: {
							span: 12,
							showSearch: true,
							filterable: true,
							//默认的filterOption仅支持value的过滤，label并不会加入查询
							//所以需要自定义filterOption
							filterOption(inputValue: any, option: { label: string | any[]; value: string | any[]; }) {
								return option.label.indexOf(inputValue) >= 0 || option.value.indexOf(inputValue) >= 0;
							}
						},
					},
        },
        row_id: {
          title: "资源ID",
          search: { show: true },
          type: "table-select",
          dict: dict({
            value: "id",
            label: "name",
          }),
          form: {
            component: {
              multiple: false,
              rowKey: "id", //element-plus 必传
              createCrudOptions: operationLogCreateCrudOptions,
              crudOptionsOverride: {
                rowHandle: {
                  fixed: "right"
                }
              }
            }
          }
        },
        user: {
          title: '用户ID',
          search: {
            show: true,
          },
          type: 'input',
          column: {
            minWidth: 90,
          },
          form: {
            rules: [
              // 表单校验规则
              {required: true, message: '用户ID必填项'},
            ],
            component: {
              placeholder: '请输入用户ID',
            }
          },
        },
        is_external: {
          title: '外部资源',
          search: {
            show: true,
          },
          type: 'dict-radio',
          dict: dict({
            data: dictionary('button_whether_bool', undefined)
          }),
          form: {
            rules: [
              // 表单校验规则
              // {required: false, message: '用户ID必填项'},
            ],
            component: {
              placeholder: '请输入资源描述',
            }
          },
        },
      },
    },
  };
};
