<template>
  <div>
    <div style="margin-bottom: 12px;">
      <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image
          :src="ticketImg" alt="订单图标" style="width: 24px; height: 16px;"></tiny-image>当前订单</span>
      <tiny-tag type="success" size="medium"> {{ currentSelectTicket }} </tiny-tag>
    </div>
    <tiny-grid ref="selectTicketGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium"
      :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent"
      @radio-change="handleRadioChange" :resizable="true">
      <template #toolbar>
        <div style="margin: 10px;">
          <tiny-button type="primary" @click="handleClearRadioRowData" size="medium">取消关联订单</tiny-button>
        </div>
        
      </template>
      <tiny-grid-column type="radio" width="40"></tiny-grid-column>
      <tiny-grid-column field="name" title="ID/名称" align="left" width="180" :sortable="true" :filter="nameFilter">
        <template #default="{ row }">
          <div class="id-cell">
            <tiny-link :underline="false" type="primary">{{ row.ticket_id.slice(0, 32) }}</tiny-link>
            <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.id)"></tiny-link>
          </div>
          <p>{{ row.name }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="creator_name" title="创建人" align="center" :sortable="true">
        <template #default="{ row }">
          {{ row.creator_name }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="ticket_id" title="工单ID" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="current_status" title="状态" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="is_finished" title="结束" align="center" :sortable="true">
        <template #default="{ row }">
          {{ row.is_finished ? '是' : '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="create_datetime" title="创建时间" align="center" :sortable="true">
        <template #default="{ row }">
          {{ formatNow(row.create_datetime) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectTicketForm">
import { ref, reactive, toRefs, watch, } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyTag,
  TinyImage,
  TinyInput,
  TinyPager,
  Modal as TinyModal,
} from '@opentiny/vue';
import { GetList } from '/@/api/ticket/ticket';
import { iconCopy } from '@opentiny/vue-icon';
import { formatNow } from '/@/utils/formatTime';
import { copyText } from '/@/utils/copyText';
import ticketImg from '/@/assets/img/ticket.svg';

const props = defineProps({
  currentSelectTicketId: {
    type: String,
    required: false,
    default: '',
  }
});

// 当前选中值
let currentSelectTicket = ref<string>('--');
const emit = defineEmits(['update:currentSelectTicketId']);

const TinyIconCopy = iconCopy();

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input,base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})




// 初始化请求数据
interface FilterOptions {
  id: string;
  name: string;
  ticket_id: string;
  current_status: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    id: props.currentSelectTicketId,
    name: '',
    ticket_id: '',
    current_status: '',
  },
});
let tableData = ref([]);

const selectTicketGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
const reloadGrid = () => {
  selectTicketGrid.value?.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };

  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;

    // 如果有初始选中的 ID，设置默认选中行
    if (props.currentSelectTicketId) {
      // @ts-ignore
      const selectedRow = tableData.value.find(row => row.id === props.currentSelectTicketId);
      if (selectedRow) {
        selectTicketGrid.value?.setRadioRow(selectedRow);
        // @ts-ignore
        currentSelectTicket.value = selectedRow.name;
      }
    }

    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    id: props.currentSelectTicketId,
    name: '',
    ticket_id: '',
    current_status: '',
  };
  // reloadGrid();
}

const handleClearRadioRowData = () => {
  TinyModal.confirm('您确定要取消关联工单吗？点击“确认”按钮后，请记得点击下方的确定按钮').then((code: string) => {
    if (code === 'confirm') {
      selectTicketGrid.value.clearRadioRow();
    currentSelectTicket.value = '--';
    emit('update:currentSelectTicketId', '');
    }
    
  })
}

const handleRadioChange = () => {
  let selectedRow = selectTicketGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectTicket.value = `${selectedRow.name}`
    emit('update:currentSelectTicketId', selectedRow.id)
  }

}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any) => {
  // if (filters)
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
    filterOptions.value.name = filters.filters.name.value.text;
  }
  reloadGrid();
};

watch(() => props.currentSelectTicketId, (newTicketId:any) => {
  filterOptions.value.id = newTicketId;
  reloadGrid();
  if (newTicketId) {
    // @ts-ignore
    const selectedRow = tableData.value.find(row => row.id === props.currentSelectTicketId);
    if (selectedRow) {
        selectTicketGrid.value?.setRadioRow(selectedRow);
        // @ts-ignore
        currentSelectTicket.value = selectedRow.name;
      }
  } else {
    currentSelectTicket.value = '--';
  }
  
}, {immediate: true});

</script>
<style lang="less" scoped>
.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    /* 根据需要调整宽度 */
  }
}
</style>
