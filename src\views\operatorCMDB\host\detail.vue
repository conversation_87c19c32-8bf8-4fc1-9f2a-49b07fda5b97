<template>
	<fs-page>
		<div class="container-content">
			<div class="detail-base-cards">
				<el-descriptions :column="3" size="large">
					<template #title>
						<div class="id-wrapper">
							<span>实例ID：{{ item.instance_id }}</span>

							<el-link
								:underline="false"
								type="primary"
								:icon="DocumentCopy"
								@click="copyText(item.instance_id)"
							></el-link>
							<span style="padding-left: 20px; padding-right:20px;">|</span>
							<el-link type="primary" @click="router.go(-1)">返回</el-link>
						</div>
					</template>
					<template #extra v-if="item.is_maintenance">
						<div class="toolbar-container">
							<el-link
								:underline="false"
								type="primary"
								target="_blank"
								:href="`http://${item.is_maintenance}`"
								:icon="Monitor"
							>&nbsp;BMC控制台</el-link>
							<el-dropdown>
								<span class="el-dropdown-link">
									操作
									<el-icon class="el-icon--right">
										<arrow-down />
									</el-icon>
								</span>
								<template #dropdown>
									<el-dropdown-menu>
										<el-dropdown-item :disabled="true">关闭电源</el-dropdown-item>
										<el-dropdown-item :disabled="true">软关机</el-dropdown-item>
										<el-dropdown-item :disabled="true">移动 acvtive</el-dropdown-item>
										<el-dropdown-item :disabled="true">移动 manageable</el-dropdown-item>
									</el-dropdown-menu>
								</template>
							</el-dropdown>
						</div>
					</template>
					<el-descriptions-item
						label="主机名称"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						{{
						item.name }}
					</el-descriptions-item>
					<el-descriptions-item
						label="主机类型"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						{{
						item.host_type }}
					</el-descriptions-item>

					<el-descriptions-item
						label="机房ID"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						<el-tag type="primary">{{ item.machine_room }}</el-tag>
					</el-descriptions-item>
					<el-descriptions-item
						label="包间ID"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						<el-tag type="primary">{{ item.private_room }}</el-tag>
					</el-descriptions-item>
					<el-descriptions-item
						label="机柜ID"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						<el-tag type="primary">{{ item.idc_rack_machine }}</el-tag>
					</el-descriptions-item>
					<el-descriptions-item
						label="U位"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>{{ item.u_position }}</el-descriptions-item>
					<el-descriptions-item
						label="物理机ID"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						<el-tag type="primary">{{ item.physical_server_machine }}</el-tag>
					</el-descriptions-item>
					<el-descriptions-item
						label="客户ID"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						<el-tag type="primary">{{ item.customer }}</el-tag>
					</el-descriptions-item>
					<el-descriptions-item
						label="主机状态"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>{{ item.host_status }}</el-descriptions-item>
					<el-descriptions-item
						label="Buffer池"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>{{ item.is_buffer ? '是' : '否'}}</el-descriptions-item>
					<el-descriptions-item
						label="启动时间"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>{{ item.start_time }}</el-descriptions-item>
					<el-descriptions-item
						label="到期时间"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>{{ item.expire_time }}</el-descriptions-item>
					<el-descriptions-item
						label="区域"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>{{ item.area }}</el-descriptions-item>
					<el-descriptions-item
						label="资源分类"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>
						<el-tag type="primary">{{ item.resource_category }}</el-tag>
					</el-descriptions-item>
					<el-descriptions-item
						label="描述"
						label-class-name="desc-customer-label"
						class-name="desc-customer-content"
					>{{ item.description }}</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>
		<div class="container-content">
			<div class="detail-base-cards">
				<tiny-layout :cols="24">
					<tiny-row :gutter="40">
						<!--- 左侧信息区---->
						<tiny-col :span="12">
							<tiny-card title="配置状态" class="detial-sub-cards" :auto-width="true">
								<tiny-layout :cols="24">
									<!-- <tiny-row :gutter="20">
										<tiny-col :span="8">云主机/裸金属ID</tiny-col>
										<tiny-col :span="16">
											<el-link
												type="primary"
												:underline="false"
												@click="toOPServerDetail(item.instance_id)"
												:disabled="item.instance_id ? false : true"
											>&nbsp;{{ item.instance_id || '-' }}</el-link>
										</tiny-col>
									</tiny-row>-->
									<tiny-row :gutter="20">
										<tiny-col :span="8">内网IP</tiny-col>
										<tiny-col :span="16">{{ item.ip_private || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">公网IP</tiny-col>
										<tiny-col :span="16">{{ item.ip_public || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">BMC IP</tiny-col>
										<tiny-col :span="16">{{ item.is_maintenance || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">宿主机IP</tiny-col>
										<tiny-col :span="16">{{ item.ip_bmc || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">操作系统</tiny-col>
										<tiny-col :span="16">{{ item.os || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">CPU(核)</tiny-col>
										<tiny-col :span="16">{{ item.cpu || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">内存(G)</tiny-col>
										<tiny-col :span="16">{{ item.mem || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">系统盘(G)</tiny-col>
										<tiny-col :span="16">{{ item.sys_disk || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">数据盘(G)</tiny-col>
										<tiny-col :span="16">{{ item.data_disk || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">GPU型号</tiny-col>
										<tiny-col :span="16">{{ item.gpu_model || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">GPU数量</tiny-col>
										<tiny-col :span="16">{{ item.gpu_count || '-' }}</tiny-col>
									</tiny-row>
								</tiny-layout>
							</tiny-card>
						</tiny-col>
						<!--- 右侧信息区 ---->
						<tiny-col :span="12">
							<tiny-card title="登录信息" class="detial-sub-cards" :auto-width="true">
								<tiny-layout :cols="24">
									<tiny-row :gutter="20">
										<tiny-col :span="8">BMC_用户</tiny-col>
										<tiny-col :span="16">{{ item.bmc_user || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">BMC_密码</tiny-col>
										<tiny-col :span="16">
											{{displayPassword}}
											<el-icon @click="togglePassword" style="margin-left: 10px; cursor: pointer;">
												<component v-if="displayPassword != '-'" :is="showPassword ? View : Hide" />
											</el-icon>
										</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">SSH端口</tiny-col>
										<tiny-col :span="16">{{ item.ssh_port || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">SSH用户</tiny-col>
										<tiny-col :span="16">{{ item.ssh_user || '-' }}</tiny-col>
									</tiny-row>
									<tiny-row :gutter="20">
										<tiny-col :span="8">SSH密码</tiny-col>
										<tiny-col :span="16">
											{{displaySSHPW}}
											<el-icon @click="handleSSHPW" style="margin-left: 10px; cursor: pointer;">
												<component v-if="displaySSHPW != '-'" :is="showSSHPW ? View : Hide" />
											</el-icon>
										</tiny-col>
									</tiny-row>
								</tiny-layout>
							</tiny-card>
						</tiny-col>
					</tiny-row>
				</tiny-layout>
			</div>
		</div>
	</fs-page>
</template>

<script setup>
import { ref,reactive,onMounted,toRefs,computed } from 'vue';
import {
  TinyTabs,
  TinyTabItem,
} from '@opentiny/vue';
import { GetObj } from '/@/api/operatorCMDB/host';
import { useRouter } from 'vue-router';
import { DocumentCopy,View,Hide } from '@element-plus/icons-vue';
import { copyText } from '/@/utils/copyText';
import { toStringJSON } from 'xe-utils';

const router = useRouter();
const data = reactive({
  item: {}
})

// 实例ID
const instance_id = router.currentRoute.value.params.id;

const showPassword = ref(false);
const showSSHPW = ref(false);
const togglePassword = () => showPassword.value  = !showPassword.value; 
const handleSSHPW = () => showSSHPW.value  = !showSSHPW.value; 

const displayPassword = computed(() => {
  if (data.item.bmc_passwd  == null || data.item.bmc_passwd  === '') {
    return '-';
  }
  return showPassword.value  ? data.item.bmc_passwd  : '******';
});

const displaySSHPW = computed(() => {
  if (data.item.ssh_passwd  == null || data.item.ssh_passwd  === '') {
    return '-';
  }
  return showSSHPW.value  ? data.item.ssh_passwd  : '******';
});

// 请求数据接口方法
const fetchDetail = async () => {
    const response = await GetObj(instance_id);
    data.item = response.data;
};

const { item } = toRefs(data);

onMounted(async () => {
  await fetchDetail()
});
</script>

<style scoped>
.container-content {
	display: grid;
	grid-template-columns: auto;
	gap: 8px;
	margin: 10px;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
}

.toolbar-container {
	display: flex;
	align-items: center;
	gap: 12px; /* 元素间隔 */
	padding: 8px 16px;
	background: #f5f7fa;
	border-radius: 4px;
}

/* 卡片内容限制 */
.detail-base-cards {
	margin: 16px;
	box-sizing: border-box;
	box-shadow: none;
}

.id-wrapper {
	display: inline-flex;
	align-items: center;
	gap: 10px;
	max-width: 100%;
}

.detial-sub-cards {
	line-height: 2.4;
	margin-bottom: 16px;
}

:deep(.desc-customer-label) {
	color: #909399;
	font-weight: bolder;
}

:deep(.desc-customer-content) {
	color: #000;
}
</style>
