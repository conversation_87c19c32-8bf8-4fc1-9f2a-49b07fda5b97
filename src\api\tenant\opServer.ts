import { request } from '/@/utils/service';
import { UserPageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/tenants/tenant-op-server/';
export function GetList(query: UserPageQuery) {
	return request({
		url: apiPrefix,
		method: 'get',
		params: query,
	});
}
export function GetObj(id: InfoReq|string) {
	return request({
		url: apiPrefix + id + '/',
		method: 'get',
	});
}

export function AddObj(obj: AddReq) {
	return request({
		url: apiPrefix,
		method: 'post',
		data: obj,
	});
}

export function UpdateObj(obj: EditReq) {
	return request({
		url: apiPrefix + obj.id + '/',
		method: 'put',
		data: obj,
	});
}

export function DelObj(id: string) {
	return request({
		url: apiPrefix + id + '/',
		method: 'delete',
		data: { id },
	});
}

export function DelObjs(obj: any) {
	return request({
		url: apiPrefix + 'multiple_delete/',
		method: 'delete',
		data: obj,
	});
}
export function createBaremetalServerObj(obj: AddReq) {
	return request({
		url: '/api/tenants/create_baremetal_server/',
		method: 'post',
		data: obj,
	});
}

export function SoftDeleteOpenstackServer(id: string) {
	return request({
		url: apiPrefix + 'soft_delete_openstack_server/',
		method: 'post',
		data: { 'id': id },
	});
}



export function updateServerNameOrDesc(id: string, obj: any) {
	return request({
		url: apiPrefix + id + '/update_server_name_or_desc/',
		method: 'post',
		data: obj,
	});
}

export function stopServer(id: string) {
	return request({
		url: apiPrefix + id + '/stop_server/',
		method: 'post',
		data: { 'id': id },
	});
}

export function startServer(id: string) {
	return request({
		url: apiPrefix + id + '/start_server/',
		method: 'post',
		data: { 'id': id },
	});
}

export function rebootServer(id: string, obj:any) {
	return request({
		url: apiPrefix + id + '/reboot_server/',
		method: 'post',
		data: obj,
	});
}

export function rebuildServer(id: string, obj: any) {
	return request({
		url: apiPrefix + id + '/rebuild_server/',
		method: 'post',
		data: obj,
	});
}

export function updateServerExpireAt(id: string, obj: any) {
	return request({
		url: apiPrefix + id + '/update_server_expire_at/',
		method: 'post',
		data: obj,
	});
}


export function updateServerToOPCmdbHost(id: string) {
	return request({
		url: apiPrefix + id + '/update_server_to_op_cmdb_host/',
		method: 'post',
	});
}


export function retryOPServerSoftwareInstallTask(id: string) {
	return request({
		url: apiPrefix + id + '/retry_op_server_software_install_task/',
		method: 'post',
	});
}