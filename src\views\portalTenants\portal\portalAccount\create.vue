<template>
  <div id="container-list" class="container-list">
    <div class="contain">
      <div class="contain-head">
        <span>创建租户</span>
        <hr />
      </div>
      <div id="account_config" class="create-step-container">
        <tiny-form ref="createAccountValid" :inline="false" label-position="right" label-width="150px"
          style="border-radius: 0px" :model="createAccounntFormData" :rules="createAccountFormRules">
          <tiny-row :flex="true" :gutter="20" :span="12">
            <tiny-col :span="6">
              <tiny-form-item label="租户名称" prop="account_name">
                <tiny-input placeholder="请输入" v-model="createAccounntFormData.account_name"
                  style="width: 80%; margin-right: 10px" :maxlength="63" show-word-limit></tiny-input>
                <span
                  style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">用于系统内用户自动创建，仅支持英文、数字、特殊字符串仅支持“
                  - _ ”。</span>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="6">
              <tiny-form-item label="租户别称" prop="account_nick_name">
                <tiny-input placeholder="请输入" v-model="createAccounntFormData.account_nick_name"
                  style="width: 80%; margin-right: 10px" :maxlength="63" show-word-limit></tiny-input>
                <span
                  style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">租户简称，未来可登录后，显示的为用户花名信息</span>
              </tiny-form-item>
            </tiny-col>
          </tiny-row>

          <tiny-row :flex="true" :gutter="20" :span="12">
            <tiny-col :span="6">
              <tiny-form-item label="公司名称" prop="company">
                <tiny-input placeholder="请输入" v-model="createAccounntFormData.company"
                  style="width: 80%; margin-right: 10px" :maxlength="63" show-word-limit></tiny-input>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="6">
              <tiny-form-item label="邮箱" prop="email">
                <tiny-input placeholder="请输入" v-model="createAccounntFormData.email"
                  style="width: 80%; margin-right: 10px" :maxlength="63" show-word-limit></tiny-input>
                <span style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">&nbsp;</span>
              </tiny-form-item>
            </tiny-col>
          </tiny-row>
          <tiny-row :flex="true" :gutter="20" :span="12">
            <tiny-col :span="6">
              <tiny-form-item label="手机号" prop="mobile">
                <tiny-input placeholder="请输入" v-model="createAccounntFormData.mobile"
                  style="width: 80%; margin-right: 10px" :maxlength="11" show-word-limit></tiny-input>
                <span style="display: block; color: #8a8e99; border-radius: 0px; font-size: 12px">&nbsp;</span>
              </tiny-form-item>
            </tiny-col>
            <tiny-col :span="6">
              <tiny-form-item label="账号类型" prop="account_type">
                <tiny-select v-model="createAccounntFormData.account_type" :clearable="true" :allow-copy="true">
                  <tiny-option v-for="item in tenantPortalAccountType" :key="item.value" :label="item.label" :value="item.value"> </tiny-option>
                </tiny-select>
              </tiny-form-item>
            </tiny-col>
          </tiny-row>
        </tiny-form>
      </div>
      <div style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin: 10px;">
                  <tiny-button text="确认信息" type="danger" style="max-width: unset;" @click="sendData" v-if="props.isDirectCreate === '0'"></tiny-button>
        <tiny-button text="创建" type="danger" style="max-width: unset;" @click="clickCreate" v-if="props.isDirectCreate === '1'"></tiny-button>
      </div>
    </div>
  </div>
</template>

<script setup name="portalAccountCreate" lang="ts">
import { reactive, ref } from 'vue';
import { Modal as TinyModal, Input as TinyInput, Form as TinyForm, Button as TinyButton, } from '@opentiny/vue';
import { AddObj } from '/@/api/tenant/account';
import { useRouter } from 'vue-router';
import { dictionary } from '/@/utils/dictionary';


const props = defineProps({
  isDirectCreate: {
    type: String,
    required: false,
    default: '1',
  }
});

// 路由
const router = useRouter()

// 枚举列数据获取
const tenantPortalAccountType = dictionary('tenant:account:account_type', undefined)

let createAccounntFormData = ref({
  'account_name': '',
  'account_nick_name': '',
  'company': '',
  'description': '',
  'email': '',
  'mobile': '',
  'account_type': '',
});

const emit = defineEmits(['update:configedAccountInfo']);

// 点击创建按钮
const clickToServerList = () => {
  router.push(`/tenant/producter_and_services/elasti-computes/baremetal`)
}

// 项目数据选择验证
const createAccountValid = ref();

// 用户名验证函数
let validateUsername = (_rule:any, value:any, callback:any) => {
  // 验证用户名是否只包含英文、数字以及特殊字符“-”和“_”
  if (!/^[a-zA-Z0-9-_]+$/.test(value)) {
    callback(new Error('用户名只能包含英文、数字以及特殊字符“-”和“_”。'));
  } else {
    callback();
  }
};

let validateMobile = (rule: any, value: string, callback: any) => {
      if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入有效的手机号码'));
      } else {
        callback();
      }
    };

const createAccountFormRules = reactive({
  account_name: [
    {
      required: true, message: '请输入租户名称', trigger: 'blur'
    },
    {
      validator: validateUsername, trigger: 'blur'
    }
  ],
  account_nick_name: [
    { required: true, message: '请输入租户花名', trigger: 'blur' }
  ],
  company: [
    { required: true, message: '请输入公司名称', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validateMobile, trigger: 'blur' }
  ],
  description: [
    { max: 255, message: '最大255个字符', trigger: 'blur' }
  ],
});
const sendData = () => {
  const valid = createAccountValid.value.validate();
      if (!valid) {
        return
      }
  emit('update:configedAccountInfo', createAccounntFormData);
}
const clickCreate = async () => {
  const validations = [
    { form: createAccountValid, },
  ];

  for (const { form, } of validations) {
    try {
      const valid = await form.value.validate();
      if (!valid) {
        return
      }
    } catch (error) {
      // 处理验证失败的情况
      return; // 停止后续验证
    }
  }
  // 所有验证通过后，调用 createBaremetalServerObj 函数
  try {
    console.error(createAccounntFormData.value)
    const response = await AddObj(createAccounntFormData.value);
    console.error(response)
    TinyModal.message({
      message: response.msg,
      status: 'success',
    });
  } catch (error) {
    TinyModal.message({
      message: '创建失败',
      status: 'error',
    });
  }

};
</script>
<style scoped lang="less">
.new-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
  padding: 8px;
}

.fixed-header {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-server-steps {
  display: flex;
  align-items: center;
  /* 垂直居中 */
  justify-content: center;
  /* 水平居中 */
  margin: 20px;
}

.create-step-container {
  margin: 20px;
}

.container-step-head {
  font-size: 24px;
  font-weight: bolder;
  margin-bottom: 24px;
}

.content {
  margin-top: 150px;
  /* 确保内容不会被步骤条覆盖 */
  overflow-y: auto;
  /* 允许内容区域垂直滚动 */
}

.section {
  height: 100vh;
  /* 每个部分的高度为视口高度 */
  background-color: #f0f0f0;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.container-list {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 20px);
  overflow-x: hidden;
  overflow-y: auto;
}

/* Tiny Vue 组件搜索栏修改圆角 */
:deep(.tiny-input__inner) {
  border-radius: 6px;
  /* 设置圆角大小 */
}

:deep(.tiny-button) {
  border-radius: 16px;
  /* 设置圆角大小 */
}

.line {
  height: 1px;
  color: rgb(213, 213, 213);
  background-color: rgb(213, 213, 213);
  border: 0;
}

.contain {
  flex: 1 1 auto;
  margin: 8px 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px 8px rgba(43, 44, 45, 0.05);

  .contain-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 10px 0 10px;

    hr {
      .line();

      width: 92%;
      margin: 0 20px;
    }

    span {
      color: #1a1818;
      font-size: 16px;
    }
  }

  .contain-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 52px;
  }

  .contain-img {
    position: relative;
    display: flex;
    cursor: pointer;

    img:hover {
      background: whitesmoke;
    }
  }

  .contain-text {
    padding-left: 10px;
    color: #4e5969;
    font-size: 14px;
    cursor: pointer;
  }
}

.bottom-line {
  padding-bottom: 8px;

  hr {
    .line();

    width: 96%;
    margin: 0 auto;
  }
}

.filter-form {
  margin: 10px 0;
}

.col {
  width: 96%;
}

:deep(.tiny-grid) {

  &-header__column,
  &-body__column {

    &.col__selection,
    &.col__radio {
      padding: 0 8px 0 8px;

      &+th,
      +td {
        padding-left: 0;
      }
    }
  }
}

:deep(.tiny-pager) {
  float: right;
}

:deep(.tiny-grid-button__wrapper) {
  width: 100%;
}

:deep(.tiny-form-item__label) {
  color: #494747;
  font-weight: normal;
}

:deep(.tiny-grid-header__column) {
  height: 35px;
  color: rgb(139, 137, 137);
  background-color: #f5f6f7;
}

.operation {
  color: #5e7ce0;
}

.btn {
  display: flex;
  width: 100%;

  .screen {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    text-align: center;
    cursor: pointer;

    span {
      margin-left: 10px;
      color: #4e5969;
      font-size: 14px;
    }
  }
}

.btn>div:last-child {
  margin-left: auto;
}

.tiny-fullscreen {
  background: #fff;
}

.tiny-fullscreen-wrapper {
  min-height: 710px;
  padding: 0 30px;
}

.tiny-fullscreen-scroll {
  overflow-y: auto;
}

.search-btn {
  display: flex;

  button {
    height: 34px;
  }
}

.id-cell {
  display: flex;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
    /* 根据需要调整宽度 */
  }
}

.status-success {
  fill: #52c41a;
  margin-right: 8px;
}

.status-danger {
  fill: #C7000B;
  margin-right: 8px;
}

.status-warning {
  fill: #FA9841;
  margin-right: 8px;
}
</style>