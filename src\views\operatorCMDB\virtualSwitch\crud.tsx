import * as api from '/@/api/operatorCMDB/virtualSwitch';
import {
	UserPageQuery,
	AddReq,
	DelReq,
	EditReq,
	CreateCrudOptionsProps,
	CreateCrudOptionsRet,
	dict,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { dictionary } from '/@/utils/dictionary';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, h } from 'vue';
import { useRouter } from 'vue-router';
import { GetObj as getPhysicalSwitchDetail } from '/@/views/resource/physicalAsset/networkHardware/api';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
	const router = useRouter();
	const pageRequest = async (query: UserPageQuery) => {
		return await api.GetList(query);
	};
	const editRequest = async ({ form, row }: EditReq) => {
		form.id = row.id;
		return await api.UpdateObj(form);
	};
	const delRequest = async ({ row }: DelReq) => {
		return await api.DelObj(row.id);
	};
	const addRequest = async ({ form }: AddReq) => {
		return await api.AddObj(form);
	};

	let selectedIds = ref([]);
	const onSelectionChange = (changed: any) => {
		selectedIds.value = changed.map((item: any) => item.id);
	};

	return {
		selectedIds,
		crudOptions: {
			...commentCurdCustomSettings,
			request: {
				pageRequest,
				addRequest,
				editRequest,
				delRequest,
			},
			actionbar: {
				buttons: {
					add: {
						show: auth('operatorcmdb:virtualSwitch:Create'),
						plain: true,
						type: 'primary',
					},
					selectionsDeleted: {
						text: '批量删除',
						type: 'danger',
						plain: true,
						show: auth('operatorcmdb:virtualSwitch:MultipleDelete'),
						click: (): void => {
							if (selectedIds.value.length === 0) {
								ElMessage.warning('请先勾选')
								return
							}
							ElMessageBox.confirm(
								h('p', null, [
									h('span', null, '确定删除 '),
									h('i', { style: 'color: red' }, selectedIds.value.length),
									h('span', null, ' 个记录吗？'),
								]),
								{
									confirmButtonText: '确定',
									cancelButtonText: '取消',
									type: 'warning',
								}
							)
								.then(() => {
									api.DelObjs({ 'keys': selectedIds.value }).then(
										(response: any) => {
											if (response.code === 2000 && response.msg === '删除成功') {
												ElMessage.success('删除成功')
											} else {
												ElMessage.error('删除失败')
											}
										}
									)
								})
								.catch(() => {
									ElMessage({
										type: 'info',
										message: '取消删除',
									})
								})
						},
					}
				},
			},
			rowHandle: {
				fixed: 'right',
				width: 280,
				buttons: {
					view: {
						show: auth('operatorcmdb:virtualSwitch:Retrieve'),
						type: 'primary',
						link: true,
					},
					edit: {
						link: true,
						type: 'primary',
						show: auth('operatorcmdb:virtualSwitch:Update'),
					},
					remove: {
						link: true,
						type: 'danger',
						show: auth('operatorcmdb:virtualSwitch:Delete'),
					},
					sync: {
						text: '同步',
						type: 'success',
						link: true,
						show: auth('operatorcmdb:virtualSwitch:SyncSwitchData'),
						click(context) {
							ElMessageBox.confirm('确定要同步该交换机信息吗？', '提示', {
								confirmButtonText: '确定',
								cancelButtonText: '取消',
								type: 'warning',
							}).then(() => {
								api.SyncSwitchData({ id: context.row.id }).then((response: any) => {
									if (response.code === 2000) {
										crudExpose.doRefresh()
                    ElMessage.success('同步成功')
									} else {
										ElMessage.error('同步失败')
									}
								})
							})
						}
					},
					// viewLog: {
					// 	type: 'primary',
					// 	text: '查看日志',
					// 	link: true,
					// 	show: auth('system:auditLog:GetResourceLogs'),
					// 	click(context) {
					// 		router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
					// 	}
					// },
				},
			},
			pagination: {
				show: true,
				'default-page-size': 10,
				'default-current': 1,
			},
			table: {
				rowKey: 'id',
				onSelectionChange,
			},
			form: {
				labelWidth: 120,
				row: { gutter: 20 },
			},
			columns: {
				$checked: {
					title: "选择",
					form: { show: false },
					column: {
						type: "selection",
						align: "center",
						width: "55px",
					}
				},
				_index: {
					title: '序号',
					form: { show: false },
					column: {
						type: 'index',
						align: 'center',
						width: '70px',
					},
				},
				name: {
					title: '交换机名称',
					search: {
						show: true,
					},
					type: 'input',
					column: {
						minWidth: 150,
						showOverflowTooltip: true,
						sortable: 'custom',
					},
					form: {
						rules: [
							{ required: true, message: '交换机名称必填项' },
							{ max: 255, min: 1, message: '最大: 255, 最小: 1', trigger: 'blur' }
						],
						component: {
							placeholder: '请输入交换机名称',
						},
					},
				},
				physical_switch: {
					title: '物理交换机',
					search: {
						show: true,
					},
					column: {
						minWidth: 180,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/resource/physical_asset/network_hardware/get_list_by_ids/?is_all=true',
						value: 'id',
						label: 'physical_machine_sn',
						cache: false,
					}),
					form: {
						valueChange: {
							handle({ value, key, form }) {
								console.log('physical_switch valueChange:', key, value, form);
								const physicalSwitchId = value;
								if (physicalSwitchId) {
									// 发起请求获取物理交换机详情
									getPhysicalSwitchDetail(physicalSwitchId).then((res: any) => {
										const switchInfo = res.data;
										console.log('物理交换机详情:', switchInfo);

										// 自动填充相关字段
										if (switchInfo.machine_room) {
											form.machine_room = switchInfo.machine_room;
										}
										if (switchInfo.private_room) {
											form.private_room = switchInfo.private_room;
										}
										if (switchInfo.idc_rack_machine) {
											form.idc_rack_machine = switchInfo.idc_rack_machine;
										}
										if (switchInfo.rack_unit) {
											form.rack_unit = switchInfo.rack_unit;
										}
										if (switchInfo.vendor) {
											form.vendor = switchInfo.vendor;
										}
										if (switchInfo.physical_machine_sn) {
											form.sn = switchInfo.physical_machine_sn;
										}
										if (switchInfo.management_ip) {
											form.management_ip = switchInfo.management_ip;
										}
										if (switchInfo.management_username) {
											form.management_username = switchInfo.management_username;
										}
										if (switchInfo.management_password) {
											form.management_password = switchInfo.management_password;
										}
										if (switchInfo.management_port) {
											form.management_port = switchInfo.management_port;
										}
										if (switchInfo.management_protocol) {
											form.management_protocol = switchInfo.management_protocol;
										}

										// 如果虚拟交换机名称为空，可以基于物理交换机名称生成
										if (!form.name && switchInfo.name) {
											form.name = `VS-${switchInfo.name}`;
										}
									}).catch((error: any) => {
										console.error('获取物理交换机详情失败:', error);
										ElMessage.error('获取物理交换机详情失败');
									});
								}
							},
						},
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							placeholder: '请选择物理交换机',
						},
					},
				},
				machine_room: {
					title: '机房',
					search: {
						show: true,
					},
					column: {
						minWidth: 150,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/resource/machine_room/get_list_by_ids/?is_all=true',
						value: 'id',
						label: 'name',
						cache: false,
					}),
					form: {
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							placeholder: '请选择机房',
						},
					},
				},
				private_room: {
					title: '包间',
					search: {
						show: true,
					},
					column: {
						minWidth: 150,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/resource/private_room/get_list_by_ids/?is_all=true',
						value: 'id',
						label: 'name',
						cache: false,
					}),
					form: {
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							placeholder: '请选择包间',
						},
					},
				},
				idc_rack_machine: {
					title: '机柜',
					search: {
						show: true,
					},
					column: {
						minWidth: 150,
					},
					type: 'dict-select',
					dict: dict({
						url: '/api/resource/idc_rack_machine/get_list_by_ids/?is_all=true',
						value: 'id',
						label: 'rack_sn',
						cache: false,
					}),
					form: {
						component: {
							name: 'fs-dict-select',
							filterable: true,
							clearable: true,
							placeholder: '请选择机柜',
						},
					},
				},
				rack_unit: {
					title: 'U位',
					search: {
						show: false,
					},
					column: {
						minWidth: 80,
					},
					type: 'number',
					form: {
						rules: [
							{ required: true, message: 'U位必填项' },
						],
						component: {
							placeholder: '请输入U位',
						},
					},
				},
				node: {
					title: '节点',
					search: {
						show: true,
					},
					column: {
						minWidth: 100,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:host:area_node', undefined)
					}),
					form: {
						component: {
							placeholder: '请选择地域节点',
						},
					},
				},
				sn: {
					title: '序列号',
					search: {
						show: true,
					},
					column: {
						minWidth: 150,
						showOverflowTooltip: true,
					},
					type: 'input',
					form: {
						rules: [
							{ max: 255, message: '最大长度: 255', trigger: 'blur' }
						],
						component: {
							placeholder: '请输入序列号',
						},
					},
				},
				management_ip: {
					title: '管理IP',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'input',
					form: {
						rules: [
							{ max: 255, message: '最大长度: 255', trigger: 'blur' }
						],
						component: {
							placeholder: '请输入管理IP',
						},
					},
				},
				management_username: {
					title: '管理用户名',
					search: {
						show: false,
					},
					column: {
						minWidth: 120,
						show: false,
					},
					type: 'input',
					form: {
						rules: [
							{ max: 32, message: '最大长度: 32', trigger: 'blur' }
						],
						component: {
							placeholder: '请输入管理用户名',
						},
					},
				},
				management_password: {
					title: '管理密码',
					search: {
						show: false,
					},
					column: {
						show: false,
					},
					type: 'password',
					form: {
						rules: [
							{ max: 255, message: '最大长度: 255', trigger: 'blur' }
						],
						component: {
							placeholder: '请输入管理密码',
						},
					},
				},
				management_port: {
					title: '管理端口',
					search: {
						show: false,
					},
					column: {
						minWidth: 100,
						show: false,
					},
					type: 'number',
					form: {
						component: {
							placeholder: '请输入管理端口',
						},
					},
				},
				management_protocol: {
					title: '管理协议',
					search: {
						show: false,
					},
					column: {
						minWidth: 100,
						show: false,
					},
					type: 'dict-select',
					dict: dict({
						data: dictionary('operator_cmdb:virtualSwitch:management_protocol', undefined)
					}),
					form: {
						component: {
							placeholder: '请选择管理协议',
						},
					},
				},
				vendor: {
					title: '厂商',
					search: {
						show: true,
					},
					column: {
						minWidth: 120,
					},
					type: 'input',
					form: {
						rules: [
							{ max: 255, message: '最大长度: 255', trigger: 'blur' }
						],
						component: {
							placeholder: '请输入厂商',
						},
					},
				},
				firmware_version: {
					title: '固件版本',
					search: {
						show: false,
					},
					column: {
						minWidth: 150,
						showOverflowTooltip: true,
						show: false,
					},
					type: 'textarea',
					form: {
						component: {
							placeholder: '请输入固件版本',
						},
					},
				},
				vlan_range: {
					title: 'VLAN范围',
					search: {
						show: false,
					},
					column: {
						minWidth: 150,
						showOverflowTooltip: true,
					},
					type: 'textarea',
					form: {
						component: {
							placeholder: '请输入VLAN范围，如：1-100,200-300',
						},
					},
				},
				last_sync_at: {
					title: '最后同步时间',
					search: {
						show: false,
					},
					column: {
						minWidth: 150,
						sortable: 'custom',
					},
					type: 'datetime',
					form: {
						show: false,
					},
				},
				description: {
					title: '描述',
					search: {
						show: true,
					},
					type: 'textarea',
					column: {
						minWidth: 200,
						showOverflowTooltip: true,
					},
					form: {
						component: {
							placeholder: '请输入描述',
						},
					},
				},
			},
		},
	};
};
