<template>
  <tiny-card class="todo-card">
    <header class="card-header">
      <h2 class="card-title">待办事项</h2>
    </header>
    
    <div class="table-container">
      <el-table 
        :data="tableData" 
        style="width: 100%"
        @row-click="handleRowClick"
        :row-class-name="tableRowClassName"
        size="small"
        scrollbar-always-on
        height="230"
      >
        <el-table-column 
          prop="operatorcmdb_host" 
          label="主机" 
          min-width="100"
        >
          <template #header>
              <el-input
                v-model="searchForm.host"
                placeholder="搜索主机"
                size="small"
                style="width: 100px;"
                @input="handleSearch"
              />
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="issue_type" 
          label="问题类型" 
          sortable
          min-width="80"
        />
        
        <el-table-column 
          prop="issue_status" 
          label="问题状态" 
          sortable
          min-width="80"
        >
          <template #default="scope">
            <el-tag 
              :type="getStatusType(scope.row.issue_status)"
              size="small"
            >
              {{ scope.row.issue_status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column 
          prop="customer" 
          label="客户" 
          min-width="120"
        >
          <template #header>
              <el-input
                v-model="searchForm.customer"
                placeholder="搜索客户"
                size="small"
                style="width: 100px;"
                @input="handleSearch"
              />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </tiny-card>
</template>

<script setup lang="ts">
import { TinyCard } from '@opentiny/vue'
import { PropType, ref, computed } from 'vue'

interface MaintenanceData {
  id: string;
  operatorcmdb_host: string;
  issue_type: string;
  issue_status: string;
  customer: string;
  create_datetime: string;
}

interface Props {
  data: MaintenanceData[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  rowClick: [row: MaintenanceData]
}>();

// 搜索表单
const searchForm = ref({
  host: '',
  customer: ''
});

// 过滤后的表格数据
const tableData = computed(() => {
  return props.data.filter(item => {
    const hostMatch = !searchForm.value.host || 
      item.operatorcmdb_host.toLowerCase().includes(searchForm.value.host.toLowerCase());
    const customerMatch = !searchForm.value.customer || 
      item.customer.toLowerCase().includes(searchForm.value.customer.toLowerCase());
    return hostMatch && customerMatch;
  });
});

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在 computed 中处理
};

// 处理行点击
const handleRowClick = (row: MaintenanceData) => {
  emit('rowClick', row);
};

// 表格行样式
const tableRowClassName = ({ row }: { row: MaintenanceData }) => {
  return 'clickable-row';
};

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已上报':
      return 'warning';
    case '进行中':
      return 'primary';
    case '已解决':
      return 'success';
    default:
      return 'info';
  }
};
</script>

<style scoped>
.todo-card {
  width: calc(100% - var(--card-margin) * 2);
  margin: var(--card-margin);
  padding: var(--card-padding);
  height: 320px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.card-header {
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.table-container {
  width: 100%;
}

/* 表格行样式 */
:deep(.clickable-row) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.clickable-row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .todo-card {
    --card-padding: 16px;
    --card-margin: 8px;
  }
  
  .card-header {
    margin-bottom: 12px;
  }
}
</style> 