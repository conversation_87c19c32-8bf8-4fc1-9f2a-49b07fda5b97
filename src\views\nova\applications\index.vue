<template>
  <fs-page>
    <el-row class="mx-2">

      <!-- 树形列表 -->
      <el-col :span="5" class="p-1">
        <el-card :body-style="{ height: '100%' }">
          <p class="font-mono font-black text-center text-xl pb-5">
            项目列表
            <el-tooltip effect="dark" :content="content" placement="right">
              <el-icon>
                <QuestionFilled/>
              </el-icon>
            </el-tooltip>
          </p>
          <el-input v-model="filterText" :placeholder="placeholder"/>
          <el-tree ref="treeRef" class="font-mono font-bold leading-6 text-7xl" :data="data" :props="treeProps"
                   :filter-node-method="filterNode" icon="ArrowRightBold" :indent="38" highlight-current @node-click="onTreeNodeClick">
            <template #default="{ node, data }">
              <element-tree-line :node="node" :showLabelLine="false" :indent="32">
					<span v-if="data.status" class="text-center font-black font-normal">
						<SvgIcon name="iconfont icon-shouye" color="var(--el-color-primary)"/>&nbsp;{{ node.label }}
					</span>
                <span v-else color="var(--el-color-primary)"> <SvgIcon name="iconfont icon-shouye"/>&nbsp;{{
                    node.label
                  }} </span>
              </element-tree-line>
            </template>
          </el-tree>
        </el-card>
      </el-col>

      <!-- 数据表格 -->
      <el-col :span="19" class="p-1">
        <el-card :body-style="{ height: '100%' }">

          <!--  功能说明:使用导入组件，并且修改api地址为当前对应的api，当前是demo的api="api/ApplicationsModelViewSet/"-->
          <fs-crud ref="crudRef" v-bind="crudBinding">
            <template #actionbar-right>
              <importExcel api="api/ApplicationsModelViewSet/" v-auth="'user:Import'">导入</importExcel>
            </template>
          </fs-crud>

        </el-card>
      </el-col>
    </el-row>

  </fs-page>
</template>

<script lang="ts" setup name="apps">
import {useExpose, useCrud} from '@fast-crud/fast-crud';
import createCrudOptions  from './crud';
import { GetAllPrj } from '/@/views/nova/projects/api';
import {ElTree} from 'element-plus';
import {ref, onMounted, watch, toRaw, h} from 'vue';
import XEUtils from 'xe-utils';
import {getElementLabelLine} from 'element-tree-line';
import importExcel from '/@/components/importExcel/index.vue';


const ElementTreeLine = getElementLabelLine(h);

interface Tree {
  id: number;
  name: string;
  status: boolean;
  children?: Tree[];
}

interface APIResponseData {
  code?: number;
  data: [];
  msg?: string;
}

let userId = ref();
let userName = ref('Unknown');


// 引入组件
const placeholder = ref('请输入项目名称');
const filterText = ref('');
const treeRef = ref<InstanceType<typeof ElTree>>();

const treeProps = {
  children: 'children',
  label: 'name',
  icon: 'icon',
};

watch(filterText, (val) => {
  treeRef.value!.filter(val);
});

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  console.error(toRaw(data).name.indexOf(value))
  return toRaw(data).name.indexOf(value) !== -1;
};

let data = ref([]);

const content = '选择项目，查看所包含的所有应用'

const getData = () => {
  GetAllPrj({}).then((ret: APIResponseData) => {
    const responseData = ret.data;
    const result = XEUtils.toArrayTree(responseData, {
      parentKey: 'parent',
      children: 'children',
      strict: true,
    });

    data.value = result;
  });
};


//树形点击事件
const onTreeNodeClick = (node: any) => {
  const {id} = node;
  crudExpose.doSearch({form: {project: id}});
};

// 页面打开后获取列表数据
onMounted(() => {
  getData();
});

// crud组件的ref
const crudRef = ref();
// crud 配置的ref
const crudBinding = ref();
// 暴露的方法
const {crudExpose} = useExpose({crudRef, crudBinding});
// 你的crud配置
const {crudOptions} = createCrudOptions({crudExpose});
// 初始化crud配置
// eslint-disable-next-line no-unused-vars
const {resetCrudOptions} = useCrud({crudExpose, crudOptions});

// 页面打开后获取列表数据
onMounted(() => {
  crudExpose.doRefresh();
});
</script>

<style lang="scss" scoped>
.el-row {
  height: 100%;

  .el-col {
    height: 100%;
  }
}

.el-card {
  height: 100%;
}

.font-normal {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
}
</style>
