
import { request } from '/@/utils/service';  // 换成service中的request方法，请求token格式为"JWT Tokenxxxx"

const BASE_URL = "/api/op/server";

class ServerAPI {
  /**
   * 获取服务器分页列表
   *
   * @param queryParams 查询参数
   * @returns 服务器分页结果
   */
  static getPage(queryParams: ServerPageQuery) {
    return request({
      url: `${BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  }

  /**
   * 获取服务器表单数据
   *
   * @param id 服务器ID
   * @returns 服务器表单数据
   */
  static getFormData(id: number) {
    return request({
      url: `${BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 新增服务器
   *
   * @param data 服务器表单数据
   */
  static add(data: ServerForm) {
    return request({
      url: `${BASE_URL}/`,
      method: "post",
      data: data,
    });
  }

  /**
   * 修改服务器
   *
   * @param id 服务器ID
   * @param data 服务器表单数据
   */
  static update(id: number, data: ServerForm) {
    return request({
      url: `${BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  }

  /**
   * 删除服务器
   *
   * @param ids 服务器ID，多个以英文逗号(,)分隔
   */
  static deleteByIds(ids: string) {
    return request({
      url: `${BASE_URL}/${ids}`,
      method: "delete",
    });
  }

  /**
   * 获取服务器列表
   *
   * @returns 服务器列表
   */
  static getList() {
    return request({
      url: `${BASE_URL}/list`,
      method: "get",
    });
  }

  /**
   * 获取服务器的数据项
   *
   * @param typeCode 服务器编码
   * @returns 服务器数据项
   */
  static getOptions(code: string) {
    return request({
      url: `${BASE_URL}/${code}/options`,
      method: "get",
    });
  }
}

export default ServerAPI;

/**
 * 服务器查询参数
 */
export interface ServerPageQuery extends PageQuery {
  search?: string;    // keywords
}

/**
 * 分页查询参数
 */
interface PageQuery {
  page: number;
  limit: number;
}


/**
 * 服务器分页对象
 */
export interface ServerPageVO {
  /**
   * 服务器ID
   */
  id: number;
  /**
   * 服务器名称
   */
  name: string;
  /**
   * 服务器编码
   */
  code: string;
  /**
   * 服务器状态（1-启用，0-禁用）
   */
  status: number;
  /**
   * 服务器项列表
   */
  volumeItems: VolumeItem[];
}

/**
 * Volume项
 */
export interface VolumeItem {
  id?: number;
  name?: string;
  size?: number;
  type?: string;
}

/**
 * 安全组List元素
 */
export interface SecurityGroup {
  id?: string;
  name?: string;
}

export interface ServerForm {
  insid?: number;
  name?: string;
  image_name?: string;
  volume_size?: number;
  flavor_name?: string;
  key_name?: string;
  netname?: string;
  compute_host?: string;
  volumeItems?: VolumeItem[];
  security_groups?: number[];
}
