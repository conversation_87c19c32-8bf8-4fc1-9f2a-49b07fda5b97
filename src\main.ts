import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import { directive } from '/@/directive/index';
import { i18n } from '/@/i18n';
import other from '/@/utils/other';
import '/@/assets/style/tailwind.css'; // 先引入tailwind css, 以免element-plus冲突
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import '/@/theme/index.scss';
import mitt from 'mitt';
import VueGridLayout from 'vue-grid-layout';
import piniaPersist from 'pinia-plugin-persist';
// @ts-ignore
import fastCrud from './settings.ts';
import pinia from './stores';
import {RegisterPermission} from '/@/plugin/permission/index';
import {
  FsExtendsCopyable, FsExtendsJson,
} from "@fast-crud/fast-extends";
// @ts-ignore
import eIconPicker, { iconList, analyzingIconForIconfont } from 'e-icon-picker';
import 'e-icon-picker/icon/default-icon/symbol.js'; //基本彩色图标库
import 'e-icon-picker/index.css'; // 基本样式，包含基本图标
import 'font-awesome/css/font-awesome.min.css';
import elementPlus from 'e-icon-picker/icon/ele/element-plus.js'; //element-plus的图标
import fontAwesome470 from 'e-icon-picker/icon/fontawesome/font-awesome.v4.7.0.js'; //fontAwesome470的图标
import eIconList from 'e-icon-picker/icon/default-icon/eIconList.js';
import iconfont from '/@/assets/iconfont/iconfont.json'; //引入json文件
import '/@/assets/iconfont/iconfont.css'; //引入css
// 自动注册插件
import { scanAndInstallPlugins } from '/@/views/plugins/index';
import VXETable from 'vxe-table';
import '/@/assets/global.css';
import 'vxe-table/lib/style.css';
import '@xterm/xterm/css/xterm.css';

import '/@/assets/style/reset.scss';
// import 'element-tree-line/dist/style.css';

import CardCustomLayout from '/@/layout/component/cardCustomLayout.vue';

import {ElTable, ElTableColumn} from 'element-plus';

// [Importent] 解决[Violation]Added non-passive event listener to a scroll-blocking 'wheel' event. Consider marking event handler as 'passive' to make the page more responsive. 警告
// yarn add -D default-passive-events -S 安装依赖 "default-passive-events": "^2.0.0".
import 'default-passive-events' // 解决 vue3 wheel 告警

// [Importent] 解决 Unable to preventDefault inside passive event listener invocation 浏览器发出的警告
// 原因分析: "Unable to preventDefault inside passive event listener invocation"是浏览器开发中的一个警告信息。
// 这个警告通常出现在使用passive事件监听器时，当在事件处理函数中调用preventDefault()方法时会引发该警告
import '/@/utils/browserPatch'
let forIconfont = analyzingIconForIconfont(iconfont); //解析class
iconList.addIcon(forIconfont.list); // 添加iconfont dvadmin3的icon
iconList.addIcon(elementPlus); // 添加element plus的图标
iconList.addIcon(fontAwesome470); // 添加fontAwesome 470版本的图标

// 获取组件的props
const TableProps = ElTable.props
const TableColumnProps = ElTableColumn.props

// 修改默认props
// 全局el-table设置
TableProps.border = { type: Boolean, default: false } // 边框线
TableProps.stripe = {
  type: Boolean,
  default: true,
}
// TableProps.headerCellStyle = {
//   type: Object,
//   default: {
//     'background-color': '#111111'
//   }
// } // 表头表格内css

// 全局el-table-column设置
TableColumnProps.align = { type: String, default: 'center' } // 居中
TableColumnProps.showOverflowTooltip = { type: Boolean, default: true } // 文本溢出

let app = createApp(App);

scanAndInstallPlugins(app);

app.use(eIconPicker, {
	addIconList: eIconList, //全局添加图标
	removeIconList: [], //全局删除图标
	zIndex: 3100, //选择器弹层的最低层,全局配置
});

pinia.use(piniaPersist);
directive(app);
other.elSvg(app);

// 自定义卡片布局
app.component('CardCustomLayout', CardCustomLayout)
app.use(VXETable)
// @ts-ignore
app.use(pinia)
    .use(router)
    // @ts-ignore
    .use(ElementPlus, { i18n: i18n.global.t })
    .use(i18n)
    .use(VueGridLayout)
    .use(fastCrud, undefined)
    .use(FsExtendsCopyable)
    .use(FsExtendsJson)
    .mount('#app');

app.config.globalProperties.mittBus = mitt();
