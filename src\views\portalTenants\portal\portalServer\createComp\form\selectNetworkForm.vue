<template>
  <div>
    <div style="margin-bottom: 12px;">
              <span style="width: 150px; display: inline-block; font-weight: bolder; padding-left: 6px;"><tiny-image :src="networkImg" alt="网络图标" style="width: 24px; height: 16px;"></tiny-image>当前网络</span>
              <tiny-tag type="success" size="medium"> {{ currentSelectNetwork }} </tiny-tag>
              
            </div>
    <tiny-grid ref="selectNetworkGrid" :auto-load="true" :fetch-data="fetchDataOption" :loading="loading" size="medium" :auto-resize="true" :pager="pagerConfig" remote-filter @filter-change="filterChangeEvent" @radio-change="handleRadioChange" :resizable="true">
      <tiny-grid-column type="radio" width="40"></tiny-grid-column>
      <tiny-grid-column field="name" title="ID/名称" align="left" width="180" :sortable="true" :filter="nameFilter">
        <template #default="{row}">
          <div class="id-cell">
              <tiny-link :underline="false" type="primary">{{ row.network_id.slice(0, 8) }}</tiny-link>
              <tiny-link :underline="false" type="primary" :icon="TinyIconCopy" @click="copyText(row.network_id)"></tiny-link>
          </div>
          <p>{{ row.name }}</p>
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="status" title="状态" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.status === 'ACTIVE' ? '正常': '不可用' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="is_shared" title="共享" align="center" :sortable="true">
        <template #default="{row}">
          {{ row.is_shared ? '是': '否' }}
        </template>
      </tiny-grid-column>
      <tiny-grid-column field="node" title="区域" align="center" :sortable="true">
      </tiny-grid-column>
      <tiny-grid-column field="sync_time" title="同步时间" align="center" :sortable="true">
        <template #default="{row}">
          {{ formatNow(row.sync_time) }}
        </template>
      </tiny-grid-column>
    </tiny-grid>
  </div>
</template>

<script setup lang="ts" name="selectNetworkForm">
import { ref, reactive, toRefs, watch } from 'vue';
import {
  TinyGrid,
  TinyGridColumn,
  TinyTag,
  TinyImage,
  TinyInput,
  TinyPager,
} from '@opentiny/vue';
import { GetList } from '/@/api/tenant/opNetwork';
import { iconCopy } from '@opentiny/vue-icon';
import { formatNow } from '/@/utils/formatTime';
import { copyText } from '/@/utils/copyText';
import networkImg from '/@/assets/img/network.svg';

const props = defineProps({
  projectId: {
    type: String,
    required: true,
    default: ''
  },
  currentSelectNetworkId: {
    type: String,
    required: false,
    default: '',
  },
  node: {
    type: String,
    required: false,
    default: '',
  }
});
// 当前选中值
let currentSelectNetwork = ref<string>('--');
const emit = defineEmits(['update:currentSelectNetworkId']);

const TinyIconCopy = iconCopy();

const nameFilter = ref({
  // layout: 'input,enum,default,extends,base',
  layout: 'input, base',
  inputFilter: {
    component: TinyInput,
    relations: [
      { label: '包含', value: 'contains' },
    ]
  }
})


// 初始化请求数据
interface FilterOptions {
  network_id: string;
  status: string;
  is_shared: string;
  name: string;
  project_id: string;
  node: string;
}

const pagerConfig = ref({
  component: TinyPager,
  attrs: {
    currentPage: 1,
    pageSize: 5,
    pageSizes: [5, 10],
    total: 5,
    layout: 'total, prev, pager, next, jumper, sizes',
  },
});

// 加载效果
const state = reactive<{
  loading: boolean;
  filterOptions: FilterOptions;
}>({
  loading: false,
  filterOptions: {
    status: '',
    network_id: '',
    is_shared: '',
    name: '',
    project_id: props.projectId,
    node: props.node,
  },
});
let tableData = ref<Array<FilterOptions>>([]);

const selectNetworkGrid = ref();
const { loading, filterOptions } = toRefs(state);
// form的button
function reloadGrid() {
  selectNetworkGrid?.value?.handleFetch('reload');
  // fetchData();
}
// 请求数据接口方法
const fetchData = async (params: Record<string, any> = {}) => {
  const { ...rest } = filterOptions.value;
  const queryParmas = {
    ...rest,
    ...params,
  };
  
  state.loading = true;
  try {
    const response = await GetList(queryParmas);
    const { data, total } = response;
    tableData.value = data;

    // 如果有初始选中的 ID，设置默认选中行
    if (props.currentSelectNetworkId) {
      const selectedRow = tableData.value.find(row => row.network_id === props.currentSelectNetworkId);
      if (selectedRow) {
        selectNetworkGrid.value?.setRadioRow(selectedRow);
        currentSelectNetwork.value = selectedRow.name;
      }
    }
    if (total === 1) {
      const selectedRow = tableData.value[0];
      if (selectedRow) {
        selectNetworkGrid.value?.setRadioRow(selectedRow);
        currentSelectNetwork.value = selectedRow.name;
      }
      emit('update:currentSelectNetworkId', selectedRow.network_id);
    } else if (total === 0) {
      currentSelectNetwork.value = '未查询到项目网络信息，请联系管理员！！！';
      emit('update:currentSelectNetworkId', '');
    }
    
    return {
      result: data,
      page: { total },
    };
  } finally {
    state.loading = false;
  }
};

const fetchDataOption = reactive({
  api: ({ page }: any) => {
    const { currentPage, pageSize } = page;
    return fetchData({
      page: currentPage,
      limit: pageSize,
    });
  },
  // 重新加载数据配置
  reloadConfig: {
    // 在重新加载数据时，保持表格过滤
    filter: true
  },
});

const handleFormReset = () => {
  state.filterOptions = {
    status: '',
    network_id: '',
    is_shared: '',
    name: '',
    project_id: '',
    node: props.node,
  };
  // reloadGrid();
}
const handleRadioChange = () => {
  let selectedRow = selectNetworkGrid.value.getRadioRow()
  if (selectedRow) {
    currentSelectNetwork.value = `${selectedRow.name}`
    emit('update:currentSelectNetworkId', selectedRow.network_id)
  }
  
}

// 检查对象是否为空
const isObjectEmpty = (obj: Record<string, any>): boolean => {
  return Object.keys(obj).length === 0;
};

const filterChangeEvent = (filters: any)  => {
  // if (filters)
  if (isObjectEmpty(filters.filters)) {
    handleFormReset();  
  }
  // 更新 filterOptions
  if (filters.filters.name && filters.filters.name.type === 'input') {
      filterOptions.value.name = filters.filters.name.value.text;
    }
  reloadGrid();
}


watch(() => props.projectId, (newId, oldId) => {
  if (newId) {
    filterOptions.value.project_id = newId;
    reloadGrid();
  }
}, {immediate: true});

// 监听 node 变化 
watch(() => props.node,  (newNode, oldNode) => {
  if (newNode && newNode !== oldNode) {
    filterOptions.value.node  = newNode;
    reloadGrid();
  }
});

</script>
<style lang="less" scoped>

.id-cell {
  display: flex;
  justify-content: left;
  justify-items: left;
  gap: 10px;

  .id-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px; /* 根据需要调整宽度 */
  }
}
</style>
