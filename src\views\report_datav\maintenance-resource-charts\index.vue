<template>
  <fs-page>
    <div class="page-header">
      <div class="header-controls">
        <div class="current-date-container">
          <span class="current-date">
            <span class="date-text">维修报表</span>
          </span>
        </div>
        <div class="flex-spacer"></div>
        <div class="button-group">
          <el-button type="primary" size="small" @click="isShowChartDescription = !isShowChartDescription">
            {{ isShowChartDescription ? "隐藏" : "显示" }}详细说明
          </el-button>
          <el-button size="small" type="primary" @click="openSaveDialog">
            保存
          </el-button>
          <el-button type="primary" size="small" @click="exportPDF">
            导出PDF
          </el-button>
        </div>
      </div>
    </div>
    <Export2PDF ref="pdfExporter" :file-name="`维修统计_${currentDate}.pdf`" :scale="1.5" background-color="#F5F5F5">
      <div class="export-content">
        <div class="pdf-header">
          <h2>维修统计报表</h2>
          <div class="pdf-subheader">
            <span class="current-date">
              <span class="date-text">日期：{{ currentDate }}</span>
            </span>
          </div>
        </div>
        <div class="resource-overview-container">
          <ChartDescription :data="descriptions" />
        </div>
        <div class="resource-overview-container">
          <ChartDescription :data="chartDataDescriptions" :config="{ name: '详细说明' }" v-if="isShowChartDescription" />
        </div>
        <div>
          <MaintenanceOverview :data="data.maintenData" />
        </div>
        <div class="resource-overview-container">
          <ChartLine 
            style="flex:1" 
            :data="reliabilityChartData" 
            :config="{ 
              title: '月可靠性统计', 
              subTitle: '所选种类各指标', 
              xColumn: 'time', 
              xColumnName: '月份', 
              yColumnName: '时间(小时)', 
              ySeries: metricSeries, 
              showLabel: false,
              showMax: false,
              showMin: false,
              showSymbol: false,
            }"
          >
            <template #extra>
              <el-select v-model="selectedType" placeholder="选择种类" style="width: 120px">
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </ChartLine>
          <ChartLine 
            style="flex:1" 
            :data="maintenanceFrequencyByMonth" 
            :config="{ 
              title: '月维修频次', 
              subTitle: '每月维修记录数量', 
              xColumn: 'month', 
              xColumnName: '月份', 
              yColumnName: '频次', 
              showMin: false 
            }" 
          />
          <!-- <ChartGauge style="flex:1" :data="resolvedRate" :config="{ title: '已解决问题占比', subTitle: '已解决/全部维修记录', name: '已解决占比' }" /> -->
        </div>
        <div class="resource-overview-container">
          <ChartPie :data="issueTypePieData" :config="{ title: '维修类型分布', subTitle: '各类型维修数量占比' }" />
          <ChartPie :data="issueStatusPieData" :config="{ title: '问题状态分布', subTitle: '各状态维修数量占比' }" />
        </div>
        <div class="resource-overview-container">
          <ChartBar :data="hostTop5" :config="{ title: '主机维修数Top5', subTitle: '维修次数最多的主机', xColumn: 'host', direction: 'horizontal' }" />
          <ChartBar :data="customerTop5" :config="{ title: '客户维修数Top5', subTitle: '维修次数最多的客户', xColumn: 'customer', direction: 'horizontal' }" />
        </div>
      </div>
    </Export2PDF>
    <el-dialog v-model="dialogVisible" title="保存报表" width="600px" :close-on-click-modal="false" @closed="handleDialogClosed">
      <el-form ref="reportForm" :model="addReportData" :rules="addReportFormRules" label-width="120px">
        <el-form-item label="报表名称" prop="name">
          <el-input v-model="addReportData.name" placeholder="请输入报表名称" clearable />
        </el-form-item>
        <el-form-item label="报表分类" prop="category">
          <el-select v-model="addReportData.category" placeholder="请选择分类" style="width: 100%">
            <el-option v-for="item in categoryData" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="isSaving" @click="handleSave">
          确认
        </el-button>
      </template>
    </el-dialog>
  </fs-page>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { GetObj, AddObj, } from '/@/api/report_datav/report';
import ChartLine from '/@/components/chaosCharts/chartLine.vue';
import ChartPie from '/@/components/chaosCharts/chartPie.vue';
import ChartGauge from '/@/components/chaosCharts/chartGauge.vue';
import ChartBar from '/@/components/chaosCharts/chartBar.vue';
import ChartDescription from '/@/components/chaosCharts/chartDescription.vue';
import Export2PDF from '/@/components/export2PDF/index.vue';
import { dictionary } from '/@/utils/dictionary';
import * as api from '/@/api/operatorCMDB/hostMaintenance';
import * as hostApi from '/@/api/operatorCMDB/host';
import * as customerApi from '/@/views/customer/customer/api';
import { useRoute } from 'vue-router';
import MaintenanceOverview from './comp/MaintenanceOverview.vue';

const data = reactive({
  metricsData:<ReliabilityData[]>([]),
  maintenData:<any[]>([])
})
const currentDate = ref('');
const formatDate = (dateString?: string) => {
  const date = dateString ? new Date(dateString) : new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  currentDate.value = `${year}-${month}-${day}`;
};

const isShowChartDescription = ref(false);
const descriptions = [
  '本报表展示了维修相关的统计数据，包括维修频次、类型分布、状态分布等。',
  '数据来源于维修记录。',
];
const chartDataDescriptions = [
  '月可靠性统计：统计不同资源类型的MTTF、MTTR、MTBF指标。',
  'MTTF(平均无故障运行时间)：资源总正常运行时间 / 故障次数。',
  'MTTR(平均故障修复效率)：资源总修复时间 / 故障次数。',
  'MTBF(故障间隔周期)：(资源总正常运行时间 + 资源总修复时间) / 故障次数。',
  '月维修频次：统计每月维修记录数量。',
  '已解决问题占比：所有维修记录中，状态为已解决的占比。',
  '维修类型分布：统计各维修类型的数量和占比。',
  '问题状态分布：统计各问题状态的数量和占比。',
  '主机维修数Top5：维修次数最多的主机。',
  '客户维修数Top5：维修次数最多的客户。',
];

const maintenanceFrequencyByMonth = ref<any[]>([]);
const resolvedRate = ref<number>(0);
const issueTypePieData = ref<any[]>([]);
const issueStatusPieData = ref<any[]>([]);
const hostTop5 = ref<any[]>([]);
const customerTop5 = ref<any[]>([]);

interface ReliabilityData {
  metrics_type: string;
  mttf: number;
  mttr: number;
  mtbf: number;
  time: string;
  total_resource_count: number;
  total_fault_count: number;
  total_downtime: number;
  total_repair_time: number;
  total_runtime: number;
}

// 1. 种类选择器
const typeOptions = computed(() => {
  return Array.from(new Set(data.metricsData.map(item => item.metrics_type))).map(type => ({ label: type, value: type }));
});
const selectedType = ref('');

// 自动选中第一个类型
watch(typeOptions, (options) => {
  if (options.length > 0) {
    if (!options.some(opt => opt.value === selectedType.value)) {
      selectedType.value = options[0].value;
    }
  } else {
    selectedType.value = '';
  }
}, { immediate: true });

// 2. 指标series
const metricSeries = ['MTTR', 'MTTF', 'MTBF'];

// 3. 折线图数据处理
const reliabilityChartData = computed(() => {
  // 只保留选中种类的数据
  const filtered = data.metricsData.filter(item => item.metrics_type === selectedType.value);
  // 按月份排序
  const months = Array.from(new Set(filtered.map(item => item.time))).sort();
  return months.map(month => {
    const found = filtered.find(item => item.time === month);
    return {
      time: month,
      MTTR: found ? found.mttr : null,
      MTTF: found ? found.mttf : null,
      MTBF: found ? found.mtbf : null,
      '设备数量': found ? `${found.total_resource_count}个`: null,
      '故障次数': found ? `${found.total_fault_count}次`: null,
      '总停机时间': found ? found.total_downtime : null,
      '总维修时间': found ? found.total_repair_time : null,
      // total_resource_count: found ? found.total_resource_count : null,
      // total_fault_count: found ? found.total_fault_count : null,
      // total_downtime: found ? found.total_downtime : null,
      // total_repair_time: found ? found.total_repair_time : null,
      // total_runtime: found ? found.total_runtime : null,
    };
  });
});

const pdfExporter = ref();
const exportPDF = () => {
  pdfExporter.value.exportAsPDF();
};

const dialogVisible = ref(false);
const isSaving = ref(false);
const reportForm = ref();
const addReportData = reactive({
  name: '维修统计',
  code: 'maintenance-resource-charts',
  category: '运营',
  source_data: {},
  path_name: 'adminReportMaintenanceDetail',
  selected_data: {},
});
const categoryData = ref(dictionary('report_datav:report:category', undefined));
const addReportFormRules = ref({
  name: [
    { required: true, message: '请输入报表名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在2到255个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择报表分类', trigger: 'change' }
  ],
});
const handleDialogClosed = () => {
  if (reportForm.value) {
    reportForm.value.clearValidate();
  }
};
const openSaveDialog = () => {
  dialogVisible.value = true;
  addReportData.name = addReportData.name + '_' + currentDate.value;
};
const handleSave = async () => {
  reportForm.value.validate(async (valid: any) => {
    if (valid) {
      isSaving.value = true;
      addReportData.source_data = data;
      const response = await AddObj(addReportData);
      if (response.code === 2000) {
        ElMessage.success('报表保存成功');
      } else {
        ElMessage.error(response.message || '保存失败');
      }
      isSaving.value = false;
      dialogVisible.value = false;
    } else {
      ElMessage.warning('请填写完整的报表信息!');
    }
  });
};

const fetchData = async () => {
  try {
    const res = await api.GetList({ limit: 10000 });
    data.maintenData = res.data || [];
    // 1. 收集所有主机和客户id
    const hostIds = Array.from(new Set(data.maintenData.map((item: any) => item.operatorcmdb_host).filter(Boolean)));
    const customerIds = Array.from(new Set(data.maintenData.map((item: any) => item.customer).filter(Boolean)));
    // 2. 批量获取主机和客户名称
    let hostMap: Record<string, string> = {};
    let customerMap: Record<string, string> = {};
    if (hostIds.length > 0) {
      const hostRes = await hostApi.GetListByIds({ is_all: true });
      if (hostRes.data && Array.isArray(hostRes.data)) {
        hostRes.data.forEach((h: any) => {
          hostMap[h.id] = h.name || h.ip_bmc || h.hostname || h.id;
        });
      }
    }
    if (customerIds.length > 0) {
      const customerRes = await customerApi.GetListByIds({ is_all: true });
      if (customerRes.data && Array.isArray(customerRes.data)) {
        customerRes.data.forEach((c: any) => {
          customerMap[c.id] = c.name || c.id;
        });
      }
    }
    // 3. 替换maintenData中的id为名称
    data.maintenData = data.maintenData.map((item: any) => ({
      ...item,
      operatorcmdb_host: hostMap[item.operatorcmdb_host] || item.operatorcmdb_host,
      customer: customerMap[item.customer] || item.customer,
    }));
    // 获取可靠性数据
    const metricsRes = await hostApi.GetMetrics();
    data.metricsData = Array.isArray(metricsRes.data) ? metricsRes.data : [];
    // 设置默认选中类型
    if (typeOptions.value.length > 0 && !selectedType.value) {
      selectedType.value = typeOptions.value[0].value;
    }
    processChartData();
  } catch (e) {
    ElMessage.error('维修数据加载失败');
  }
};

const route = useRoute();
const reportId = ref(route.params.reportId as string | undefined);

const getReportData = async (reportId: string) => {
  try {
    const { data: reportData } = await GetObj(reportId);
    // 直接用报表保存时的原始数据
    data.maintenData = reportData.source_data?.maintenData || [];
    data.metricsData = reportData.source_data?.metricsData || [];
    console.log(data.metricsData)
    // 设置日期（如有）
    formatDate(reportData?.create_datetime);
    processChartData();
  } catch (e) {
    ElMessage.error('报表详情加载失败');
  }
};

// 在 setup 顶部添加类型字典获取
const issueTypeDict = dictionary('operator_cmdb:host_maintenance:issue_type', undefined) || [];
const issueTypeKeys = Array.isArray(issueTypeDict) ? issueTypeDict.map((item: any) => item.label) : [];

function processChartData() {
  const maintenData = data.maintenData;
  const monthMap: Record<string, number> = {};
  maintenData.forEach((item: any) => {
    const date = item.start_resolve_time || item.create_datetime;
    if (!date) return;
    const month = date.slice(0, 7);
    monthMap[month] = (monthMap[month] || 0) + 1;
  });
  maintenanceFrequencyByMonth.value = Object.entries(monthMap)
    .map(([month, value]) => ({ month, 频次: value }))
    .sort((a, b) => (a.month as string).localeCompare(b.month as string));

  const total = maintenData.length;
  const resolved = maintenData.filter((item: any) => item.issue_status === '已解决').length;
  resolvedRate.value = total ? Number(((resolved / total) * 100).toFixed(2)) : 0;

  const typeMap: Record<string, number> = {};
  maintenData.forEach((item: any) => {
    const type = item.issue_type || '未知';
    typeMap[type] = (typeMap[type] || 0) + 1;
  });
  issueTypePieData.value = Object.entries(typeMap).map(([name, value]) => ({ name, value }));

  const statusMap: Record<string, number> = {};
  maintenData.forEach((item: any) => {
    const status = item.issue_status || '未知';
    statusMap[status] = (statusMap[status] || 0) + 1;
  });
  issueStatusPieData.value = Object.entries(statusMap).map(([name, value]) => ({ name, value }));

  // 主机维修Top5宽表
  const hostMap: Record<string, number> = {};
  maintenData.forEach((item: any) => {
    const host = item.operatorcmdb_host || '未知';
    hostMap[host] = (hostMap[host] || 0) + 1;
  });
  // 主机类型分布
  const hostTypeMap: Record<string, Record<string, number>> = {};
  maintenData.forEach((item: any) => {
    const host = item.operatorcmdb_host || '未知';
    const type = item.issue_type || '未知';
    if (!hostTypeMap[host]) hostTypeMap[host] = {};
    hostTypeMap[host][type] = (hostTypeMap[host][type] || 0) + 1;
  });
  hostTop5.value = Object.entries(hostMap)
    .map(([host, value]) => {
      const row: Record<string, any> = { host };
      issueTypeKeys.forEach((type) => {
        row[type] = hostTypeMap[host]?.[type] || 0;
      });
      return row;
    })
    .sort((a, b) => {
      // 按所有类型数量之和排序
      const sumA = issueTypeKeys.reduce((sum, key) => sum + (a[key] || 0), 0);
      const sumB = issueTypeKeys.reduce((sum, key) => sum + (b[key] || 0), 0);
      return sumB - sumA;
    })
    .slice(0, 5);

  // 客户维修Top5宽表
  const customerMap: Record<string, number> = {};
  maintenData.forEach((item: any) => {
    const customer = item.customer || '未知';
    customerMap[customer] = (customerMap[customer] || 0) + 1;
  });
  // 客户类型分布
  const customerTypeMap: Record<string, Record<string, number>> = {};
  maintenData.forEach((item: any) => {
    const customer = item.customer || '未知';
    const type = item.issue_type || '未知';
    if (!customerTypeMap[customer]) customerTypeMap[customer] = {};
    customerTypeMap[customer][type] = (customerTypeMap[customer][type] || 0) + 1;
  });
  customerTop5.value = Object.entries(customerMap)
    .map(([customer, value]) => {
      const row: Record<string, any> = { customer };
      issueTypeKeys.forEach((type) => {
        row[type] = customerTypeMap[customer]?.[type] || 0;
      });
      return row;
    })
    .sort((a, b) => {
      // 按所有类型数量之和排序
      const sumA = issueTypeKeys.reduce((sum, key) => sum + (a[key] || 0), 0);
      const sumB = issueTypeKeys.reduce((sum, key) => sum + (b[key] || 0), 0);
      return sumB - sumA;
    })
    .slice(0, 5);
}

onMounted(() => {
  formatDate();
  if (reportId.value) {
    getReportData(reportId.value);
  } else {
    fetchData();
  }
});

</script>


<style scoped>
.page-header {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  margin: 10px;
  padding: 12px 15px;
}
.header-controls {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
.flex-spacer {
  flex-grow: 1;
}
.button-group {
  display: flex;
  align-items: center;
  margin-left: 10px;
}
.button-group .el-button {
  margin-left: 10px;
  display: flex;
  align-items: center;
}
.current-date-container {
  display: flex;
  align-items: center;
  margin-right: 15px;
  min-width: 160px;
}
.current-date {
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.date-text {
  white-space: nowrap;
}
.export-content {
  margin: 10px;
}
.pdf-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}
.pdf-subheader {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}
.resource-overview-container {
  display: flex;
  gap: 16px;
  margin: 10px 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
  box-sizing: border-box;
}
@media (max-width: 768px) {
  .header-controls {
    flex-wrap: wrap;
  }
  .current-date-container {
    order: 1;
    width: 100%;
    margin: 10px 0;
  }
}
</style> 