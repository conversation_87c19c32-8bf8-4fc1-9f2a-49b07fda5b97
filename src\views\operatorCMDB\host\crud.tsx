import * as api from '/@/api/operatorCMDB/host';
import {
  UserPageQuery,
  AddReq,
  DelReq,
  EditReq,
  CreateCrudOptionsProps,
  CreateCrudOptionsRet,
  dict,
} from '@fast-crud/fast-crud';
import { useRouter } from 'vue-router';
import { auth } from "/@/utils/authFunction";
import { dictionary } from '/@/utils/dictionary';
import { Setting, InfoFilled, Key } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, h } from 'vue';
import { commentCurdCustomSettings } from '/@/commonCrudCustomSettings';
import {useCompute} from '@fast-crud/fast-crud';
import { GetObj as getPhysicalServerDetail } from '/@/api/resource/physicalAsset/physicalServerMachine';
import { fa } from 'element-plus/es/locale';


// eslint-disable-next-line no-unused-vars
export const createCrudOptions = function ({ crudExpose }: CreateCrudOptionsProps): CreateCrudOptionsRet {
  const router = useRouter();
  const pageRequest = async (query: UserPageQuery) => {
    return await api.GetList(query);
  };
  const editRequest = async ({ form, row }: EditReq) => {
    form.id = row.id;
    return await api.UpdateObj(form);
  };
  const delRequest = async ({ row }: DelReq) => {
    return await api.DelObj(row.id);
  };
  const addRequest = async ({ form }: AddReq) => {
    return await api.AddObj(form);
  };

  let selectedIds = ref([]);
  let selectedHosts = ref([]);
  const {compute} = useCompute();
  const onSelectionChange = (changed: any) => {
    selectedIds.value = changed.map((item: any) => item.id);
    selectedHosts.value = changed.map((item: any) => {
      return {
        "id": item.id,
        "ip": item.ip_private,
        "ipmi_ip": item.ip_bmc,
        "ssh_user": item.ssh_user,
        "ssh_password": item.ssh_passwd,
        "ssh_port": item.ssh_port,
        "ipmi_user": item.bmc_user,
        "ipmi_password": item.bmc_passwd,
      }
    });
  };

  return {
    selectedIds,
    selectedHosts,
    crudOptions: {
      ...commentCurdCustomSettings,
      request: {
        pageRequest,
        addRequest,
        editRequest,
        delRequest,
      },
      tabs: {
        show: true,
        name: "host_type",
        type: "card", //tabs类型
        defaultOption: {
          //第一个tab页签显示
          show: true,
          value: "", //点击第一个页签，查询值
          label: "全部" // 第一个页签的名称
        }
      },
      actionbar: {
        buttons: {
          add: {
            show: auth('operatorcmdb:host:Create'),
            plain: true,
            type: 'primary',
          },
          selectionsDeleted: {
            text: '批量删除',
            type: 'danger',
            plain: true,
            show: auth('operatorcmdb:host:MultipleDelete'),
            click: (): void => {
              if (selectedIds.value.length === 0) {
                ElMessage.warning('请先勾选')
                return
              }
              ElMessageBox.confirm(
                h('p', null, [
                  h('span', null, '确定删除 '),
                  h('i', { style: 'color: red' }, selectedIds.value.length),
                  h('span', null, ' 个记录吗？'),
                ]),
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning',
                }
              )
                .then(() => {
                  api.DelObjs({ 'keys': selectedIds.value }).then(
                    (response: any) => {
                      if (response.code === 2000 && response.msg === '删除成功') {
                        ElMessage.success('删除成功')
                        // TODO 刷新列表
                      } else {
                        ElMessage.error('删除失败')
                      }
                    }
                  )
                })
                .catch(() => {
                  ElMessage({
                    type: 'info',
                    message: '取消删除',
                  })
                })
            },
          }
        }
      },
      rowHandle: {
        //固定右侧
        fixed: 'right',
        width: 180,
        buttons: {
          view: {
            show: auth('operatorcmdb:host:Retrieve'),
            type: 'primary',
            link: true,
            click(context) {
              router.push(`/operatorCMDB/host/detail/${context.row.id}`) 
            }
          },
          edit: {
            link: true,
            type: 'primary',
            show: auth('operatorcmdb:host:Update')
          },
          remove: {
            link: true,
            type: 'danger',
            show: auth('operatorcmdb:host:Delete')
          },
          viewLog: {
            type: 'primary',
            text: '查看日志',
            link: true,
            show: auth('system:auditLog:GetResourceLogs'),
            click(context) {
              router.push("/system/resourceChangeRecords/detail?resource_id=" + context.row.id)
            }
          },
        },
      },
      pagination: {
        show: true,
        'default-page-size': 10,
        'default-current': 1,
      },
      table: {
        rowKey: 'id',
        onSelectionChange,
      },
      form: {
        labelWidth: 120,
        row: { gutter: 20 },
        group: {
          type: "collspase",
          accordion: true,
          groups: {
            host_base: {
              slots: {
                // 自定义 header
                title: () => {
                  return (
                    <span style={{ color: 'green' }}>
                      <el-icon><InfoFilled /></el-icon>
                      基础信息
                    </span>
                  );
                }
              },
              columns: [
                'instance_id', 'name', 'description', 'host_type', 'physical_server_machine', 'machine_room', 'private_room', 'idc_rack_machine', 'u_position',
                'customer', 'host_status', 'is_buffer', 'start_time', 'expire_time', 'area', 'resource_category',
              ]
            },
            host_config: {
              slots: {
                // 自定义 header
                title: () => {
                  return (
                    <span style={{ color: 'green' }}>
                      <el-icon><Setting /></el-icon>
                      配置信息
                    </span>
                  );
                }
              },
              columns: [
                'ip_private', 'ip_public', 'ip_bmc',
                'kvm_ip_host', 'os', 'cpu', 'mem', 'sys_disk', 'data_disk', 'gpu_model', 'gpu_count',
              ]
            },
            host_ssh_config: {
              slots: {
                // 自定义 header
                title: () => {
                  return (
                    <span style={{ color: 'green' }}>
                      <el-icon><Key /></el-icon>
                      登录信息
                    </span>
                  );
                }
              },
              columns: ['bmc_user', 'bmc_passwd', 'ssh_port', 'ssh_user', 'ssh_passwd',]
            },
          }
        }
      },
      columns: {
        // _index: {
        //   title: '序号',
        //   form: { show: false },
        //   column: {
        //     type: 'index',
        //     align: 'center',
        //     width: '70px',
        //     // columnSetDisabled: true, //禁止在列设置中选择
        //   },
        // },
        $checked: {
          title: "选择",
          form: { show: false },
          column: {
            type: "selection",
            align: "center",
            width: "55px",
          }
        },
        $expand: {
          title: "",
          form: { show: false },
          column: {
            type: "expand",
            align: "center",
            width: "55px",
            fixed: 'left',
            columnSetDisabled: true //禁止在列设置中选择
          }
        },
        ip_bmc: {
          title: 'BMC IP',
          search: {
            show: true,
          },
          column: {
            minWidth: 160,
            fixed: 'left',
          },
          type: 'copyable',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: 'BMC IP必填项' },
            ],
            component: {
              placeholder: '请输入BMC IP',
            },
            show:compute((context)=>{
                    // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
                    return context.form.host_type !== "虚拟机"
                })
          }
        },
        ip_private: {
          title: '内网IP',
          search: {
            show: true,
          },
          column: {
            minWidth: 120,
            fixed: 'left',
            sortable: 'custom',
          },
          type: 'input',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '内网IP必填项' },
            ],
            component: {
              placeholder: '请输入内网IP',
            }
          }
        },
        host_type: {
          title: '主机类型',
          search: {
            show: true,
          },
          column: {
            minWidth: 100,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('operator_cmdb:host:host_type', undefined)
          }),
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '主机类型必填项' },
            ],
            component: {
              placeholder: '请输入主机类型',
            },
            
          }
        },
        resource_category: {
          title: '资源分类',
          search: {
            show: true,
          },
          column: {
            minWidth: 100,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('operator_cmdb:host:resource_category', undefined)
          }),
          form: {
            value: "裸金属",
            rules: [
              // 表单校验规则
              { required: false, message: '资源分类必填项' },
            ],
            component: {
              placeholder: '请输入资源分类',
            },
            show:compute((context)=>{
                    // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
                    return context.form.host_type !== "虚拟机"
                }),
          }
        },
        area: {
          title: '区域',
          search: {
            show: true,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('operator_cmdb:host:area_node', undefined)
          }),
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '区域必填项' },
            ],
            component: {
              placeholder: '请输入区域',
            }
          }
        },
        start_time: {
          title: '启用时间',
          search: {
            show: false,
          },
          column: {
            minWidth: 120,
            sortable: 'custom',
          },
          type: 'datetime',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '启用时间必填项' },
            ],
            component: {
              placeholder: '请输入启用时间',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
            }
          }
        },
        expire_time: {
          title: '到期时间',
          search: {
            show: false,
          },
          column: {
            minWidth: 120,
            sortable: 'custom',
          },
          type: 'datetime',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '到期时间必填项' },
            ],
            component: {
              placeholder: '请输入到期时间',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
            }
          }
        },
        instance_id: {
          title: '实例ID',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 200,
            showOverflowTooltip: true,
            sortable: 'custom',
          },
          form: {
            rules: [
              // 表单校验规则
              { required: true, message: '主机名称必填项,且为唯一项' },
              { max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' }
            ],
            component: {
              placeholder: '请输入主机名称',
            }
          }
        },
        name: {
          title: '主机名称',
          search: {
            show: true,
          },
          // treeNode: true,
          type: 'input',
          column: {
            minWidth: 200,
            showOverflowTooltip: true,
            sortable: 'custom',
          },
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '主机名称必填项' },
              { max: 63, min: 1, message: '最大: 63, 最小: 1', trigger: 'blur' }
            ],
            component: {
              placeholder: '请输入主机名称',
            }
          }
        },
        is_buffer: {
          title: 'Buffer池',
          search: {
            show: true,
          },
          column: {
            minWidth: 60,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('button_whether_bool', undefined)
          }),
          form: {
            value: false,
            rules: [
              // 表单校验规则
              { required: false, message: 'Buffer池必填项' },
            ],
            component: {
              placeholder: '请选择Buffer池',
            },
            show:compute((context)=>{
                    // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
                    return context.form.host_type !== "虚拟机"
                }),
          }
        },
        is_ipmi_mon: {
          title: 'IPMI监控',
          search: {
            show: true,
          },
          column: {
            minWidth: 100,
            fixed: "right",
            valueChange({form}) {
              //动态onChange方法测试
              api.UpdateObj(form).then(
                (response: any) => {
                  if (response.code === 2000 && response.msg === '更新成功') {
                    if (form.is_ipmi_mon) {
                      ElMessage.success('开启IMPI成功')
                    } else {
                      ElMessage.success('关闭IPMI成功')
                    }
                  }
                  else {
                    ElMessage.error('操作失败')
                  }
                }
              )
            },
            component: {
              name: "el-switch",
              disabled: !auth('operatorcmdb:host:switchIPMIMoniter') && compute((context)=>{
                    // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
                    return context.row.host_type === "虚拟机"
                }),
            },
          },
          form: {
            show: false,
            value: false,
            rules: [
              // 表单校验规则
              { required: false, message: 'IPMI监控必填项' },
            ],
            component: {
              name: "el-switch",
              activeText: "打开",
              inactiveText: "关闭",
              placeholder: '请选择IPMI监控',
            },
          }
        },
        is_system_mon: {
          title: '系统监控',
          search: {
            show: true,
          },
          column: {
            minWidth: 100,
            fixed: 'right',
            // eslint-disable-next-line no-unused-vars
            valueChange({value, key, form}) {
              //动态onChange方法测试
              api.UpdateObj(form).then(
                (response: any) => {
                  if (response.code === 2000 && response.msg === '更新成功') {
                    if (form.is_system_mon) {
                      ElMessage.success('开启系统监控成功')
                    } else {
                      ElMessage.success('关闭系统监控成功')
                    }
                  }
                  else {
                    ElMessage.error('操作失败')
                  }
                }
              )
            },
            component: {
              name: "el-switch",
              disabled: !auth('operatorcmdb:host:switchSystemMoniter'),
              // activeText: "打开",
              // inactiveText: "关闭"
            },
          },
          form: {
            value: false,
            show: false,
            rules: [
              // 表单校验规则
              { required: false, message: '系统监控必填项' },
            ],
            component: {
              name: "el-switch",
              placeholder: '请选择系统监控',
              activeText: "打开",
              inactiveText: "关闭"

            }
          }
        },
        description: {
          title: '描述',
          search: {
            show: true,
          },
          type: 'textarea',
          column: {
            minWidth: 180,
            showOverflowTooltip: true,
          },
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '描述必填项' },
            ],
            component: {
              placeholder: '请输入主机描述',
            }
          }
        },
        machine_room: {
          title: '机房',
          search: {
            show: true,
          },
          column: {
            minWidth: 180,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/machine_room/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            cache: false,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择机房',
            },
          },
        },
        private_room: {
          title: '包间',
          search: {
            show: true,
          },
          column: {
            minWidth: 180,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/private_room/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            cache: false,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择包间',
            },
          },
        },
        idc_rack_machine: {
          title: '机柜',
          search: {
            show: true,
          },
          column: {
            minWidth: 200,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/idc_rack_machine/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'rack_sn',
            cache: false,
          }),
          form: {
            component: {
              
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择机柜',
            },
          },
        },
        u_position: {
          title: 'U位',
          search: {
            show: false,
          },
          column: {
            minWidth: 90,
            show: false,
          },
          type: 'number',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: 'U位必填项' },
            ],
            component: {
              placeholder: '请输入U位',
            },
          }
        },
        physical_server_machine: {
          title: '物理机',
          search: {
            show: true,
          },
          column: {
            minWidth: 200,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/resource/physical_asset/physical_server_machine/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'physical_machine_sn',
            cache: false,
          }),
          form: {
            valueChange: {
                          handle({ value, key, form }) {
                            console.log('physical_switch valueChange:', key, value, form);
                            const physicalServerId = value;
                            if (physicalServerId) {
                              // 发起请求获取物理交换机详情
                              getPhysicalServerDetail(physicalServerId).then((res: any) => {
                                const serverInfo = res.data;
                                console.log('物理交换机详情:', serverInfo);
            
                                // 自动填充相关字段
                                if (serverInfo.machine_room) {
                                  form.machine_room = serverInfo.machine_room;
                                }
                                if (serverInfo.private_room) {
                                  form.private_room = serverInfo.private_room;
                                }
                                if (serverInfo.idc_rack_machine) {
                                  form.idc_rack_machine = serverInfo.idc_rack_machine;
                                }
                                if (serverInfo.rack_unit) {
                                  form.u_position = serverInfo.rack_unit;
                                }
                                if (serverInfo.physical_machine_sn) {
                                  form.sn = serverInfo.physical_machine_sn;
                                }
                                if (serverInfo.physical_machine_sn) {
                                  form.sn = serverInfo.physical_machine_sn;
                                }
            
                                // 如果虚拟交换机名称为空，可以基于物理交换机名称生成
                                if (!form.name && serverInfo.name) {
                                  form.name = `VS-${serverInfo.name}`;
                                }
                              }).catch((error: any) => {
                                console.error('获取物理服务器详情失败:', error);
                                ElMessage.error('获取物理服务器详情失败');
                              });
                            }
                          },
                        },
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                // {required: false, message: '机房必填项'},
              ],
              placeholder: '请选择物理机',
            },
          },
        },
        customer: {
          title: '客户',
          search: {
            show: true,
          },
          column: {
            minWidth: 200,
          },
          type: 'dict-select',
          dict: dict({
            url: '/api/customer/customer/get_list_by_ids/?is_all=true',
            value: 'id',
            label: 'name',
            cache: false,
          }),
          form: {
            component: {
              name: 'fs-dict-select',
              filterable: true,
              clearable: true,
              rules: [
                // 表单校验规则
                { required: false, message: '客户为必填项' },
              ],
              placeholder: '请选择客户',
            }
          },
        },
        ip_public: {
          title: '公网IP',
          search: {
            show: true,
          },
          column: {
            minWidth: 120,
            sortable: 'custom',
          },
          type: 'input',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '公网IP必填项' },
            ],
            component: {
              placeholder: '请输入公网IP',
            }
          }
        },
        kvm_ip_host: {
          title: '宿主机IP',
          search: {
            show: true,
          },
          column: {
            minWidth: 120,
            sortable: 'custom',
          },
          type: 'input',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '宿主机IP必填项' },
            ],
            component: {
              placeholder: '请输入宿主机IP',
            },
            show:compute((context)=>{
                    // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
                    return context.form.host_type === "虚拟机"
                }),
          }
        },
        bmc_user: {
          title: 'BMC用户',
          search: {
            show: true,
          },
          column: {
            minWidth: 120,
          },
          type: 'input',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: 'BMC必填项' },
            ],
            component: {
              placeholder: '请输入BMC用户',
            },
            show:compute((context)=>{
                    // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
                    return context.form.host_type !== "虚拟机"
                })
          }
        },
        bmc_passwd: {
          title: 'BMC密码',
          search: {
            show: false,
          },
          column: {
            minWidth: 60,
            show: false,
          },
          type: 'password',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: 'BMC密码必填项' },
            ],
            component: {
              placeholder: '请输入BMC密码',
            },
            show:compute((context)=>{
                    // 通过动态计算show属性的值，当前表单内的userType值为公司，才显示此字段
                    return context.form.host_type !== "虚拟机"
                }),
          }
        },
        host_status: {
          title: '主机状态',
          search: {
            show: true,
          },
          column: {
            minWidth: 90,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('operator_cmdb:host:host_status', undefined)
          }),
          form: {
            value: '空闲',
            rules: [
              // 表单校验规则
              { required: false, message: '主机状态必填项' },
            ],
            component: {
              placeholder: '请输入主机状态',
            }
          }
        },
        os: {
          title: '操作系统',
          search: {
            show: false,
          },
          column: {
            minWidth: 180,
          },
          type: 'input',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '操作系统必填项' },
            ],
            component: {
              placeholder: '请输入操作系统',
            }
          }
        },
        cpu: {
          title: 'CPU(核)',
          search: {
            show: false,
          },
          column: {
            minWidth: 90,
            show: false,
          },
          type: 'number',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: 'CPU(核)必填项' },
            ],
            component: {
              placeholder: '请输入CPU(核)',
            }
          }
        },
        mem: {
          title: '内存(G)',
          search: {
            show: false,
          },
          column: {
            minWidth: 90,
            sortable: 'custom',
            show: false,
          },
          type: 'number',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '内存(G)必填项' },
            ],
            component: {
              placeholder: '请输入内存(G)',
            }
          }
        },
        sys_disk: {
          title: '系统盘(G)',
          search: {
            show: false,
          },
          column: {
            minWidth: 90,
            sortable: 'custom',
            show: false,
          },
          type: 'number',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '系统盘(G)必填项' },
            ],
            component: {
              placeholder: '请输入系统盘(G)',
            }
          }
        },
        data_disk: {
          title: '数据盘(G)',
          search: {
            show: false,
          },
          column: {
            minWidth: 90,
            sortable: 'custom',
            show: false,
          },
          type: 'number',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: '数据盘(G)必填项' },
            ],
            component: {
              placeholder: '请输入数据盘(G)',
            }
          }
        },
        gpu_model: {
          title: 'GPU型号',
          search: {
            show: false,
          },
          column: {
            minWidth: 180,
            show: false,
          },
          type: 'dict-select',
          dict: dict({
            data: dictionary('operator_cmdb:host:gpu_model', undefined)
          }),
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: 'GPU型号必填项' },
            ],
            component: {
              placeholder: '请输入GPU型号',
            }
          }
        },
        gpu_count: {
          title: 'GPU数量',
          search: {
            show: false,
          },
          column: {
            minWidth: 90,
            show: false,
          },
          type: 'number',
          form: {
            value: 0,
            rules: [
              // 表单校验规则
              { required: false, message: 'GPU数量必填项' },
            ],
            component: {
              placeholder: '请输入GPU数量',
            }
          }
        },
        ssh_port: {
          title: 'SSH端口',
          search: {
            show: false,
          },
          column: {
            minWidth: 90,
            show: false,
          },
          type: 'number',
          form: {
            value: 22,
            rules: [
              // 表单校验规则
              { required: false, message: 'SSH端口必填项' },
            ],
            component: {
              placeholder: '请输入SSH端口',
            }
          }
        },
        ssh_user: {
          title: 'SSH用户',
          search: {
            show: true,
          },
          column: {
            minWidth: 100,
          },
          type: 'input',
          form: {
            value: 'root',
            rules: [
              // 表单校验规则
              { required: false, message: 'SSH用户必填项' },
            ],
            component: {
              placeholder: '请输入SSH用户',
            }
          }
        },
        ssh_passwd: {
          title: 'SSH密码',
          search: {
            show: false,
          },
          column: {
            minWidth: 60,
            show: false,
          },
          type: 'password',
          form: {
            rules: [
              // 表单校验规则
              { required: false, message: 'SSH密码必填项' },
            ],
            component: {
              placeholder: '请输入SSH密码',
            }
          }
        },
        // ************************
      },
    },
  };
};
