<template>
  <fs-page>
    <div class="container-content">
      <Base :detail-instance="detailOPServerData" />
  </div>
  <tiny-tabs v-model="activeTabName" tab-style="card">
    <tiny-tab-item title="详情" name="base">
      <div class="container-content">
        <TabsDetail :detail-instance="detailOPServerData" />
      </div>
    </tiny-tab-item>
    <tiny-tab-item title="公网IP" name="publicIP">
      <publicIPServer :op-server="detailOPServerData.instance_id" :project-id="detailOPServerData.project_id" />
    </tiny-tab-item>
  </tiny-tabs>
  
  </fs-page>
</template>

<script setup lang="ts" name="portalServerDetail">
import { ref, reactive, onMounted, } from 'vue';
import {
  TinyTabs,
  TinyTabItem,
} from '@opentiny/vue';
import { GetObj } from '/@/api/tenant/opServer';

import { useRouter } from 'vue-router';
import type { DetailInstance } from '/@/views/interface/tenant/opServer';
import { detailInstanceExample } from '/@/views/interface/tenant/opServerExample';
import Base from '/@/views/portalTenants/portal/portalServer/detail/components/base.vue';

import TabsDetail from '/@/views/portalTenants/portal/portalServer/detail/tabs/detail.vue';
import publicIPServer from '/@/views/portalTenants/portal/portalServer/detail/tabs/publicIP.vue';

const router = useRouter();

// 实例ID
const instance_id = router.currentRoute.value.params.id;
const activeTabName = ref('base');

// 加载效果
const state = reactive<{
  loading: boolean;
  success: boolean;
}>({
  loading: false,
  success: false,
});
let detailOPServerData = ref<DetailInstance>(detailInstanceExample);

// 请求数据接口方法
const fetchOPServerDetailData = async () => {
  
  state.loading = true;
  try {
    const response = await GetObj(instance_id);
    detailOPServerData.value = response.data;
    state.success = true;
    return {
      result: response,
    };
  } finally {
    state.loading = false;
  }
};


onMounted(async () => {
  try {
    await Promise.all([
      fetchOPServerDetailData()
    ])
  } catch {
    state.loading = false;
    state.success = false;
  }
});

</script>
<style scoped>
.container-content {
  display: grid;
  grid-template-columns:auto;
  gap: 8px;
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 8px 8px rgba(169, 174, 184, 0.05);
}

</style>